#!/bin/bash

# Create directory for combined coverage if it doesn't exist
mkdir -p coverage/combined

# Find all coverage-final.json files and merge them
echo "Finding coverage reports..."
COVERAGE_FILES=$(find ./coverage -name coverage-final.json)

if [ -z "$COVERAGE_FILES" ]; then
  echo "No coverage files found. Run tests with coverage first."
  echo "Try running: pnpm test:coverage"
  exit 1
fi

echo "Found coverage files:"
echo "$COVERAGE_FILES"

echo "Merging coverage reports..."
npx istanbul-merge --out coverage/merged.json $COVERAGE_FILES

# Generate HTML report from the merged data
echo "Generating combined HTML report..."
npx nyc report --reporter=html --reporter=lcov --reporter=json-summary --temp-directory coverage --report-dir ./coverage/combined

# Create a simple summary script
cat > ./coverage/summary.js << 'EOF'
const fs = require('fs');
try {
  const summary = JSON.parse(fs.readFileSync('./coverage/combined/coverage-summary.json'));
  const result = {
    lines: summary.total.lines.pct,
    statements: summary.total.statements.pct,
    functions: summary.total.functions.pct,
    branches: summary.total.branches.pct
  };
  console.log(JSON.stringify(result, null, 2));
  fs.writeFileSync('./coverage/combined/summary.json', JSON.stringify(result, null, 2));
} catch (error) {
  console.error('Error generating summary:', error.message);
}
EOF

# Generate a coverage summary
echo "Generating coverage summary..."
node ./coverage/summary.js

echo "Coverage reports successfully aggregated to ./coverage/combined"
echo "Summary:"
cat ./coverage/combined/summary.json
