{"name": "mass", "version": "1.0.11", "description": "", "main": "index.js", "scripts": {"dev:dashboard": "pnpm --filter @mass/dashboard dev", "dev:admin": "pnpm --filter @mass/admin dev", "dev:shared": "pnpm --filter @mass/shared dev", "build": "pnpm --filter @mass/dashboard build && pnpm --filter @mass/admin build && pnpm --filter @mass/shared build", "build:shared": "pnpm --filter @mass/shared build", "build:dashboard": "pnpm --filter @mass/dashboard build", "preview:dashboard": "pnpm --filter @mass/dashboard preview", "build:admin": "pnpm --filter @mass/admin build", "preview:admin": "pnpm --filter @mass/admin preview", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "coverage:dashboard": "pnpm --filter @mass/dashboard test run --coverage", "coverage:admin": "pnpm --filter @mass/admin test run --coverage", "coverage:shared": "pnpm --filter @mass/shared test run --coverage", "coverage:merge": "vitest run --coverage && vitest coverage report", "coverage:threshold": "vitest run --coverage --coverage.threshold=80", "coverage:aggregate": "./scripts/aggregate-coverage.sh", "coverage:full": "pnpm test:coverage && pnpm coverage:aggregate", "knip": "knip", "postinstall": "pnpm build:shared"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20", "istanbul-merge": "^2.0.0", "jq": "^1.7.2", "knip": "^5.45.0", "nyc": "^15.1.0", "typescript": "^5.8.2", "vitest": "^3.1.4"}, "dependencies": {"autoprefixer": "^10.4.20", "postcss": "^8", "tailwindcss": "3"}, "pnpm": {"ignoredBuiltDependencies": ["@swc/core", "esbuild"]}, "workspaces": ["apps/*", "packages/*"]}