# Mass Frontend

[![Code Coverage](https://img.shields.io/badge/Code%20Coverage-Check%20Report-blue)](./coverage/combined/index.html)
[![Test Status](https://img.shields.io/badge/Tests-Passing-brightgreen)](./)

## Applications

This repository contains the following applications:

- **Dashboard**: Customer-facing application
- **Admin**: Administrative interface
- **Shared**: Common components and utilities

## Development

### Setup

```bash
# Install dependencies
pnpm install

# Start development servers
pnpm dev:dashboard  # For dashboard app
pnpm dev:admin      # For admin app
pnpm dev:shared     # For shared components
```

### Building

```bash
# Build all applications
pnpm build

# Build specific applications
pnpm build:dashboard
pnpm build:admin
```

### Testing

```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run tests in watch mode
pnpm test:watch

# Run tests with UI
pnpm test:ui
```

### Code Coverage

```bash
# Generate coverage for all packages
pnpm test:coverage

# Generate coverage for specific packages
pnpm coverage:dashboard
pnpm coverage:admin
pnpm coverage:shared

# Generate coverage report with thresholds (80%)
pnpm coverage:threshold

# Merge and aggregate all coverage reports
pnpm coverage:aggregate

# Complete end-to-end coverage generation and reporting
pnpm coverage:full
```

After running coverage commands:
- Individual package reports can be found in `coverage/{packageName}/index.html`
- The combined coverage report is available at `coverage/combined/index.html`
- The merged coverage data is available in `coverage/merged.json`

## Deployment

The project uses Kubernetes for deployment. See `deployment.yaml` for the configuration.
