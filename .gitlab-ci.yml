stages:
  - build
  - container-build
  - deploy
  - notify
  - security

build:
  stage: build
  image: node:22
  script:
    - npm install -g corepack
    - corepack enable
    - corepack prepare pnpm@latest --activate
    - pnpm --version
    - pnpm install
    - pnpm run build:dashboard
    - pnpm run build:admin
  artifacts:
    paths:
      - apps/dashboard/dist
      - apps/admin/dist

docker-build:
  stage: container-build
  image: docker:latest
  services:
    - name: docker:dind
      alias: docker
  variables:
    DOCKER_TLS_CERTDIR: ""
  dependencies:
    - build
  before_script:
    - docker info
  script:
    - echo "Pushing to $CI_REGISTRY"
    - echo "$CI_JOB_TOKEN" | docker login -u gitlab-ci-token --password-stdin "$CI_REGISTRY"
    - docker build -t "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-dashboard" . --file Dockerfile.dashboard
    - docker tag "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-dashboard" "$CI_REGISTRY_IMAGE:latest-dashboard"
    - docker push "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-dashboard"
    - docker build -t "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-admin" . --file Dockerfile.admin
    - docker tag "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-admin" "$CI_REGISTRY_IMAGE:latest-admin"
    - docker push "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-admin"

deploy:
  stage: deploy
  image:
    name: bitnami/kubectl:latest
    entrypoint: [""]
  dependencies:
    - docker-build
  script:
    - echo "$KUBECONFIG_DATA" > kubeconfig
    - export KUBECONFIG=$CI_PROJECT_DIR/kubeconfig
    - sed -i.bak "s~FRONTEND_IMAGE_PLACEHOLDER~$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-dashboard~g" deployment.yaml
    - sed -i.bak "s~ADMIN_IMAGE_PLACEHOLDER~$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-admin~g" deployment.yaml
    - sed -i.bak "s~BRANCH_ID_PLACEHOLDER~branch-$(echo $CI_COMMIT_BRANCH | sha1sum | tr -dc a-z0-9 | head -c 16)~g" deployment.yaml
    - sed -i.bak "s~DEF_BRANCH~branch-$(echo $CI_DEFAULT_BRANCH | sha1sum | tr -dc a-z0-9 | head -c 16)~g" deployment.yaml
    - kubectl apply -f deployment.yaml

notify:
  stage: notify
  image: alpine:latest
  dependencies:
    - deploy
  script:
    - apk add --no-cache git sed coreutils bash curl
    - export DEPLOYMENT_URL=$(bash ./scripts/get-deployment-url.sh)
    - echo "$DEPLOYMENT_URL" > deployment_url.txt
    - |
      export DEPLOYMENT_MESSAGE="Updated deployment of $CI_PROJECT_NAME (branch $CI_COMMIT_BRANCH) to $DEPLOYMENT_URL"
      echo "$DEPLOYMENT_MESSAGE"
      export SLACK_MESSAGE="{\"text\":\"$DEPLOYMENT_MESSAGE\"}"
      curl -X POST -H 'Content-type: application/json' --data "$SLACK_MESSAGE" "$SLACK_WEBHOOK_URL"
  artifacts:
    paths:
      - deployment_url.txt

zap_scan:
  stage: security
  image: zaproxy/zap-stable
  dependencies:
    - notify
  script:
    - export DEPLOYMENT_URL=$(cat deployment_url.txt)
    - mkdir -p /zap/wrk/zap-reports
    - echo running zap-full-scan.py -t "$DEPLOYMENT_URL" -r /zap/wrk/zap-reports/zap_report.html -J /zap/wrk/zap-reports/zap_report.json -x /zap/wrk/zap-reports/zap_report.xml -m 5
    - zap-full-scan.py -t "$DEPLOYMENT_URL" -r /zap/wrk/zap-reports/zap_report.html -J /zap/wrk/zap-reports/zap_report.json -x /zap/wrk/zap-reports/zap_report.xml -m 5
    - mkdir -p zap
    - cp -r /zap/wrk/zap-reports/* zap/
  artifacts:
    paths:
      - zap
  allow_failure: false
