{"loading": "Loading...", "errorTitle": "An error occurred", "errorDescription": "An error occurred while processing your request. Please try again.", "errorTryAgain": "Try again", "loginTitle": "<PERSON><PERSON>", "logoutTitle": "Do you want to logout?", "logoutDescription": "When you logout, you will stop receiving notifications on this device and will need to login again with e-Devlet.", "logoutButton": "Logout", "deleteAccTitle": "Do you want to delete your account?", "deleteAccDescription": "When you delete your account, all your data will be permanently deleted. This action cannot be undone and all your account information, subscriptions, notifications and complaints will be completely removed from the system.", "deleteAccButton": "Permanently delete my account", "agreementAccept": "Confirm", "agreementAcceptHint": "Please read the entire agreement", "documentKVKKTitle": "Privacy Policy", "documentAgreementTitle": "User Agreement", "documentManualTitle": "Usage Scenarios", "documentAboutTitle": "About", "delete_action": "Delete", "sidebar": {"search": "Search...", "toggle": "Toggle Sidebar", "groups": {"general": "General", "settings_help": "Settings and Help", "settings_account": "Settings and Account"}, "items": {"subscriptions": "Subscriptions", "notifications": "Notifications", "user_management": "User Management", "document_management": "Document Management", "complaint_management": "Complaint Category Management", "document_management_detail": {"agreement_guidelines": "Agreements and Guides", "faq": "Frequently Asked Questions", "edas_info": "EDAŞ Information"}, "documents": {"title": "Documents", "agreement": "Agreement", "about": "About", "kvkk": "KVKK", "manual": "User Manual"}, "notification_management_detail": {"settings": "Notification Categories", "notification_history": "Notification History"}, "notification_management": "Notification Management", "admin_settings": {"account_settings": "Account <PERSON><PERSON>", "notifications_and_alerts": "Notifications and Alerts", "parameters_settings": "Parameter Settings", "page_settings": "Page Settings", "logs": "<PERSON><PERSON>"}, "complaints": "Complaints and Requests", "settings": "Settings", "profile": "Profile", "account": "Account", "appearance": "Appearance", "notifications_settings": "Notifications", "help_support": "Help and Support", "use_cases": "Use Cases", "faq": "Frequently Asked Questions", "about": "About", "logout": "Log Out", "dashboard": "Dashboard"}}, "table": {"pagination": {"previous": "Previous", "next": "Next"}, "select_all": "Select all", "select_row": "Select row {{row}}"}, "autoFormFields": {"select_file": "Select a file from device", "remove_file": "Remove File", "allowed_file_formats": "Maximum PDF size is {{size}} MB", "allowed_file_formats_multiple": "Maximum file size is {{size}} MB\nAllowed file formats: {{formats}}"}, "common": {"error": "Error!", "clear_filters": "Clear Filters", "save": "Save", "cancel": "Cancel", "delete": "Delete", "delete_all": "Delete All", "edit": "Edit", "add_new": "Add New", "search_placeholder": "Search...", "filter": "Filter", "apply": "Apply", "select_all": "Select All", "yes": "Yes", "no": "No", "date_select": "Select Date", "description_label": "Description", "distribution_company_placeholder": "Select distribution company", "no_regions_found": "No distribution company found", "search_regions": "Search distribution company", "export": "Export"}, "auth": {"common": {"backToLogin": "← Back to login screen", "login": "<PERSON><PERSON>"}, "login": {"title": "Welcome, Log In", "subtitle": "Log in securely to your account with e-Devlet", "eDevletLogin": "Login with e-<PERSON><PERSON>", "errorTitle": "Error!", "blockedTitle": "Too many failed attempts!", "blockedDescription": "Your account has been blocked for 15 minutes. Please try again later.", "email": "Email", "password": "Password", "emailPlaceholder": "Enter your email address", "passwordPlaceholder": "Enter your password", "loginButton": "Log In", "loginLoading": "Logging in..."}, "register": {"kvkkTitle": "Personal Data Protection Information", "kvkkDescription": "Please confirm that you have read the personal data protection text.", "userAgreementTitle": "User Agreement", "userAgreementDescription": "Please confirm that you have read the user agreement.", "readAndApprove": "Before logging in, you must confirm that you have read the <kvkk>Personal Data Protection Information Text</kvkk> and the <userAgreement>User Agreement</userAgreement>.", "approveButton": "I Approve", "scrollToEnd": "View all pages and scroll to the end", "pdfError": "An error occurred while loading the agreement. Please try again."}}, "api": {"errors": {"session_expired": "Your session has expired. Please log in again.", "unauthorized": "You are not authorized for this action.", "not_found": "Requested resource not found.", "server_error": "Server error occurred. Please try again later.", "network_error": "Cannot connect to the server. Please check your internet connection.", "request_error": "An error occurred while sending the request.", "rate_limit_global": "Too many API requests. Request aborted. Please try again later.", "rate_limit_endpoint": "Too many requests to this service. Request aborted. Please try again later.", "rate_limit_server": "Rate limit reached. Request aborted. Try again in {{seconds}} seconds.", "default": "An error occurred."}}, "status": {"canceled": "Canceled", "pending": "Pending", "done": "Completed", "in progress": "In Progress", "rejected": "Rejected", "resolved": "Resolved"}, "help_center": {"about": "About", "faq": "Frequently Asked Questions", "use-cases": "Use Cases"}, "date_picker_placeholder": "Pick a date", "search": "Search...", "select_all": "Select All", "reserve": {"title": "Are you sure?", "description": "When your application is canceled, it is drawn to the archive and cannot be activated again.", "confirm": "Yes, close", "cancel": "No, continue"}, "confirmation": {"delete": {"title": "Are you sure you want to delete?", "description": "Once confirmed, this will be permanently deleted and your access to related services will end."}, "modal_close": {"title": "Are you sure you want to close?", "description": "Unsaved changes will be lost.", "confirm": "Yes, close", "cancel": "No, continue"}}, "empty": {"no_data": "No Data Found", "try_again": "Please try again with different filters"}, "notifications": {"types": {"notification": {"type": "Notification", "title": "Notification"}, "warning": {"type": "Warning", "title": "Unexpected Usage Warning"}, "closed-term-warning": {"type": "Warning", "title": "Closed Term Warning"}, "user-limit-warning": {"type": "Warning", "title": "User Limit Warning"}, "complaint-update": {"type": "Notification", "title": "<PERSON><PERSON><PERSON>t Update"}, "planned-outage": {"type": "Notification", "title": "Planned Outage Notification"}, "unplanned-outage": {"type": "Notification", "title": "Unplanned Outage Notification"}}}, "app_title": "MASS Web User Portal", "pick_date": "Pick a date", "start_typing_to_search": "Start typing to search...", "max_files_exceeded": "Maximum {{maxCount}} files can be uploaded.", "go_back": "Go Back"}