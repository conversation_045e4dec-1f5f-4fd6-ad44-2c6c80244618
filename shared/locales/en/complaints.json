{"page": {"title": "Complaints & Requests", "description": "You can start managing your complaints and requests by adding a new application."}, "tabs": {"all": "All Requests", "active": "Active", "resolved": "Resolved", "old": "Archived"}, "search_placeholder": "Search...", "date_select": "Select Date", "filter": "Filter", "clear_filters": "Clear Filters", "empty": {"title": "You have no application yet.", "description": "It will be displayed here when you create a new application", "past_description": "You have no application yet. It will be displayed here when you create a new application", "button": "Create Request"}, "columns": {"application_id": "Application ID", "request_type": "Application Category", "status": "Status", "application_date": "Application Date", "response_date": "Response Date"}, "table": {"select_all": "Select all", "select_row": "Select row"}, "restore": "Cancel", "create_modal": {"title": "Submit New Complaint or Request", "description": "Please provide details about your issue so we can address it promptly."}, "delete_confirmation": "Are you sure you want to delete this request?", "delete_description": "Once you confirm, your request will be permanently deleted.", "cancel": "Cancel", "delete": "Delete", "electricity_fault": "Electricity Fault", "meter_issues": "Meter Issues", "disconnection_notice": "Disconnection Notice", "illegal_usage": "Illegal Usage", "mass_data_issues": "MASS Data Issues/Information Request", "submitting": "Submitting...", "uploading_file": "Uploading File...", "submit_application": "Submit Application", "application_type": "Application Categorıes", "application_subcategory": "Application Subcategories", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "request": "Request", "response": "Response", "alert": {"pending_title": "Your application is being reviewed.", "pending_description": "It has been submitted to EDAŞ for evaluation. You will be notified when your application is finalized."}, "errors": {"application_type_required": "Application type is required.", "application_category_required": "Application category is required.", "application_description_required": "Application description is required.", "application_description_too_long": "Application description must not exceed 2000 characters.", "subscription_id_required": "Subscription selection is required."}, "files": "Files", "supported_file_types": "Supported file types: PDF, XLSX, DOCX", "file_type_error": "Unsupported file type. Please upload a valid format.", "subscription_not_found": "Subscription not found", "error": {"complaint_not_found": "<PERSON><PERSON><PERSON><PERSON> not found", "load_complaint_failed": "Failed to load complaint details", "upload_missing_data": "Upload succeeded but no upload URL or file ID was returned", "load_subscriptions_failed": "Failed to load subscriptions"}, "toast": {"uploading": "Uploading {{filename}}...", "upload_success": "{{filename}} uploaded successfully", "upload_failed": "File upload failed", "invalid_file_type": "Invalid file type. Please upload a supported format.", "submission_success": "Your application has been submitted successfully", "submission_success_full": "Your application has been saved with ID {{id}} and forwarded to the distribution company {{distributionCompany}}.", "submission_failed": "Failed to submit application"}, "filter_start_date": "Start Date", "filter_end_date": "End Date", "application_category": "Application Category", "all": "All", "no_complaints_description_no_filters": "No complaints found for the selected filters.", "application_id": "Application ID", "application_details": "Application Details", "application_details_description": "Details and information about this complaint or request.", "description": "Description", "download": "Download", "unknown": "Unknown"}