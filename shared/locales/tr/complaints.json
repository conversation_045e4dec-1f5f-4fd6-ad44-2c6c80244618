{"page": {"title": "Şikayetler & Talepler", "description": "Yeni bir başvuru ekleyerek şikayet ve taleplerinizi yönetmeye başlayabilirsiniz."}, "tabs": {"all": "<PERSON><PERSON><PERSON>", "active": "Aktif", "resolved": "Çözüldü", "old": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "search_placeholder": "Ara...", "date_select": "<PERSON><PERSON><PERSON>", "filter": "Filtrele", "clear_filters": "<PERSON><PERSON><PERSON><PERSON>", "empty": {"title": "<PERSON><PERSON><PERSON><PERSON> hiç ba<PERSON><PERSON><PERSON>uz bulunmuyor.", "past_title": "Geçmiş Başvuru Bulunamadı.", "description": "Yeni başvuru oluşturduğunuzda burada görüntülenecektir.", "past_description": "<PERSON><PERSON><PERSON><PERSON> hiç başvurunuz bulunmuyor. <PERSON><PERSON> başvuru oluşturduğunuzda burada görüntülenecektir", "button": "Başvuru Oluştur"}, "columns": {"application_id": "Başvuru No", "request_type": "Başvuru Kategorisi", "status": "Durum", "application_date": "Başvuru <PERSON>", "response_date": "<PERSON><PERSON><PERSON>"}, "table": {"select_all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "select_row": "Satırı seç"}, "restore": "İptal Et", "create_modal": {"title": "Yeni Şikayet veya Talep Oluştur", "description": "Sorununuzun detaylarını <PERSON>şın, en kısa sürede ilgilenelim."}, "delete_confirmation": "Bu talebi silmek istediğinize emin misin<PERSON>?", "delete_description": "Onayladığınızda talebiniz kalıcı olarak silinecek.", "cancel": "İptal", "delete": "Sil", "electricity_fault": "Elektrik Arızası", "meter_issues": "<PERSON><PERSON><PERSON>", "disconnection_notice": "<PERSON><PERSON><PERSON>", "illegal_usage": "Kaçak Kullanım", "mass_data_issues": "MASS Veri Sorunları/Bilgi Talebi", "submitting": "Gönderiliyor...", "uploading_file": "<PERSON><PERSON><PERSON>...", "submit_application": "Başvuru <PERSON>", "application_type": "Başvuru <PERSON>", "application_subcategory": "Başvuru Alt Kategorileri", "complaint": "Şikayet", "request": "<PERSON><PERSON>", "response": "Yan<PERSON>t", "alert": {"pending_title": "Başvurunuz inceleniyor.", "pending_description": "EDAŞ'a değerlendirilmek üzere iletildi. Sonuçlandığında bilgilendirileceksiniz."}, "errors": {"application_type_required": "Başvuru türü zorunludur.", "application_category_required": "Başvuru kategorisi zorunludur.", "application_description_required": "Başvuru açıklaması zorunludur.", "application_description_too_long": "Başvuru açıklaması 2000 karakterden uzun olamaz.", "subscription_id_required": "Abonelik seçimi zorunludur."}, "files": "<PERSON><PERSON><PERSON><PERSON>", "supported_file_types": "Desteklenen dosya türleri: PDF, XLSX, DOCX", "file_type_error": "Desteklenmeyen dosya türü. Lütfen geçerli bir format yükleyin.", "subscription_not_found": "Abonelik bulunamadı", "error": {"complaint_not_found": "Şikayet bulunamadı", "load_complaint_failed": "Şikayet detayları yüklenemedi", "upload_missing_data": "Yükleme başarılı ancak yükleme URL'si veya dosya ID'si dönmedi", "load_subscriptions_failed": "Abonelikler yüklenemedi"}, "toast": {"uploading": "{{filename}} yükleniyor...", "upload_success": "{{filename}} başarıyla yüklendi", "upload_failed": "<PERSON><PERSON><PERSON>", "invalid_file_type": "Geçersiz dosya türü. Lütfen desteklenen bir format yükleyin.", "submission_success": "Başvurunuz başarıyla gönderildi", "submission_success_full": "Başvurunuz {{id}} numarasıyla kaydedildi ve {{distributionCompany}} dağıtım şirketine iletildi.", "submission_failed": "Başvuru gö<PERSON>i"}, "filter_start_date": "Başlangıç <PERSON>", "filter_end_date": "Bitiş Tarihi", "application_category": "Başvuru Kategorisi", "all": "Tümü", "no_complaints_description_no_filters": "Seçili filtrelere uygun şikayet bulunamadı.", "application_id": "Başvuru No", "application_details": "Başvuru Detayları", "application_details_description": "Bu şikayet veya talep ile ilgili detaylar ve bilgiler.", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "unknown": "Bilinmiyor"}