{"subsTypeProduction": "Elektrik Üretimi", "subsTypeConsumption": "Elektrik Tüketimi", "subsKindIndividual": "Gerçek <PERSON>", "subsKindCorporate": "<PERSON><PERSON><PERSON>", "subsEmptyTitle": "Abonelik Bulunamadı.", "subsEmptyDescription": "Henüz bir aboneliğiniz yok. Verilerinizi görüntülemek için yeni bir abonelik ekleyin.", "subsFilterEmptyTitle": "Filtrelere Uygun Abonelik Bulunamadı", "subsFilterEmptyDescription": "Seçtiğiniz filtrelere uygun abonelik bulunamadı. Lütfen filtreleri değiştirerek tekrar deneyin.", "subsEmptyButton": "<PERSON><PERSON> a<PERSON> e<PERSON>", "subsClearFilterButton": "<PERSON><PERSON><PERSON><PERSON>", "subsAddButton": "<PERSON><PERSON>", "subsProduction": "Üretim", "subsConsumption": "<PERSON><PERSON><PERSON><PERSON>", "subsActionsDescription": "Abonelik bilgilerinizi düzenleyebilirsiniz.", "subsActionsEdit": "<PERSON><PERSON><PERSON><PERSON>", "subsActionsRemove": "Aboneliği Sil", "subsEditTitle": "Abonelik adını düzenle", "subsEditDescription": "Abonelik için yeni bir ad girin.", "subsEditLabel": "Abonelik adı", "subsEditHint": "Ev", "subsEditButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subsEditSuccessTitle": "Abonelik adı güncellendi", "subsEditSuccessDescription": "Başarılı! Abonelik adı başarıyla güncellendi.", "subsEditErrorTitle": "Abonelik adı güncellenemedi", "subsEditErrorDescription": "Bir sorun oluştu! Lütfen internet bağlantınızı kontrol edip tekrar deneyin.", "subsRemoveTitle": "Abonelik silinsin mi?", "subsRemoveDescription": "Bu işlemi onayladığınızda aboneliğiniz kalıcı olarak silinecek ve ilgili hizmetlere erişiminiz sona erecek.", "subsRemoveButton": "Aboneliği Sil", "subsRemoveSuccessTitle": "<PERSON><PERSON><PERSON> si<PERSON>", "subsRemoveSuccessDescription": "Başarılı! Aboneliğiniz başarıyla silindi.", "subsRemoveErrorTitle": "<PERSON><PERSON>lik si<PERSON>i", "subsRemoveErrorescription": "Bir sorun oluştu! Lütfen internet bağlantınızı kontrol edip tekrar deneyin.", "subsCreateTitle": "<PERSON><PERSON><PERSON> e<PERSON>", "subsCreateButton": "<PERSON><PERSON><PERSON> e<PERSON>", "subsCreateRegionTitle": "Dağıtım Şirketi", "subsCreateRegionHint": "Dağıtım Şirketi 1", "subsCreateInstallationIdTitle": "Te<PERSON>l Numa<PERSON>ı", "subsCreateInstallationIdHint": "Örn. J2HABDE75R", "subsCreateKindTitle": "Abonelik Türü", "subsCreateKindIndividual": "Gerçek <PERSON>", "subsCreateKindCorporate": "<PERSON><PERSON><PERSON>", "subsCreateTaxIdTitle": "<PERSON><PERSON>gi <PERSON>", "subsCreateTaxIdHint": "Örn. 3334455566", "subsCreateNameTitle": "Abonelik Adı", "subsCreateNameHint": "Ev", "subsCreateSuccessTitle": "Abonelik başarıyla eklendi", "subsCreateSuccessDescription": "Başarılı! Artık enerji tüketiminizi kolayca takip edebilirsiniz.", "subsCreateErrorTitle": "Abonelik eklenemedi", "subsCreateErrorDescription": "Bir sorun oluştu! Lütfen internet bağlantınızı kontrol edip tekrar deneyin.", "title": "<PERSON><PERSON><PERSON><PERSON>", "subscriptions": "<PERSON><PERSON><PERSON><PERSON>", "description": "Aboneliklerinizi ve tesislerinizi verimli bir şekilde yönetin.", "list": "Liste", "no_subscription_title": "Abonelik Bulunamadı.", "no_subscription_filter_title": "Abonelik Bulunamadı", "no_facility_title": "<PERSON><PERSON>adı.", "no_subscription_description": "Henüz bir aboneliğiniz yok. Verilerinizi görüntülemek için yeni bir abonelik ekleyin.", "no_subscription_filter_description": "Seçtiğiniz filtrelere uygun abonelik bulunmamaktadır. Lütfen filtreleri değiştirerek tekrar deneyin.", "no_facility_description": "Henüz bir tesisiniz yok. Verilerinizi görüntülemek için yeni bir tesis ekleyin.", "add_subscription": "<PERSON><PERSON> a<PERSON> e<PERSON>", "add_facility": "<PERSON><PERSON> te<PERSON> e<PERSON>", "facility_name": "<PERSON><PERSON>", "subscription_name": "Abonelik Adı", "subscription_name_placeholder": "Örn. Ev", "tax_number_placeholder": "Örn. 45654345", "distribution_company_placeholder": "Dağıtım şirketi ara ve seç", "installation_id_placeholder": "Örn. 123456", "subscription_info": "Abonelik Bilgileri", "subscription_type": "Abonelik Türü", "subsDetailTabDetail": "Abonelik Detayları", "subsDetailTabUsage": "<PERSON><PERSON>", "subsDetailTabOutage": "Kesinti ve Tazminat Bilgisi", "subsDetailTabSettings": "<PERSON><PERSON><PERSON><PERSON>", "subsDetailNameTitle": "Abonelik Adı", "subsDetailRegionTitle": "Dağıtım Şirketi", "subsDetailAddressTitle": "<PERSON><PERSON>", "subsDetailAddressErrorTitle": "Abonelik detayları yüklenemedi", "subsDetailAddressErrorDescription": "Bir sorun oluştu! Lütfen internet bağlantınızı kontrol edip tekrar deneyin.", "subsDetailInstallationIdTitle": "Te<PERSON>l Numa<PERSON>ı", "subsDetailTypeTitle": "<PERSON><PERSON>", "subsDetailConsumerInfoTitle": "Tüketici Bilgileri", "subsDetailKindTitle": "Abonelik Türü", "subsDetailKindIndividual": "Gerçek <PERSON>", "subsDetailKindeCorporate": "<PERSON><PERSON><PERSON>", "subsDetailTCKNTitle": "<PERSON><PERSON><PERSON><PERSON>", "subsDetailVKNTitle": "<PERSON><PERSON><PERSON>", "subsDetailStartDateTitle": "Abonelik Başlangıç Tarihi", "subsDetailNotifThresholdTitle": "Beklenmeyen Aşırı Tüketim", "subsDetailNotifThresholdDescription": "Aşırı tüketim limitinizi ayarlayabilirsiniz.", "subsDetailUserLimitTitle": "Kullanıcı Tanımlı Limit", "subsDetailUserLimitDescription": "Kullanıcı limitinizi ayarlayabilirsiniz.", "subsDetailGranularityTitle": "Dönem Türü", "subsDetailDateTitle": "<PERSON><PERSON><PERSON>", "subsDetailDateHint": "<PERSON><PERSON><PERSON>", "subsDetailCompareTitle": "<PERSON><PERSON><PERSON>", "subsDetailClear": "<PERSON><PERSON><PERSON>", "subsDetailSubmit": "<PERSON><PERSON><PERSON>", "subsDetailExport": "Dışa Aktar", "subsOutageExportSuccessTitle": "Kesinti verisi dışa aktarıldı", "subsOutageExportSuccessDescription": "Başarılı! Kesinti veriniz seçilen konuma kaydedildi.", "subsOutageExportErrorTitle": "Kesinti verisi dışa aktarılamadı", "subsOutageExportErrorDescription": "Bir sorun oluştu! Lütfen internet bağlantınızı kontrol edip tekrar deneyin.", "subscription_type_individual": "Gerçek <PERSON>", "subscription_type_description": "Bu Gerçek Kişi bir hesap mı", "subscription_type_corporate": "<PERSON><PERSON><PERSON>", "distribution_company": "Dağıtım Şirketi", "tax_number": "VKN/Vergi Kimlik Numarası", "facilities": "<PERSON><PERSON><PERSON>", "facility_type": "<PERSON><PERSON>", "update_subscription": "Abonelik bilgisini güncelle", "edit_facility": "<PERSON><PERSON><PERSON>", "edit_facility_description": "Tesis de<PERSON>rını burada güncelleyebilirsiniz.", "edit_subscription": "Aboneliği Dü<PERSON>le", "edit_description": "Abonelik bilgilerini güncelleyebilirsiniz.", "application_date": "Abonelik Başlangıç Tarihi", "application_category": "Başvuru Kategorisi", "application_description": "Başvuru Açıklaması", "application_description_hint": "2 bin karakteri aşmayacak dağıtım şirketine iletmek istediğiniz başvuru açıklamasını giriniz", "file": "<PERSON><PERSON><PERSON>", "subscription_detail": "Abonelik Detayları", "invoice_no": "Fatura Seri No", "contract_no": "Sözleşme Hesap No", "installation_type": "Tesisat Tipi", "installation_no": "Tekil No", "installation_number": "Tesisat Numarası", "consumer_info": "TÜKETİCİ BİLGİLERİ", "full_name": "Ad Soyad", "identity_no": "Kimlik No/VKN", "national_or_tax_id": "<PERSON><PERSON> Numarası / Vergi Kimlik No", "address": "<PERSON><PERSON>", "consumer_type": "Tüketici Grubu/Sınıfı", "phone": "İletişim", "reading_info": "Okuma Bilgileri", "reading_day": "Okuma Günü", "first_index": "<PERSON><PERSON>s", "last_index": "<PERSON>", "difference": "Fark", "days": "g<PERSON>n", "data_query": "<PERSON><PERSON>", "type_label": "<PERSON><PERSON><PERSON>", "individual": "Gerçek <PERSON>", "start_date": "Başlangıç <PERSON>", "created_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manage_subscriptions": "Aboneliklerinizi burada yönetebilirsiniz.", "manage_facilities": "Tesislerinizi burada yönetebilirsiniz.", "subscriptions_count": "{{count}} adet abonelik mevcut", "facilities_count": "{{count}} adet tesis mevcut", "installation_id": "Tekil No", "subscription": "Abonelik", "select_subscription": "Abonelik Seç", "subscription_name_required": "Abonelik adı gereklidir.", "subscription_type_required": "Abonelik türü gereklidir.", "tax_number_required": "Vergi numarası gereklidir.", "tax_number_digits_only": "<PERSON><PERSON>gi <PERSON>uma<PERSON>ı sadece rakam içermelidir.", "distribution_company_required": "Dağıtım şirketi gereklidir.", "installation_id_required": "Tesisat numarası gereklidir.", "production": "Üretim", "subscription_start_date": "Abonelik Başlangıç Tarihi", "subscription_region": "Abonelik Bölgesi", "tckn": "<PERSON><PERSON><PERSON><PERSON>", "vkn": "<PERSON><PERSON><PERSON>", "electricity_consumption": "Elektrik Tüketimi", "electricity_production": "Üretim", "facility_detail": "<PERSON><PERSON>", "notification_settings": "Abonelik Bildirim Ayarı", "notifications_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications_description": "Aboneliğinizle ilgili bildirim ayarlarını burada yönetebilirsiniz.", "warning_notifs": "Uyarı Bildirimleri", "notif_notifs": "<PERSON><PERSON><PERSON>", "warning_threshold": "Beklenmeyen Aşırı Tüketim Uyarı Eşiği", "kwh_warning_threshold": "Kullanıcı Tanımlı Limit", "pick_threshold": "<PERSON>şik Değeri Seçin", "usage": {"pick_date": "<PERSON><PERSON><PERSON>", "data_title": "<PERSON><PERSON><PERSON><PERSON>", "query_title": "<PERSON><PERSON><PERSON><PERSON>", "query_start_date_error": "Lütfen başlangıç tarihi se<PERSON>in", "query_end_date_error": "Lütfen bitiş tarihi se<PERSON>", "query_end_date_prev_error": "Bitiş tarihi ba<PERSON><PERSON><PERSON>ç tarihinden sonra olmalı", "query_clear_button": "<PERSON><PERSON><PERSON>", "query_export_button": "Dışa Aktar", "query_apply_button": "<PERSON><PERSON><PERSON><PERSON>", "daterange_title": "<PERSON><PERSON><PERSON>", "daterange_desc": "Verileri görmek istediğiniz zaman aralığını seçin", "daterange_label": "Aralık Türü", "range_placeholder": "Aralık türü seçin", "range_hour": "Saatlik", "range_day": "Günlük", "range_month": "Aylık", "range_year": "Yıllık", "range_month_placeholder": "<PERSON><PERSON><PERSON>", "range_end_month_placeholder": "Bitiş tarihi se<PERSON>", "day": "<PERSON><PERSON><PERSON>", "start_date": "Başlangıç <PERSON>", "end_date": "Bitiş Tarihi", "max_data_disclaimer": {"hour": "En fazla {{maxRange}} saatlik veri görüntülenebilir", "day": "En fazla {{maxRange}} günlük veri görüntülenebilir", "month": "En fazla {{maxRange}} aylık veri görüntülenebilir", "year": "En fazla {{maxRange}} yıllık veri görüntülenebilir"}, "compare_title": "Karş<PERSON>laştır", "compare_desc": "Kullanımınızı benzer tüketicilerle karşılaştırın", "compare_label": "Karşılaş<PERSON>ırma Türü", "compare_none": "Karşılaştırma yok", "compare_similar_district": "İlçede<PERSON> ben<PERSON> tü<PERSON>", "usage_compare_similar_city": "<PERSON><PERSON><PERSON>", "table_date": "<PERSON><PERSON><PERSON>", "usage_chart_label": "Toplam Tüketim (kWh)", "compare_chart_label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart_type_line": "<PERSON><PERSON><PERSON>", "chart_type_bar": "<PERSON><PERSON><PERSON>", "chart_type_table": "<PERSON><PERSON><PERSON>", "no_usage_data_title": "Kullanım Verisi Bulunamadı", "no_usage_data_description": "Seçilen döneme ait kullanım verisi yok", "query_usage_button": "Kullanım Verisi <PERSON>", "compare_placeholder": "Karş<PERSON>laş<PERSON><PERSON>rma türü seçin", "query_invalid_date_error": "Lütfen geçerli bir tarih seçin", "compare_average": "Ortalama", "compare": {"query": {"hour": "Son {{range}} saate ait veriler", "day": "Son {{range}} g<PERSON>ne ait veriler", "month": "Son {{range}} aya ait veriler", "year": "Son {{range}} yıla ait veriler"}, "usage": "<PERSON><PERSON><PERSON><PERSON> verisi", "average": "<PERSON><PERSON><PERSON> veri", "similar-consumer-district": "İlçede<PERSON> ben<PERSON> tü<PERSON>", "similar-consumer-city": "<PERSON><PERSON><PERSON>"}, "single_time": "Tek Zamanlı Dağılım", "three_time": "Çok Zamanlı Dağılım", "data_type": "<PERSON><PERSON>", "export": {"title": "Kullanım Verisini Dışa Aktar", "filetype": "<PERSON><PERSON><PERSON>", "hourly": {"label": "Saatlik Dağılım", "enabled": "Aktif", "disabled": "<PERSON><PERSON><PERSON>"}, "year": "<PERSON><PERSON><PERSON>"}}, "no_subscriptions_found": "Abonelik bulunamadı", "search_subscriptions": "Aboneliklerde ara...", "error": {"invalid": {"name": "Geçersiz abonelik adı", "installationId": "Geçersiz tekil numarası", "tckn": "Geçersiz T.C. kimlik numarası", "vkn": "Geçersiz vergi kimlik numarası"}, "exists": {"name": "Abonelik adı zaten mevcut", "installationId": "Tekil numarası zaten mevcut"}}, "all": "Tümü", "filter": "Filtrele", "apply": "<PERSON><PERSON><PERSON><PERSON>", "search_subscription": "Aboneliklerde Ara...", "no_subscription_found": "Abonelik bulunamadı", "delete_confirmation": "<PERSON><PERSON> aboneliği silmek istediğinizden emin misiniz?", "delete_description": "Bu işlem geri alınamaz.", "outages": "Kesinti & Tazminat Bilgisi", "notification_settings_saved": "<PERSON><PERSON><PERSON><PERSON> başarıyla kaydedildi.", "download_loading": "İndiriliyor...", "download_failed": "<PERSON><PERSON><PERSON> indirili<PERSON>en bir hata <PERSON>.", "download_successful": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> indirildi.", "subsUsageTitle": "Ku<PERSON>ım Verisi", "subsTabSingle": "Tek Zamanlı Dağılım", "subsTabMultiple": "Çok Zamanlı Dağılım", "subsUsageTooltip": "<PERSON><PERSON><PERSON><PERSON> verisi", "subsUsageTooltipAverage": "<PERSON><PERSON><PERSON> veri", "subsUsageTooltipCity": "<PERSON><PERSON><PERSON>", "subsUsageTooltipDistrict": "İlçede<PERSON> ben<PERSON> tü<PERSON>", "subsUsageExport": "Dışa Aktar", "subsUsageExportSuccessTitle": "Kullanım verisi dışa aktarıldı", "subsUsageExportSuccessDescription": "Başarılı! Kullanım veriniz seçilen konuma kaydedildi.", "subsUsageExportErrorTitle": "Kullanım verisi dışa aktarılamadı", "subsUsageExportErrorDescription": "Bir sorun oluştu! Lütfen internet bağlantınızı kontrol edip tekrar deneyin.", "exportOptionsTitle": "Veri Dışa Aktar", "exportOptionsDescription": "Kullanım verinizi dışa aktarmak için bir format seçin.", "exportOptionsTypeTitle": "<PERSON><PERSON><PERSON>", "exportOptionsHourTitle": "Saatlik Dağılım", "exportOptionsHourEnabled": "Aktif", "exportOptionsHourDisabled": "<PERSON><PERSON><PERSON>", "exportOptionsSubmit": "Dışa Aktar", "subsFiltersTitle": "Abonelik Filtreleri", "subsFiltersSelectButton": "<PERSON><PERSON><PERSON><PERSON>", "subsFiltersClear": "<PERSON><PERSON><PERSON>", "subsFiltersRegionTitle": "Dağıtım Şirketi", "subsFiltersTypeTitle": "<PERSON><PERSON>", "subsFiltersKindTitle": "Abonelik Türü", "subsFiltersEmptyTitle": "<PERSON><PERSON><PERSON><PERSON> bo<PERSON>", "subsFiltersEmptyDescription": "Uygun filtre bulunamadı.", "subsRegionsTitle": "Dağıtım Şirketi Seç", "subsRegionsSearchHint": "Ara...", "subsRegionsSelectButton": "<PERSON><PERSON><PERSON><PERSON>", "subsRegionsEmptyTitle": "Dağıtım şirketi bulunamadı", "subsRegionsEmptyDescription": "Arama kriterlerinize uygun dağıtım şirketi bulunamadı. Lütfen farklı bir arama deneyin.", "notifThresholdEditTitle": "Beklenmeyen Aşırı Tüketim", "notifThresholdEditDescription": "Aşırı tüketim limitinizi ayarlayabilirsiniz.", "notifThresholdEditButton": "<PERSON><PERSON>", "userLimitEditTitle": "Kullanıcı Tanımlı Limit", "userLimitEditDescription": "Kullanıcı limitinizi ayarlayabilirsiniz.", "userLimitEditHint": "140 kWh", "userLimitEditLabel": "Kullanıcı limiti", "userLimitEditButton": "<PERSON><PERSON>", "usageGranularityDay": "Günlük", "usageGranularityMonth": "Aylık", "usageGranularityYear": "Yıllık", "usageCompareTypePast": "<PERSON><PERSON><PERSON><PERSON>ş <PERSON><PERSON><PERSON><PERSON> (Son {duration} {granularity})", "usageCompareTypeAverage": "Ortalama Tüketim", "usageCompareTypeCity": "<PERSON><PERSON><PERSON>", "usageCompareTypeDistrict": "İlçede<PERSON> ben<PERSON> tü<PERSON>", "characterLimitInstIdTitle": "Tesisat Numarası Karakter Sınırı", "characterLimitInstIdDescription": "Tesisat numarası en az {min} karakter olmalıdır", "characterLimitTaxIdTitle": "Vergi Kimlik No Karakter Sınırı", "characterLimitTaxIdDescription": "<PERSON>ergi kimlik numarası {min} karakter olmalıdır", "chartNote": "Veriler aboneliğinizin olduğu elektrik dağıtım firması sisteminden sağlanmaktadır.", "select-date-range": "<PERSON><PERSON><PERSON>"}