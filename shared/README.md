# @mass/shared

A shared component and utility library for the Mass project applications.

## Overview

This package contains common UI components, hooks, and utilities that are used
across multiple applications in the Mass monorepo. Using a shared library
ensures consistency in UI and behavior while reducing code duplication.

## Features

- **React Components**: Reusable UI components
- **Hooks**: Custom React hooks for common functionality
- **Utilities**: Helper functions and tools

## Development

### Prerequisites

- Node.js 18+
- PNPM 8+

### Installation

```bash
# From the root of the monorepo
pnpm install

# Or from this directory
pnpm install
```

### Development Mode

```bash
pnpm dev
```

This starts `tsup` in watch mode, automatically rebuilding the package when
files change.

### Building

```bash
pnpm build
```

Builds the package using `tsup`.

## Usage

This package is consumed by other applications in the Mass monorepo.

```tsx
// Example usage in an application
import { Button, useToggle } from "@mass/shared";

function MyComponent() {
    const [isOpen, toggle] = useToggle(false);

    return (
        <div>
            <Button onClick={toggle}>
                {isOpen ? "Close" : "Open"}
            </Button>
        </div>
    );
}
```

## Architecture

The shared package is built with `tsup` for optimal tree-shaking and minimal
bundle size. Components are structured for intuitive imports and maximum
reusability.

### Dependencies

- **React 19**: Modern React for component development
- **tailwind-merge** and **clsx**: For managing class names

## Adding New Components

When adding new components:

1. Create the component in the appropriate directory
2. Export it from the nearest index.ts file
3. Make sure it's exported from the main index.ts file
4. Build the package with `pnpm build`
5. The component will be available in applications that import from
   @mass/shared

## TypeScript

All components and utilities are fully typed for the best development
experience.