import { describe, it, expect } from 'vitest';
import { calculatePasswordStrength } from './calculate-password-strength';

describe('Validators', () => {
  describe('calculatePasswordStrength', () => {
    it('should return 0 for empty passwords', () => {
      expect(calculatePasswordStrength('')).toBe(0);
    });
    
    it('should return higher score for complex passwords', () => {
      const weakPassword = 'password';
      const strongPassword = 'P@ssw0rd!123';
      
      const weakScore = calculatePasswordStrength(weakPassword);
      const strongScore = calculatePasswordStrength(strongPassword);
      
      expect(strongScore).toBeGreaterThan(weakScore);
    });
  });
});
