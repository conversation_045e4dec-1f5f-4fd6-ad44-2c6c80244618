import { z } from "zod";

export const isValidTCKN = (tckn: string): boolean => {
  if (!tckn || tckn.length !== 11 || !/^\d+$/.test(tckn)) {
    return false;
  }

  const digits = tckn.split("").map(Number);
  
  if (digits[0] === 0) {
    return false;
  }
  
  const sumOdd = digits[0] + digits[2] + digits[4] + digits[6] + digits[8];
  const sumEven = digits[1] + digits[3] + digits[5] + digits[7];
  
  const digit10 = (sumOdd * 7 - sumEven) % 10;
  if (digit10 !== digits[9]) {
    return false;
  }
  
  const sum = digits.slice(0, 10).reduce((sum, digit) => sum + digit, 0);
  const digit11 = sum % 10;
  
  return digit11 === digits[10];
};

export const isValidVKN = (vkn: string): boolean => {
  if (!vkn || vkn.length !== 10 || !/^\d+$/.test(vkn)) {
    return false;
  }
  
  return true;
};

/**
 * TCKN Zod validation schema
 */
export const tcknSchema = z
  .string()
  .min(11, { message: "errors:tckn_too_short" })
  .max(11, { message: "errors:tckn_too_long" })
  .refine((val) => /^\d+$/.test(val), { 
    message: "errors:tckn_numeric_only" 
  })
  .refine((val) => isValidTCKN(val), { 
    message: "errors:tckn_invalid" 
  });

/**
 * VKN Zod validation schema
 */
export const vknSchema = z
  .string()
  .min(10, { message: "errors:vkn_too_short" })
  .max(10, { message: "errors:vkn_too_long" })
  .refine((val) => /^\d+$/.test(val), { 
    message: "errors:vkn_numeric_only" 
  })
  .refine((val) => isValidVKN(val), {
    message: "errors:vkn_invalid"
  });
