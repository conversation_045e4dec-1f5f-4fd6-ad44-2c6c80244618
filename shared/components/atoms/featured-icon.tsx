import type { IconNames } from './Iconify'
import Iconify from './Iconify'

interface Props extends React.HTMLAttributes<HTMLDivElement> {
    name: IconNames
    variant?: 'default' | 'alternative'
}

function FeaturedIcon({ name, className, variant = 'default', ...props }: Props) {
    const defaultClasses = 'relative rounded-[28px] flex items-center justify-center bg-gray-100 border-gray-200 border-solid border-[8px] box-border size-14'
    const alternativeClasses = 'relative shadow-[0px_0px_0px_1px_rgba(10,_13,_18,_0.18)_inset,_0px_-2px_0px_rgba(10,_13,_18,_0.05)_inset,_0px_1px_2px_rgba(16,_24,_40,_0.05)] rounded-lg bg-base-white border-gray-200 border-solid border-[1px] box-border flex flex-row items-center justify-start p-2.5'

    const appliedClasses = variant === 'default' ? defaultClasses : alternativeClasses

    return (
        <div className={`${appliedClasses} ${className}`} {...props}>
            <Iconify name={name} className="text-gray-500 size-6" />
        </div>
    )
}

export default FeaturedIcon
