import EmptyVector from '../../assets/empty.svg'
import Pattern from '../../assets/pattern_decorative.svg'

interface Props {
    title: string
    description: string
    children?: React.ReactNode
}

function Empty({ title, description, children }: Props) {
    return (
        <div className="w-full relative flex flex-col items-center justify-start gap-4 text-center text-xl text-gray-900 font-text-md-regular">
            <img src={Pattern as unknown as string} alt="pattern" className="w-full absolute top-[-176px] h-[480px] -z-10 pointer-events-none" />
            <img className="w-[184px] relative h-[152px] overflow-hidden shrink-0" alt="empty-vector" src={EmptyVector as unknown as string} />
            <div className="w-full flex flex-col items-center justify-start gap-3 max-w-[452px]">
                <div className="self-stretch relative leading-[30px] font-semibold">{title}</div>
                <div className="w-[494px] relative text-base leading-[24px] text-gray-600 inline-block">{description}</div>
            </div>
            {children}
        </div>
    )
}

export default Empty
