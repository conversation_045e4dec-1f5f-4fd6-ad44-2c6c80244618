// @ts-nocheck
import Mass from '../../assets/icons/mass.json'
// @ts-nocheck
import Untitled from '../../assets/icons/untitled.json'

import { addCollection, Icon } from '@iconify/react'

type Prefix<T extends string, U extends string> = `${T}:${U}`

type IconCollectionNames<
    C extends string,
    T extends { icons: Record<string, unknown> },
> = Prefix<C, keyof T['icons'] & string>

type UntitledIconNames = IconCollectionNames<'untitled', typeof Untitled>
type MassIconNames = IconCollectionNames<'mass', typeof Mass>

export type IconNames = UntitledIconNames | MassIconNames

interface IconProps {
    size?: number
    color?: string
    name: IconNames
    inherit?: boolean
    className?: string
}

addCollection(Untitled)
addCollection(Mass)

function Iconify({
    size = 12,
    name,
    color,
    inherit,
    className,
    ...props
}: IconProps) {
    return (
        <Icon
            icon={name}
            color={color}
            width={size}
            height={size}
            className={className}
            {...props}
        />
    )
}

export default Iconify
