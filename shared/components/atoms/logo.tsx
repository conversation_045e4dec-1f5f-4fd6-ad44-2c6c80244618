import * as React from "react";
import logoBig from "../../assets/logo-mark.svg?url";
import logo from "../../assets/logo.svg?url";
import { Link } from "@tanstack/react-router";

interface LogoProps extends React.HTMLAttributes<HTMLDivElement> {
  state: "expanded" | "collapsed";
}

const Logo: React.FC<LogoProps> = ({ state, ...props }) => (
  <div {...props} className={`flex gap-3 items-center ${props.className}`}>
    <Link href="/" to="/">
      <img src={state === 'collapsed' ? logo : logoBig} className={state === 'collapsed' ? 'aspect-square flex-shrink-0 h-10 mx-auto mb-4 ml-1' : 'mx-auto h-10 md:h-14 md:-ml-4'} />
    </Link>
  </div>
);

export default Logo;
