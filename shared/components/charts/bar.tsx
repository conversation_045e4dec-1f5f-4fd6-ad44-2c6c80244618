
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Cell,
  XAxis,
  YAxis,
} from "recharts";

import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from "../../components/ui/chart";
import { ChartData, ChartProps } from "./chart";
import { useTranslation } from "react-i18next";
import { getDateFunctionsForGranularity, zeroFill } from "./utils";
import { useDateLocale } from "../../hooks/use-date-locale";

export function BarDataChart(props: ChartProps) {
  const { t } = useTranslation("subscriptions");
  const dateLocale = useDateLocale();
  const { label } = getDateFunctionsForGranularity(
    props.granularity,
    dateLocale
  );

  const { chartData, compareChartData } = zeroFill(
    props.data,
    props.compare,
    props.startDate,
    props.endDate,
    props.granularity,
    dateLocale
  );

  const comparing = props.compare && Object.keys(props.compare).length > 0;

  const isThreeTime = (data: ChartData) =>
    props.threeTime &&
    props.granularity !== "hour" &&
    data.T1 !== null &&
    data.T2 !== null &&
    data.T3 !== null;

  const threeTime = chartData.some((d) => isThreeTime(d.data));

  const baseColor = "hsl(var(--primary))";

  const compareMapping = {
    usage: "usage",
    average: "average",
    cityEntry: "similar-consumer-city",
    districtEntry: "similar-consumer-district",
  };

  const chartConfig = {
    "data.T0": {
      label: t("usage.usage_chart_label"),
    },
    "data.T1": {
      label: "T1",
    },
    "data.T2": {
      label: "T2",
    },
    "data.T3": {
      label: "T3",
    },
    ...Object.fromEntries(
      Object.entries(compareMapping).flatMap(([key, value]) =>
        [...Array(4)].map((_, tv) => [
          "compare." + key + ".T" + tv,
          {
            label:
              t("usage.compare." + value) + " " + (tv === 0 ? "" : `(T${tv})`),
          },
        ])
      )
    ),
  } satisfies ChartConfig;

  const colorGroups = [
    ["#7CD4FD", "#36BFFA", "#0BA5EC"],
    ["#FDE272", "#FAC515", "#EAAA08"],
    ["#A6EF67", "#85E13A", "#66C61C"],
    ["#EEAAFD", "#E478FA", "#D444F1"],
    ["#FEA3B4", "#FD6F8E", "#F63D68"],
  ];

  return (
    <ChartContainer config={chartConfig} className="h-full">
      <BarChart
        accessibilityLayer
        data={comparing ? compareChartData : chartData}
      >
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey={comparing ? "key" : "timeframe"}
          tickMargin={10}
          tickFormatter={(value) =>
            comparing
              ? t(
                  "usage.compare." +
                    compareMapping[value as keyof typeof compareMapping]
                )
              : label(value)
          }
        />
        <YAxis />
        <ChartTooltip
          cursor={false}
          content={({ payload, label: _label }) => {
            if (!payload || payload.length === 0) return null;
            return (
              <div className="bg-white p-3 rounded-lg shadow-lg">
                <div className="font-xs mb-1">
                  {comparing
                    ? t(
                        "usage.compare." +
                          compareMapping[_label as keyof typeof compareMapping]
                      )
                    : label(_label)}
                </div>
                <div className="flex flex-col">
                  {payload
                    .map((entry, index) => ({ entry, index }))
                    .reverse()
                    .map(({ entry }) => {
                      const colorIndex = comparing
                        ? compareChartData.findIndex(
                            (elem) => elem.key === entry.payload.key
                          )
                        : chartData.length > 1 &&
                            chartData[chartData.length - 1].timeframe ===
                              entry.payload.timeframe
                          ? 4
                          : 0;
                      const tIndex =
                        parseInt(
                          (entry.dataKey?.toString() ?? "").match(
                            /\.T(\d+)$/
                          )?.[1] ?? ""
                        ) - 1;
                      const fillColor =
                        colorGroups[colorIndex][tIndex < 0 ? 2 : tIndex];

                      return (
                        <div
                          key={entry.dataKey}
                          className="flex items-center justify-center text-sm mb-1"
                        >
                          <div
                            className="w-2 h-2 rounded-full mr-1"
                            style={{ backgroundColor: fillColor }}
                          />
                          <span className="flex-1">
                            {
                              chartConfig[
                                (entry.name ?? "") as keyof typeof chartConfig
                              ].label
                            }
                            : {(entry.value ?? 0).toLocaleString()} {props.unit}
                          </span>
                        </div>
                      );
                    })}
                </div>
              </div>
            );
          }}
        />
        {threeTime ? (
          [...Array(3)]
            .map((_, i) => i)
            .reverse()
            .map((i) => (
              <Bar
                key={`bar-${i}`}
                dataKey={"data.T" + (i + 1)}
                fill={colorGroups[0][i]}
                stackId={"a"}
                isAnimationActive={false}
                barSize={120}
              >
                {comparing
                  ? [...Array(4)].map((_, j) => (
                      <Cell key={`cell-${j}`} fill={colorGroups[j][i]} />
                    ))
                  : chartData.length <= 1
                    ? null
                    : chartData.map((_, j, self) => (
                        <Cell
                          key={`cell-${j}`}
                          fill={colorGroups[j === self.length - 1 ? 4 : 0][i]}
                        />
                      ))}
              </Bar>
            ))
        ) : (
          <Bar
            dataKey="data.T0"
            fill={baseColor}
            isAnimationActive={false}
            barSize={120}
          >
            {comparing
              ? [...Array(4)].map((_, j) => (
                  <Cell key={`cell-${j}`} fill={colorGroups[j][2]} />
                ))
              : chartData.map((_, j, self) => (
                  <Cell
                    key={`cell-${j}`}
                    fill={colorGroups[j === self.length - 1 ? 4 : 0][2]}
                  />
                ))}
          </Bar>
        )}
      </BarChart>
    </ChartContainer>
  );
}
