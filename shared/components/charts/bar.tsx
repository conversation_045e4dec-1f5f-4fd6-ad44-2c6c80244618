
import {
  Bar,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Cell,
  XAxis,
  YAxis,
} from "recharts";

import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from "../../components/ui/chart";
import { ChartData, ChartProps } from "./chart";
import { useTranslation } from "react-i18next";
export function BarDataChart(props: ChartProps) {
  const { t } = useTranslation("subscriptions");

  // Use data directly from props
  const chartData = props.data;

  const isThreeTime = (data: ChartData) =>
    props.threeTime &&
    props.granularity !== "hour" &&
    data.T1 !== null &&
    data.T2 !== null &&
    data.T3 !== null;

  const threeTime = chartData.some((d) => isThreeTime(d.data));

  const baseColor = "hsl(var(--primary))";

  const compareMapping = {
    usage: "usage",
    average: "average",
    cityEntry: "similar-consumer-city",
    districtEntry: "similar-consumer-district",
  };

  const chartConfig = {
    "data.T0": {
      label: t("usage.usage_chart_label"),
    },
    "data.T1": {
      label: "T1",
    },
    "data.T2": {
      label: "T2",
    },
    "data.T3": {
      label: "T3",
    },
    ...Object.fromEntries(
      Object.entries(compareMapping).flatMap(([key, value]) =>
        [...Array(4)].map((_, tv) => [
          "data.T" + tv,
          {
            label: key === "usage"
              ? (tv === 0 ? t("usage.usage_chart_label") : `T${tv}`)
              : t("usage.compare." + value) + " " + (tv === 0 ? "" : `(T${tv})`),
          },
        ])
      )
    ),
  } satisfies ChartConfig;

  const colorGroups = [
    ["#7CD4FD", "#36BFFA", "#0BA5EC"],
    ["#FDE272", "#FAC515", "#EAAA08"],
    ["#A6EF67", "#85E13A", "#66C61C"],
    ["#EEAAFD", "#E478FA", "#D444F1"],
    ["#FEA3B4", "#FD6F8E", "#F63D68"],
  ];

  // Create comparison data from props
  const comparisonData = [];
  if (props.compare?.average) {
    comparisonData.push({ key: "average", data: props.compare.average });
  }
  if (props.compare?.cityEntry) {
    comparisonData.push({ key: "cityEntry", data: props.compare.cityEntry });
  }
  if (props.compare?.districtEntry) {
    comparisonData.push({ key: "districtEntry", data: props.compare.districtEntry });
  }

  // Always combine usage and comparison data
  const chartDisplayData = [
    // Usage data - always first
    {
      key: "usage",
      data: chartData.length > 0 ? chartData[0].data : { T0: 0, T1: 0, T2: 0, T3: 0 }
    },
    // Comparison data - if available
    ...comparisonData
  ];

  return (
    <ChartContainer config={chartConfig} className="h-full">
      <BarChart
        accessibilityLayer
        data={chartDisplayData}
      >
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey="key"
          tickMargin={10}
          tickFormatter={(value) =>
            t(
              "usage.compare." +
                compareMapping[value as keyof typeof compareMapping]
            )
          }
        />
        <YAxis />
        <ChartTooltip
          cursor={false}
          content={({ payload, label: _label }) => {
            if (!payload || payload.length === 0) return null;
            return (
              <div className="bg-white p-3 rounded-lg shadow-lg">
                <div className="font-xs mb-1">
                  {t(
                    "usage.compare." +
                      compareMapping[_label as keyof typeof compareMapping]
                  )}
                </div>
                <div className="flex flex-col">
                  {payload
                    .map((entry, index) => ({ entry, index }))
                    .reverse()
                    .map(({ entry }) => {
                      const colorIndex = chartDisplayData.findIndex(
                        (elem) => ('key' in elem) && elem.key === entry.payload.key
                      );
                      const tIndex =
                        parseInt(
                          (entry.dataKey?.toString() ?? "").match(
                            /\.T(\d+)$/
                          )?.[1] ?? ""
                        ) - 1;
                      const fillColor =
                        colorGroups[colorIndex][tIndex < 0 ? 2 : tIndex];

                      return (
                        <div
                          key={entry.dataKey}
                          className="flex items-center justify-center text-sm mb-1"
                        >
                          <div
                            className="w-2 h-2 rounded-full mr-1"
                            style={{ backgroundColor: fillColor }}
                          />
                          <span className="flex-1">
                            {
                              chartConfig[
                                (entry.name ?? "") as keyof typeof chartConfig
                              ].label
                            }
                            : {(entry.value ?? 0).toLocaleString()} {props.unit}
                          </span>
                        </div>
                      );
                    })}
                </div>
              </div>
            );
          }}
        />
        {threeTime ? (
          [...Array(3)]
            .map((_, i) => i)
            .reverse()
            .map((i) => (
              <Bar
                key={`bar-${i}`}
                dataKey={"data.T" + (i + 1)}
                fill={colorGroups[0][i]}
                stackId={"a"}
                isAnimationActive={false}
                barSize={120}
              >
                {chartDisplayData.map((_, j) => (
                  <Cell key={`cell-${j}`} fill={colorGroups[j][i]} />
                ))}
              </Bar>
            ))
        ) : (
          <Bar
            dataKey="data.T0"
            fill={baseColor}
            isAnimationActive={false}
            barSize={120}
          >
            {chartDisplayData.map((_, j) => (
              <Cell key={`cell-${j}`} fill={colorGroups[j][2]} />
            ))}
          </Bar>
        )}
      </BarChart>
    </ChartContainer>
  );
}
