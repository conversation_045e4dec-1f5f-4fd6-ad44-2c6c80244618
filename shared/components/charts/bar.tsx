
"use client";

import { useTranslation } from "react-i18next";
import { useDateLocale } from "../../hooks/use-date-locale";
import { ChartProps } from "./chart";
import { getDateFunctionsForGranularity, zeroFill } from "./utils";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  Y<PERSON><PERSON><PERSON>,
  CartesianGrid,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  ChartConfig,
} from "../ui/chart";
import { useMemo } from "react";

const NUMBER_FORMAT = new Intl.NumberFormat("es-ES", {
  useGrouping: true,
  maximumFractionDigits: 2
});

// Color scheme for different data series
const COLORS = {
  T0: "#0BA5EC",
  T1: "#EAAA08",
  T2: "#66C61C",
  T3: "#D444F1",
  average: "#8B5CF6",
  cityEntry: "#F59E0B",
  districtEntry: "#10B981"
};

interface BarChartDataPoint {
  timeframe: string;
  formattedDate: string;
  T0: number;
  T1?: number;
  T2?: number;
  T3?: number;
  average_T0?: number;
  average_T1?: number;
  average_T2?: number;
  average_T3?: number;
  cityEntry_T0?: number;
  cityEntry_T1?: number;
  cityEntry_T2?: number;
  cityEntry_T3?: number;
  districtEntry_T0?: number;
  districtEntry_T1?: number;
  districtEntry_T2?: number;
  districtEntry_T3?: number;
}

export function BarDataChart(props: ChartProps) {
  const { t } = useTranslation("subscriptions");
  const dateLocale = useDateLocale();
  const { label } = getDateFunctionsForGranularity(
    props.granularity,
    dateLocale
  );

  const { chartData } = zeroFill(
    props.data,
    props.compare,
    props.startDate,
    props.endDate,
    props.granularity,
    dateLocale
  );

  // Transform data for the bar chart
  const transformedData: BarChartDataPoint[] = useMemo(() => {
    const baseTransformed = chartData.map((point) => {
      const baseData: BarChartDataPoint = {
        timeframe: point.timeframe.toISOString(),
        formattedDate: label(point.timeframe),
        T0: point.data.T0,
      };

      // Add T1, T2, T3 if they exist and threeTime is enabled
      if (props.threeTime && props.granularity !== "hour") {
        if (point.data.T1 !== null) baseData.T1 = point.data.T1;
        if (point.data.T2 !== null) baseData.T2 = point.data.T2;
        if (point.data.T3 !== null) baseData.T3 = point.data.T3;
      }

      return baseData;
    });

    // Add comparison data as constant reference lines across all data points
    if (props.compare && baseTransformed.length > 0) {
      return baseTransformed.map((point) => {
        const enhanced = { ...point };

        if (props.compare!.average) {
          enhanced.average_T0 = props.compare!.average.T0;
          if (props.threeTime && props.granularity !== "hour") {
            if (props.compare!.average.T1 !== null) enhanced.average_T1 = props.compare!.average.T1;
            if (props.compare!.average.T2 !== null) enhanced.average_T2 = props.compare!.average.T2;
            if (props.compare!.average.T3 !== null) enhanced.average_T3 = props.compare!.average.T3;
          }
        }

        if (props.compare!.cityEntry) {
          enhanced.cityEntry_T0 = props.compare!.cityEntry.T0;
          if (props.threeTime && props.granularity !== "hour") {
            if (props.compare!.cityEntry.T1 !== null) enhanced.cityEntry_T1 = props.compare!.cityEntry.T1;
            if (props.compare!.cityEntry.T2 !== null) enhanced.cityEntry_T2 = props.compare!.cityEntry.T2;
            if (props.compare!.cityEntry.T3 !== null) enhanced.cityEntry_T3 = props.compare!.cityEntry.T3;
          }
        }

        if (props.compare!.districtEntry) {
          enhanced.districtEntry_T0 = props.compare!.districtEntry.T0;
          if (props.threeTime && props.granularity !== "hour") {
            if (props.compare!.districtEntry.T1 !== null) enhanced.districtEntry_T1 = props.compare!.districtEntry.T1;
            if (props.compare!.districtEntry.T2 !== null) enhanced.districtEntry_T2 = props.compare!.districtEntry.T2;
            if (props.compare!.districtEntry.T3 !== null) enhanced.districtEntry_T3 = props.compare!.districtEntry.T3;
          }
        }

        return enhanced;
      });
    }

    return baseTransformed;
  }, [chartData, props.compare, props.threeTime, props.granularity, label]);

  // Determine which time periods to show
  const showThreeTime = props.threeTime && props.granularity !== "hour" &&
    chartData.some(d => d.data.T1 !== null && d.data.T2 !== null && d.data.T3 !== null);

  // Chart configuration for styling
  const chartConfig: ChartConfig = useMemo(() => {
    const config: ChartConfig = {
      T0: {
        label: showThreeTime ? "T0" : t("usage.usage_chart_label"),
        color: COLORS.T0,
      },
    };

    if (showThreeTime) {
      config.T1 = { label: "T1", color: COLORS.T1 };
      config.T2 = { label: "T2", color: COLORS.T2 };
      config.T3 = { label: "T3", color: COLORS.T3 };
    }

    // Add comparison data to config
    if (props.compare) {
      if (props.compare.average) {
        config.average_T0 = {
          label: showThreeTime ? `${t("average")} T0` : t("average"),
          color: COLORS.average
        };
        if (showThreeTime) {
          config.average_T1 = { label: `${t("average")} T1`, color: COLORS.average };
          config.average_T2 = { label: `${t("average")} T2`, color: COLORS.average };
          config.average_T3 = { label: `${t("average")} T3`, color: COLORS.average };
        }
      }

      if (props.compare.cityEntry) {
        config.cityEntry_T0 = {
          label: showThreeTime ? `${t("similar-consumer-city")} T0` : t("similar-consumer-city"),
          color: COLORS.cityEntry
        };
        if (showThreeTime) {
          config.cityEntry_T1 = { label: `${t("similar-consumer-city")} T1`, color: COLORS.cityEntry };
          config.cityEntry_T2 = { label: `${t("similar-consumer-city")} T2`, color: COLORS.cityEntry };
          config.cityEntry_T3 = { label: `${t("similar-consumer-city")} T3`, color: COLORS.cityEntry };
        }
      }

      if (props.compare.districtEntry) {
        config.districtEntry_T0 = {
          label: showThreeTime ? `${t("similar-consumer-district")} T0` : t("similar-consumer-district"),
          color: COLORS.districtEntry
        };
        if (showThreeTime) {
          config.districtEntry_T1 = { label: `${t("similar-consumer-district")} T1`, color: COLORS.districtEntry };
          config.districtEntry_T2 = { label: `${t("similar-consumer-district")} T2`, color: COLORS.districtEntry };
          config.districtEntry_T3 = { label: `${t("similar-consumer-district")} T3`, color: COLORS.districtEntry };
        }
      }
    }

    return config;
  }, [showThreeTime, props.compare, t]);

  if (!transformedData.length) {
    return (
      <div className="flex h-[350px] items-center justify-center text-muted-foreground">
        {t("no-data-available")}
      </div>
    );
  }

  return (
    <ChartContainer config={chartConfig} className="h-[350px] w-full">
      <BarChart
        data={transformedData}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="formattedDate"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          interval={0}
          angle={-45}
          textAnchor="end"
          height={60}
        />
        <YAxis
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${NUMBER_FORMAT.format(value)}`}
          label={{ value: props.unit, angle: -90, position: 'insideLeft' }}
        />
        <ChartTooltip
          content={
            <ChartTooltipContent
              dataFormatter={(value) => `${NUMBER_FORMAT.format(Number(value))} ${props.unit}`}
              labelFormatter={(label) => label}
            />
          }
        />
        <ChartLegend content={<ChartLegendContent />} />

        {/* Usage data bars */}
        <Bar
          dataKey="T0"
          fill={COLORS.T0}
          name={showThreeTime ? "T0" : t("usage.usage_chart_label")}
        />
        {showThreeTime && (
          <>
            <Bar dataKey="T1" fill={COLORS.T1} name="T1" />
            <Bar dataKey="T2" fill={COLORS.T2} name="T2" />
            <Bar dataKey="T3" fill={COLORS.T3} name="T3" />
          </>
        )}

        {/* Comparison data bars */}
        {props.compare?.average && (
          <>
            <Bar
              dataKey="average_T0"
              fill={COLORS.average}
              name={showThreeTime ? `${t("average")} T0` : t("average")}
              opacity={0.7}
            />
            {showThreeTime && (
              <>
                <Bar dataKey="average_T1" fill={COLORS.average} name={`${t("average")} T1`} opacity={0.7} />
                <Bar dataKey="average_T2" fill={COLORS.average} name={`${t("average")} T2`} opacity={0.7} />
                <Bar dataKey="average_T3" fill={COLORS.average} name={`${t("average")} T3`} opacity={0.7} />
              </>
            )}
          </>
        )}

        {props.compare?.cityEntry && (
          <>
            <Bar
              dataKey="cityEntry_T0"
              fill={COLORS.cityEntry}
              name={showThreeTime ? `${t("similar-consumer-city")} T0` : t("similar-consumer-city")}
              opacity={0.7}
            />
            {showThreeTime && (
              <>
                <Bar dataKey="cityEntry_T1" fill={COLORS.cityEntry} name={`${t("similar-consumer-city")} T1`} opacity={0.7} />
                <Bar dataKey="cityEntry_T2" fill={COLORS.cityEntry} name={`${t("similar-consumer-city")} T2`} opacity={0.7} />
                <Bar dataKey="cityEntry_T3" fill={COLORS.cityEntry} name={`${t("similar-consumer-city")} T3`} opacity={0.7} />
              </>
            )}
          </>
        )}

        {props.compare?.districtEntry && (
          <>
            <Bar
              dataKey="districtEntry_T0"
              fill={COLORS.districtEntry}
              name={showThreeTime ? `${t("similar-consumer-district")} T0` : t("similar-consumer-district")}
              opacity={0.7}
            />
            {showThreeTime && (
              <>
                <Bar dataKey="districtEntry_T1" fill={COLORS.districtEntry} name={`${t("similar-consumer-district")} T1`} opacity={0.7} />
                <Bar dataKey="districtEntry_T2" fill={COLORS.districtEntry} name={`${t("similar-consumer-district")} T2`} opacity={0.7} />
                <Bar dataKey="districtEntry_T3" fill={COLORS.districtEntry} name={`${t("similar-consumer-district")} T3`} opacity={0.7} />
              </>
            )}
          </>
        )}
      </BarChart>
    </ChartContainer>
  );
}
