
import { useTranslation } from "react-i18next";
import { useDateLocale } from "../../hooks/use-date-locale";
import { ChartProps } from "./chart";
import { getDateFunctionsForGranularity } from "./utils";

const NUMBER_FORMAT = new Intl.NumberFormat("es-ES", {
  useGrouping: true,
  maximumFractionDigits: 2
});

export function BarDataChart(props: ChartProps) {
  const { t } = useTranslation("subscriptions");
  const dateLocale = useDateLocale();
  const { label } = getDateFunctionsForGranularity(
    props.granularity,
    dateLocale
  );

  return null
}
