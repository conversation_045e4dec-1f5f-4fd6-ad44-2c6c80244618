
"use client";

import { useTranslation } from "react-i18next";
import { useDateLocale } from "../../hooks/use-date-locale";
import { ChartProps } from "./chart";
import { getDateFunctionsForGranularity, zeroFill } from "./utils";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  ChartConfig,
} from "../ui/chart";
import { useMemo } from "react";

const NUMBER_FORMAT = new Intl.NumberFormat("es-ES", {
  useGrouping: true,
  maximumFractionDigits: 2
});

// Color scheme for different data series
const COLORS = {
  T0: "#0BA5EC",
  T1: "#0BA5EC", // Same base color as T0
  T2: "#0BA5EC", // Same base color as T0
  T3: "#0BA5EC", // Same base color as T0
  average: "#8B5CF6",
  cityEntry: "#F59E0B",
  districtEntry: "#10B981"
};

// Opacity levels for stacked bars (dark to light)
const T_OPACITIES = {
  T1: 1.0,    // Darkest
  T2: 0.7,    // Medium
  T3: 0.4,    // Lightest
};

interface BarChartDataPoint {
  timeframe: string;
  formattedDate: string;
  T0?: number;  // Optional - only used in single-time mode
  T1?: number;  // Optional - only used in three-time mode
  T2?: number;  // Optional - only used in three-time mode
  T3?: number;  // Optional - only used in three-time mode
  average_T0?: number;
  average_T1?: number;
  average_T2?: number;
  average_T3?: number;
  cityEntry_T0?: number;
  cityEntry_T1?: number;
  cityEntry_T2?: number;
  cityEntry_T3?: number;
  districtEntry_T0?: number;
  districtEntry_T1?: number;
  districtEntry_T2?: number;
  districtEntry_T3?: number;
}

export function BarDataChart(props: ChartProps) {
  const { t } = useTranslation("subscriptions");
  const dateLocale = useDateLocale();
  const { label } = getDateFunctionsForGranularity(
    props.granularity,
    dateLocale
  );

  const { chartData } = zeroFill(
    props.data,
    props.compare,
    props.startDate,
    props.endDate,
    props.granularity,
    dateLocale
  );

  // Determine which time periods to show
  const showThreeTime = props.threeTime && props.granularity !== "hour" &&
    chartData.some(d => d.data.T1 !== null && d.data.T2 !== null && d.data.T3 !== null);

  // Transform data for the bar chart
  const transformedData: BarChartDataPoint[] = useMemo(() => {
    const baseTransformed = chartData.map((point) => {
      const baseData: BarChartDataPoint = {
        timeframe: point.timeframe.toISOString(),
        formattedDate: label(point.timeframe),
      };

      if (showThreeTime) {
        // Three-time mode: only include T1, T2, T3
        if (point.data.T1 !== null) baseData.T1 = point.data.T1;
        if (point.data.T2 !== null) baseData.T2 = point.data.T2;
        if (point.data.T3 !== null) baseData.T3 = point.data.T3;
      } else {
        // Single-time mode: only include T0
        baseData.T0 = point.data.T0;
      }

      return baseData;
    });

    // Add comparison data as constant reference lines across all data points
    if (props.compare && baseTransformed.length > 0) {
      return baseTransformed.map((point) => {
        const enhanced = { ...point };

        if (props.compare!.average) {
          if (showThreeTime) {
            // Three-time mode: add T1, T2, T3 comparison data
            if (props.compare!.average.T1 !== null) enhanced.average_T1 = props.compare!.average.T1;
            if (props.compare!.average.T2 !== null) enhanced.average_T2 = props.compare!.average.T2;
            if (props.compare!.average.T3 !== null) enhanced.average_T3 = props.compare!.average.T3;
          } else {
            // Single-time mode: add T0 comparison data
            enhanced.average_T0 = props.compare!.average.T0;
          }
        }

        if (props.compare!.cityEntry) {
          if (showThreeTime) {
            // Three-time mode: add T1, T2, T3 comparison data
            if (props.compare!.cityEntry.T1 !== null) enhanced.cityEntry_T1 = props.compare!.cityEntry.T1;
            if (props.compare!.cityEntry.T2 !== null) enhanced.cityEntry_T2 = props.compare!.cityEntry.T2;
            if (props.compare!.cityEntry.T3 !== null) enhanced.cityEntry_T3 = props.compare!.cityEntry.T3;
          } else {
            // Single-time mode: add T0 comparison data
            enhanced.cityEntry_T0 = props.compare!.cityEntry.T0;
          }
        }

        if (props.compare!.districtEntry) {
          if (showThreeTime) {
            // Three-time mode: add T1, T2, T3 comparison data
            if (props.compare!.districtEntry.T1 !== null) enhanced.districtEntry_T1 = props.compare!.districtEntry.T1;
            if (props.compare!.districtEntry.T2 !== null) enhanced.districtEntry_T2 = props.compare!.districtEntry.T2;
            if (props.compare!.districtEntry.T3 !== null) enhanced.districtEntry_T3 = props.compare!.districtEntry.T3;
          } else {
            // Single-time mode: add T0 comparison data
            enhanced.districtEntry_T0 = props.compare!.districtEntry.T0;
          }
        }

        return enhanced;
      });
    }

    return baseTransformed;
  }, [chartData, props.compare, props.threeTime, props.granularity, label]);


  // Chart configuration for styling
  const chartConfig: ChartConfig = useMemo(() => {
    const config: ChartConfig = {};

    if (showThreeTime) {
      // Three-time mode: only T1, T2, T3
      config.T1 = { label: "T1", color: COLORS.T1 };
      config.T2 = { label: "T2", color: COLORS.T2 };
      config.T3 = { label: "T3", color: COLORS.T3 };
    } else {
      // Single-time mode: only T0
      config.T0 = { label: t("usage.usage_chart_label"), color: COLORS.T0 };
    }

    // Add comparison data to config
    if (props.compare) {
      if (props.compare.average) {
        if (showThreeTime) {
          config.average_T1 = { label: `${t("average")} T1`, color: COLORS.average };
          config.average_T2 = { label: `${t("average")} T2`, color: COLORS.average };
          config.average_T3 = { label: `${t("average")} T3`, color: COLORS.average };
        } else {
          config.average_T0 = { label: t("average"), color: COLORS.average };
        }
      }

      if (props.compare.cityEntry) {
        if (showThreeTime) {
          config.cityEntry_T1 = { label: `${t("similar-consumer-city")} T1`, color: COLORS.cityEntry };
          config.cityEntry_T2 = { label: `${t("similar-consumer-city")} T2`, color: COLORS.cityEntry };
          config.cityEntry_T3 = { label: `${t("similar-consumer-city")} T3`, color: COLORS.cityEntry };
        } else {
          config.cityEntry_T0 = { label: t("similar-consumer-city"), color: COLORS.cityEntry };
        }
      }

      if (props.compare.districtEntry) {
        if (showThreeTime) {
          config.districtEntry_T1 = { label: `${t("similar-consumer-district")} T1`, color: COLORS.districtEntry };
          config.districtEntry_T2 = { label: `${t("similar-consumer-district")} T2`, color: COLORS.districtEntry };
          config.districtEntry_T3 = { label: `${t("similar-consumer-district")} T3`, color: COLORS.districtEntry };
        } else {
          config.districtEntry_T0 = { label: t("similar-consumer-district"), color: COLORS.districtEntry };
        }
      }
    }

    return config;
  }, [showThreeTime, props.compare, t]);

  if (!transformedData.length) {
    return (
      <div className="flex h-[350px] items-center justify-center text-muted-foreground">
        {t("no-data-available")}
      </div>
    );
  }

  return (
    <ChartContainer config={chartConfig} className="h-[350px] w-full">
      <BarChart
        data={transformedData}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="formattedDate"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          interval={0}
          angle={-45}
          textAnchor="end"
          height={60}
        />
        <YAxis
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${NUMBER_FORMAT.format(value)}`}
          label={{ value: props.unit, angle: -90, position: 'insideLeft' }}
        />
        <ChartTooltip
          content={
            <ChartTooltipContent
              dataFormatter={(value) => `${NUMBER_FORMAT.format(Number(value))} ${props.unit}`}
              labelFormatter={(label) => label}
            />
          }
        />
        <ChartLegend content={<ChartLegendContent />} />

        {/* Usage data bars */}
        {showThreeTime ? (
          // Stacked bars for three-time mode (T1, T2, T3 only)
          <>
            <Bar
              dataKey="T1"
              fill={COLORS.T1}
              fillOpacity={T_OPACITIES.T1}
              name="T1"
              stackId="usage"
            />
            <Bar
              dataKey="T2"
              fill={COLORS.T2}
              fillOpacity={T_OPACITIES.T2}
              name="T2"
              stackId="usage"
            />
            <Bar
              dataKey="T3"
              fill={COLORS.T3}
              fillOpacity={T_OPACITIES.T3}
              name="T3"
              stackId="usage"
            />
          </>
        ) : (
          // Single bar for single-time mode (T0 only)
          <Bar
            dataKey="T0"
            fill={COLORS.T0}
            name={t("usage.usage_chart_label")}
          />
        )}

        {/* Comparison data bars */}
        {props.compare?.average && (
          <>
            {showThreeTime ? (
              // Stacked comparison bars for three-time mode
              <>
                <Bar
                  dataKey="average_T1"
                  fill={COLORS.average}
                  fillOpacity={T_OPACITIES.T1 * 0.7}
                  name={`${t("average")} T1`}
                  stackId="average"
                />
                <Bar
                  dataKey="average_T2"
                  fill={COLORS.average}
                  fillOpacity={T_OPACITIES.T2 * 0.7}
                  name={`${t("average")} T2`}
                  stackId="average"
                />
                <Bar
                  dataKey="average_T3"
                  fill={COLORS.average}
                  fillOpacity={T_OPACITIES.T3 * 0.7}
                  name={`${t("average")} T3`}
                  stackId="average"
                />
              </>
            ) : (
              // Single comparison bar for single-time mode
              <Bar
                dataKey="average_T0"
                fill={COLORS.average}
                name={t("average")}
                opacity={0.7}
              />
            )}
          </>
        )}

        {props.compare?.cityEntry && (
          <>
            {showThreeTime ? (
              // Stacked comparison bars for three-time mode
              <>
                <Bar
                  dataKey="cityEntry_T1"
                  fill={COLORS.cityEntry}
                  fillOpacity={T_OPACITIES.T1 * 0.7}
                  name={`${t("similar-consumer-city")} T1`}
                  stackId="cityEntry"
                />
                <Bar
                  dataKey="cityEntry_T2"
                  fill={COLORS.cityEntry}
                  fillOpacity={T_OPACITIES.T2 * 0.7}
                  name={`${t("similar-consumer-city")} T2`}
                  stackId="cityEntry"
                />
                <Bar
                  dataKey="cityEntry_T3"
                  fill={COLORS.cityEntry}
                  fillOpacity={T_OPACITIES.T3 * 0.7}
                  name={`${t("similar-consumer-city")} T3`}
                  stackId="cityEntry"
                />
              </>
            ) : (
              // Single comparison bar for single-time mode
              <Bar
                dataKey="cityEntry_T0"
                fill={COLORS.cityEntry}
                name={t("similar-consumer-city")}
                opacity={0.7}
              />
            )}
          </>
        )}

        {props.compare?.districtEntry && (
          <>
            {showThreeTime ? (
              // Stacked comparison bars for three-time mode
              <>
                <Bar
                  dataKey="districtEntry_T1"
                  fill={COLORS.districtEntry}
                  fillOpacity={T_OPACITIES.T1 * 0.7}
                  name={`${t("similar-consumer-district")} T1`}
                  stackId="districtEntry"
                />
                <Bar
                  dataKey="districtEntry_T2"
                  fill={COLORS.districtEntry}
                  fillOpacity={T_OPACITIES.T2 * 0.7}
                  name={`${t("similar-consumer-district")} T2`}
                  stackId="districtEntry"
                />
                <Bar
                  dataKey="districtEntry_T3"
                  fill={COLORS.districtEntry}
                  fillOpacity={T_OPACITIES.T3 * 0.7}
                  name={`${t("similar-consumer-district")} T3`}
                  stackId="districtEntry"
                />
              </>
            ) : (
              // Single comparison bar for single-time mode
              <Bar
                dataKey="districtEntry_T0"
                fill={COLORS.districtEntry}
                name={t("similar-consumer-district")}
                opacity={0.7}
              />
            )}
          </>
        )}
      </BarChart>
    </ChartContainer>
  );
}
