
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from "../../components/ui/chart";
import { ChartData, ChartProps } from "./chart";
import { useTranslation } from "react-i18next";
export function BarDataChart(props: ChartProps) {
  const { t } = useTranslation("subscriptions");

  // Use data directly from props
  const chartData = props.data;

  const isThreeTime = (data: ChartData) =>
    props.threeTime &&
    props.granularity !== "hour" &&
    data.T1 !== null &&
    data.T2 !== null &&
    data.T3 !== null;

  const threeTime = chartData.some((d) => isThreeTime(d.data));

  const chartConfig = {
    T0: {
      label: threeTime ? "T0" : t("usage.usage_chart_label"),
    },
    T1: {
      label: "T1",
    },
    T2: {
      label: "T2",
    },
    T3: {
      label: "T3",
    },
    average_T0: {
      label: threeTime ? `${t("average")} T0` : t("average"),
    },
    average_T1: {
      label: `${t("average")} T1`,
    },
    average_T2: {
      label: `${t("average")} T2`,
    },
    average_T3: {
      label: `${t("average")} T3`,
    },
    cityEntry_T0: {
      label: threeTime ? `${t("similar-consumer-city")} T0` : t("similar-consumer-city"),
    },
    cityEntry_T1: {
      label: `${t("similar-consumer-city")} T1`,
    },
    cityEntry_T2: {
      label: `${t("similar-consumer-city")} T2`,
    },
    cityEntry_T3: {
      label: `${t("similar-consumer-city")} T3`,
    },
    districtEntry_T0: {
      label: threeTime ? `${t("similar-consumer-district")} T0` : t("similar-consumer-district"),
    },
    districtEntry_T1: {
      label: `${t("similar-consumer-district")} T1`,
    },
    districtEntry_T2: {
      label: `${t("similar-consumer-district")} T2`,
    },
    districtEntry_T3: {
      label: `${t("similar-consumer-district")} T3`,
    },
  } satisfies ChartConfig;

  const colorGroups = [
    ["#7CD4FD", "#36BFFA", "#0BA5EC"],
    ["#FDE272", "#FAC515", "#EAAA08"],
    ["#A6EF67", "#85E13A", "#66C61C"],
    ["#EEAAFD", "#E478FA", "#D444F1"],
    ["#FEA3B4", "#FD6F8E", "#F63D68"],
  ];

  // Create comparison data from props
  const comparisonData = [];
  if (props.compare?.average) {
    comparisonData.push({ key: "average", data: props.compare.average });
  }
  if (props.compare?.cityEntry) {
    comparisonData.push({ key: "cityEntry", data: props.compare.cityEntry });
  }
  if (props.compare?.districtEntry) {
    comparisonData.push({ key: "districtEntry", data: props.compare.districtEntry });
  }

  // Transform data to include both usage and comparison data for each time point
  const chartDisplayData = chartData.map((dataPoint) => {
    const transformedPoint: any = {
      timeframe: dataPoint.timeframe,
      formattedDate: dataPoint.timeframe.toLocaleDateString(),
      // Usage data
      T0: dataPoint.data.T0,
    };

    // Add T1, T2, T3 if in three-time mode
    if (threeTime) {
      transformedPoint.T1 = dataPoint.data.T1;
      transformedPoint.T2 = dataPoint.data.T2;
      transformedPoint.T3 = dataPoint.data.T3;
    }

    // Add comparison data (same for all time points)
    if (props.compare?.average) {
      transformedPoint.average_T0 = props.compare.average.T0;
      if (threeTime) {
        transformedPoint.average_T1 = props.compare.average.T1;
        transformedPoint.average_T2 = props.compare.average.T2;
        transformedPoint.average_T3 = props.compare.average.T3;
      }
    }

    if (props.compare?.cityEntry) {
      transformedPoint.cityEntry_T0 = props.compare.cityEntry.T0;
      if (threeTime) {
        transformedPoint.cityEntry_T1 = props.compare.cityEntry.T1;
        transformedPoint.cityEntry_T2 = props.compare.cityEntry.T2;
        transformedPoint.cityEntry_T3 = props.compare.cityEntry.T3;
      }
    }

    if (props.compare?.districtEntry) {
      transformedPoint.districtEntry_T0 = props.compare.districtEntry.T0;
      if (threeTime) {
        transformedPoint.districtEntry_T1 = props.compare.districtEntry.T1;
        transformedPoint.districtEntry_T2 = props.compare.districtEntry.T2;
        transformedPoint.districtEntry_T3 = props.compare.districtEntry.T3;
      }
    }

    return transformedPoint;
  });

  return (
    <ChartContainer config={chartConfig} className="h-full">
      <BarChart
        accessibilityLayer
        data={chartDisplayData}
      >
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey="formattedDate"
          tickMargin={10}
          fontSize={12}
          tickLine={false}
          axisLine={false}
          interval={0}
          angle={-45}
          textAnchor="end"
          height={60}
        />
        <YAxis
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => value.toLocaleString()}
        />
        <ChartTooltip
          cursor={false}
          content={({ payload, label: _label }) => {
            if (!payload || payload.length === 0) return null;
            return (
              <div className="bg-white p-3 rounded-lg shadow-lg border">
                <div className="font-medium mb-2">
                  {_label}
                </div>
                <div className="flex flex-col gap-1">
                  {payload.map((entry) => (
                    <div
                      key={entry.dataKey}
                      className="flex items-center text-sm"
                    >
                      <div
                        className="w-3 h-3 rounded mr-2"
                        style={{ backgroundColor: entry.color }}
                      />
                      <span className="flex-1">
                        {entry.name}: {(entry.value ?? 0).toLocaleString()} {props.unit}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            );
          }}
        />
        {/* Usage data bars */}
        {threeTime ? (
          // Stacked bars for three-time mode (T1, T2, T3 only)
          <>
            <Bar
              dataKey="T1"
              fill={colorGroups[0][2]}
              name="T1"
              stackId="usage"
            />
            <Bar
              dataKey="T2"
              fill={colorGroups[0][1]}
              name="T2"
              stackId="usage"
            />
            <Bar
              dataKey="T3"
              fill={colorGroups[0][0]}
              name="T3"
              stackId="usage"
            />
          </>
        ) : (
          // Single bar for single-time mode (T0 only)
          <Bar
            dataKey="T0"
            fill={colorGroups[0][2]}
            name={t("usage.usage_chart_label")}
          />
        )}

        {/* Comparison data bars */}
        {props.compare?.average && (
          <>
            {threeTime ? (
              // Stacked comparison bars for three-time mode
              <>
                <Bar
                  dataKey="average_T1"
                  fill={colorGroups[1][2]}
                  name={`${t("average")} T1`}
                  stackId="average"
                />
                <Bar
                  dataKey="average_T2"
                  fill={colorGroups[1][1]}
                  name={`${t("average")} T2`}
                  stackId="average"
                />
                <Bar
                  dataKey="average_T3"
                  fill={colorGroups[1][0]}
                  name={`${t("average")} T3`}
                  stackId="average"
                />
              </>
            ) : (
              // Single comparison bar for single-time mode
              <Bar
                dataKey="average_T0"
                fill={colorGroups[1][2]}
                name={t("average")}
              />
            )}
          </>
        )}

        {props.compare?.cityEntry && (
          <>
            {threeTime ? (
              // Stacked comparison bars for three-time mode
              <>
                <Bar
                  dataKey="cityEntry_T1"
                  fill={colorGroups[2][2]}
                  name={`${t("similar-consumer-city")} T1`}
                  stackId="cityEntry"
                />
                <Bar
                  dataKey="cityEntry_T2"
                  fill={colorGroups[2][1]}
                  name={`${t("similar-consumer-city")} T2`}
                  stackId="cityEntry"
                />
                <Bar
                  dataKey="cityEntry_T3"
                  fill={colorGroups[2][0]}
                  name={`${t("similar-consumer-city")} T3`}
                  stackId="cityEntry"
                />
              </>
            ) : (
              // Single comparison bar for single-time mode
              <Bar
                dataKey="cityEntry_T0"
                fill={colorGroups[2][2]}
                name={t("similar-consumer-city")}
              />
            )}
          </>
        )}

        {props.compare?.districtEntry && (
          <>
            {threeTime ? (
              // Stacked comparison bars for three-time mode
              <>
                <Bar
                  dataKey="districtEntry_T1"
                  fill={colorGroups[3][2]}
                  name={`${t("similar-consumer-district")} T1`}
                  stackId="districtEntry"
                />
                <Bar
                  dataKey="districtEntry_T2"
                  fill={colorGroups[3][1]}
                  name={`${t("similar-consumer-district")} T2`}
                  stackId="districtEntry"
                />
                <Bar
                  dataKey="districtEntry_T3"
                  fill={colorGroups[3][0]}
                  name={`${t("similar-consumer-district")} T3`}
                  stackId="districtEntry"
                />
              </>
            ) : (
              // Single comparison bar for single-time mode
              <Bar
                dataKey="districtEntry_T0"
                fill={colorGroups[3][2]}
                name={t("similar-consumer-district")}
              />
            )}
          </>
        )}
      </BarChart>
    </ChartContainer>
  );
}
