# Energy Usage Bar Chart Component

## Overview

The `BarDataChart` component is a reusable React component built with Recharts that displays energy usage data with comparison capabilities. It supports time-series data visualization with T0-T3 energy categories and includes comparison data from averages, city entries, and district entries.

## Features

- **Time-series visualization**: Display energy usage data across different timeframes
- **Multi-category support**: Show T0, T1, T2, T3 energy categories
- **Comparison data**: Include average, city, and district comparison data
- **Responsive design**: Automatically adapts to container size
- **Internationalization**: Supports multiple languages through react-i18next
- **Customizable styling**: Uses the existing chart UI system with consistent theming
- **Accessibility**: Includes proper labels, tooltips, and legends

## Usage

```tsx
import { BarData<PERSON>hart } from "./shared/components/charts/bar";
import { ChartProps } from "./shared/components/charts/chart";

const chartProps: ChartProps = {
  data: [
    {
      timeframe: new Date("2024-01-01"),
      data: {
        T0: 120.5,
        T1: 85.2,
        T2: 95.8,
        T3: 110.3,
      },
    },
    // ... more data points
  ],
  compare: {
    average: { T0: 115.0, T1: 85.0, T2: 95.0, T3: 108.0 },
    cityEntry: { T0: 125.0, T1: 90.0, T2: 100.0, T3: 112.0 },
    districtEntry: { T0: 110.0, T1: 80.0, T2: 90.0, T3: 105.0 },
  },
  granularity: "day",
  compareTo: "average",
  startDate: new Date("2024-01-01"),
  endDate: new Date("2024-01-31"),
  unit: "kWh",
  threeTime: true,
};

<BarDataChart {...chartProps} />
```

## Props

The component accepts a `ChartProps` interface with the following properties:

### Required Props

- `data: ChartDataPoint[]` - Array of usage data points
- `startDate: Date` - Start date for the chart
- `endDate: Date` - End date for the chart
- `unit: string` - Unit of measurement (e.g., "kWh")
- `granularity: string` - Time granularity ("hour", "day", "month", "year")
- `threeTime: boolean` - Whether to show T1, T2, T3 categories

### Optional Props

- `compare: ChartCompare | null` - Comparison data
- `compareTo: string | null` - Type of comparison being made

### Data Structures

```typescript
interface ChartDataPoint {
  timeframe: Date;
  data: ChartData;
}

interface ChartData {
  T0: number;
  T1: number | null;
  T2: number | null;
  T3: number | null;
}

interface ChartCompare {
  average: ChartData | null;
  cityEntry: ChartData | null;
  districtEntry: ChartData | null;
}
```

## Styling

The component uses a predefined color scheme:

- **T0**: `#0BA5EC` (Blue)
- **T1**: `#EAAA08` (Yellow)
- **T2**: `#66C61C` (Green)
- **T3**: `#D444F1` (Purple)
- **Average**: `#8B5CF6` (Violet)
- **City Entry**: `#F59E0B` (Orange)
- **District Entry**: `#10B981` (Emerald)

Comparison data is displayed with 70% opacity to distinguish from usage data.

## Behavior

- **Three-time mode**: When `threeTime` is true and granularity is not "hour", shows T1, T2, T3 categories
- **Comparison data**: Displays comparison bars alongside usage data when available
- **Responsive tooltips**: Shows formatted values with units
- **Date formatting**: Automatically formats dates based on granularity and locale
- **Empty state**: Shows a "no data available" message when no data is present

## Dependencies

- React 19+
- Recharts 2.14+
- react-i18next
- date-fns
- Existing chart UI components from `../ui/chart`
