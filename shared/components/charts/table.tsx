"use client";

import { ChartData, ChartDataPoint, ChartProps } from "./chart";
import { useTranslation } from "react-i18next";
import { getDateFunctionsForGranularity, zeroFill } from "./utils";
import { useDateLocale } from "../../hooks/use-date-locale";
import { DataTable } from "../../components/organisms/table";
import { ColumnDef, Row } from "@tanstack/react-table";


const NUMBER_FORMAT = new Intl.NumberFormat("es-ES", {
  useGrouping: true,
  maximumFractionDigits: 2
});

// great name. honestly.
export function TableChart(props: ChartProps) {
  const { t } = useTranslation("subscriptions");
  const dateLocale = useDateLocale();
  const { label } = getDateFunctionsForGranularity(
    props.granularity,
    dateLocale
  );

  const { chartData, compareChartData } = zeroFill(
    props.data,
    props.compare,
    props.startDate,
    props.endDate,
    props.granularity,
    dateLocale
  );

  const comparing = props.compare && Object.keys(props.compare).length > 0;

  const isThreeTime = (data: ChartData) =>
    props.threeTime &&
    props.granularity !== "hour" &&
    data.T1 !== null &&
    data.T2 !== null &&
    data.T3 !== null;

  const threeTime = chartData.some((d) => isThreeTime(d.data));

  const compareMapping = {
    usage: "usage",
    average: "average",
    cityEntry: "similar-consumer-city",
    districtEntry: "similar-consumer-district",
  };

  const threeCols = (threeTime ? ["T0", "T1", "T2", "T3"] : ["T0"]).map(
    (term) => ({
      accessorKey: "data",
      enableSorting: false,
      header: () => {
        return (
          <div className="flex items-center gap-3 pl-2">
            <span className="font-medium">
              {term === "T0" ? t("usage.usage_chart_label") : `${term} (kWh)`}
            </span>
          </div>
        );
      },
      // todo: any
      cell: ({ row }: any) => {
        return (
          <div className="flex items-start gap-3 pl-2">
            <div className="flex flex-col">
              {NUMBER_FORMAT.format(+(row.getValue("data") as any)[term])}
            </div>
          </div>
        );
      },
    })
  );

  const columns: ColumnDef<ChartDataPoint, unknown>[] = comparing
    ? [
        {
          accessorKey: "key",
          enableSorting: false,
          header: () => {
            return (
              <div className="flex items-center gap-3 pl-2">
                <span className="font-medium">{t("usage.data_type")}</span>
              </div>
            );
          },
          cell: ({ row }) => {
            return (
              <div className="flex items-start gap-3 pl-2">
                <div className="flex flex-col">
                  {t(
                    "usage.compare." +
                      compareMapping[
                        row.getValue("key") as keyof typeof compareMapping
                      ]
                  )}
                </div>
              </div>
            );
          },
        },
        ...threeCols,
      ]
    : [
        {
          accessorKey: "timeframe",
          enableSorting: false,
          header: () => {
            return (
              <div className="flex items-center gap-3 pl-2">
                <span className="font-medium">{t("usage.table_date")}</span>
              </div>
            );
          },
          cell: ({ row }) => {
            return (
              <div className="flex items-start gap-3 pl-2">
                <div className="flex flex-col">
                  {label(row.getValue("timeframe"))}
                </div>
              </div>
            );
          },
        },
        ...threeCols,
      ];

  return (
    <DataTable
      columns={columns}
      data={(comparing ? compareChartData : chartData) as any} //todo: any
      meta={{ t }}
      roundless
      className="w-full border rounded-2xl shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)] overflow-hidden"
    />
  );
}
