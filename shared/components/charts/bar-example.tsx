// Example usage of the BarDataChart component
import { BarDataChart } from "./bar";
import { ChartProps } from "./chart";

// Sample data for demonstration
const sampleUsageData = [
  {
    timeframe: new Date("2024-01-01"),
    data: {
      T0: 120.5,
      T1: 85.2,
      T2: 95.8,
      T3: 110.3,
    },
  },
  {
    timeframe: new Date("2024-01-02"),
    data: {
      T0: 135.7,
      T1: 92.4,
      T2: 88.1,
      T3: 105.9,
    },
  },
  {
    timeframe: new Date("2024-01-03"),
    data: {
      T0: 98.3,
      T1: 78.6,
      T2: 102.4,
      T3: 115.2,
    },
  },
];

const sampleCompareData = {
  average: {
    T0: 115.0,
    T1: 85.0,
    T2: 95.0,
    T3: 108.0,
  },
  cityEntry: {
    T0: 125.0,
    T1: 90.0,
    T2: 100.0,
    T3: 112.0,
  },
  districtEntry: {
    T0: 110.0,
    T1: 80.0,
    T2: 90.0,
    T3: 105.0,
  },
};

export function BarChartExample() {
  const chartProps: ChartProps = {
    data: sampleUsageData,
    compare: sampleCompareData,
    granularity: "day",
    compareTo: "average",
    startDate: new Date("2024-01-01"),
    endDate: new Date("2024-01-03"),
    unit: "kWh",
    threeTime: true,
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">Energy Usage Bar Chart</h2>
      <BarDataChart {...chartProps} />
    </div>
  );
}
