import { render, screen } from "@testing-library/react";
import { BarDataChart } from "./bar";
import { ChartProps } from "./chart";

// Mock react-i18next
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        "usage.usage_chart_label": "Energy Usage",
        "average": "Average",
        "similar-consumer-city": "City Average",
        "similar-consumer-district": "District Average",
        "no-data-available": "No data available",
      };
      return translations[key] || key;
    },
  }),
}));

// Mock date locale hook
jest.mock("../../hooks/use-date-locale", () => ({
  useDateLocale: () => ({ code: "en" }),
}));

// Mock Recharts components
jest.mock("recharts", () => ({
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: ({ dataKey, name }: any) => <div data-testid={`bar-${dataKey}`}>{name}</div>,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
}));

const mockChartProps: ChartProps = {
  data: [
    {
      timeframe: new Date("2024-01-01"),
      data: {
        T0: 120.5,
        T1: 85.2,
        T2: 95.8,
        T3: 110.3,
      },
    },
    {
      timeframe: new Date("2024-01-02"),
      data: {
        T0: 135.7,
        T1: 92.4,
        T2: 88.1,
        T3: 105.9,
      },
    },
  ],
  compare: {
    average: {
      T0: 115.0,
      T1: 85.0,
      T2: 95.0,
      T3: 108.0,
    },
    cityEntry: {
      T0: 125.0,
      T1: 90.0,
      T2: 100.0,
      T3: 112.0,
    },
    districtEntry: {
      T0: 110.0,
      T1: 80.0,
      T2: 90.0,
      T3: 105.0,
    },
  },
  granularity: "day",
  compareTo: "average",
  startDate: new Date("2024-01-01"),
  endDate: new Date("2024-01-02"),
  unit: "kWh",
  threeTime: true,
};

describe("BarDataChart", () => {
  it("renders chart with three-time mode", () => {
    render(<BarDataChart {...mockChartProps} />);
    
    expect(screen.getByTestId("bar-chart")).toBeInTheDocument();
    expect(screen.getByTestId("bar-T0")).toBeInTheDocument();
    expect(screen.getByTestId("bar-T1")).toBeInTheDocument();
    expect(screen.getByTestId("bar-T2")).toBeInTheDocument();
    expect(screen.getByTestId("bar-T3")).toBeInTheDocument();
  });

  it("renders chart with single-time mode", () => {
    const singleTimeProps = { ...mockChartProps, threeTime: false };
    render(<BarDataChart {...singleTimeProps} />);
    
    expect(screen.getByTestId("bar-chart")).toBeInTheDocument();
    expect(screen.getByTestId("bar-T0")).toBeInTheDocument();
    expect(screen.queryByTestId("bar-T1")).not.toBeInTheDocument();
    expect(screen.queryByTestId("bar-T2")).not.toBeInTheDocument();
    expect(screen.queryByTestId("bar-T3")).not.toBeInTheDocument();
  });

  it("renders comparison data when available", () => {
    render(<BarDataChart {...mockChartProps} />);
    
    expect(screen.getByTestId("bar-average_T0")).toBeInTheDocument();
    expect(screen.getByTestId("bar-cityEntry_T0")).toBeInTheDocument();
    expect(screen.getByTestId("bar-districtEntry_T0")).toBeInTheDocument();
  });

  it("shows no data message when data is empty", () => {
    const emptyProps = { ...mockChartProps, data: [] };
    render(<BarDataChart {...emptyProps} />);
    
    expect(screen.getByText("No data available")).toBeInTheDocument();
  });
});
