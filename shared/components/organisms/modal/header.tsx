import { DialogDescription, DialogHeader, DialogTitle } from '../../ui/dialog'
import { DrawerDescription, DrawerHeader, DrawerTitle } from '../../ui/drawer'
import useMediaQuery from '../../../hooks/use-media-query'

interface Props {
    title?: React.ReactNode
    description?: string
}

function ModalHeader({ title, description }: Props) {
    const isDesktop = useMediaQuery('(min-width: 768px)')

    if (isDesktop) {
        return (
            <DialogHeader className={`p-6 pb-4 ${(title || description) ? 'border-gray-200 border-b' : ''}`}>
                <DialogTitle>
                    {title}
                </DialogTitle>
                <DialogDescription>
                    {description}
                </DialogDescription>
            </DialogHeader>
        )
    }

    return (
        <DrawerHeader className={`p-6 pb-4 ${(title || description) ? 'border-gray-200 border-b' : ''}`}>
            <DrawerTitle>
                {title}
            </DrawerTitle>
            <DrawerDescription>
                {description}
            </DrawerDescription>
        </DrawerHeader>
    )
}

export default ModalHeader
