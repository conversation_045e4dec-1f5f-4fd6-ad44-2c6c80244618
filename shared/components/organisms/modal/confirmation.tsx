import { IconNames } from '@/components/atoms/Iconify'
import useMediaQuery from '../../../hooks/use-media-query'
import FeaturedIcon from '../../atoms/featured-icon'
import { Button } from '../../ui/button'
import { Dialog, DialogContent } from '../../ui/dialog'
import { Drawer, DrawerContent } from '../../ui/drawer'
import { useTranslation } from 'react-i18next'
import { useEffect } from 'react'

export interface ConfirmationDialogProps {
    open: boolean
    onClose: () => void
    onConfirm: () => void
    title?: string
    description?: string
    confirmText?: string
    cancelText?: string
    iconName?: string
}

export function ConfirmationDialog({
    open,
    onClose,
    onConfirm,
    title,
    description, 
    confirmText,
    cancelText,
    iconName = 'untitled:alert-triangle'
}: ConfirmationDialogProps) {
    const { t } = useTranslation('common')
    const isDesktop = useMediaQuery('(min-width: 768px)')
    
    useEffect(() => {
        const resetBodyStyle = () => {
            if (document.body.style.pointerEvents === 'none') {
                document.body.style.pointerEvents = '';
            }
        };
        
        if (!open) {
            resetBodyStyle();
        }
        
        return () => {
            resetBodyStyle();
        };
    }, [open]);
    
    const defaultTitle = t('confirmation.modal_close.title')
    const defaultDescription = t('confirmation.modal_close.description')
    const defaultConfirmText = t('confirmation.modal_close.confirm')
    const defaultCancelText = t('confirmation.modal_close.cancel')

    const content = (
        <div className="p-6 pt-4 flex flex-col gap-5">
            <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-lg text-gray-900 font-text-sm-regular">
                <FeaturedIcon name={iconName as IconNames} className="size-16" />
                <div className="self-stretch relative leading-[28px] font-semibold">
                    {title || defaultTitle}
                </div>
                <div className="self-stretch relative text-sm leading-[20px] text-gray-600">
                    {description || defaultDescription}
                </div>
            </div>

            <div className="w-full flex gap-2 items-center">
                <Button variant="outline" onClick={onClose} className="w-full">
                    {cancelText || defaultCancelText}
                </Button>
                <Button
                    variant="default"
                    className="w-full"
                    onClick={onConfirm}
                >
                    {confirmText || defaultConfirmText}
                </Button>
            </div>
        </div>
    )

    if (isDesktop) {
        return (
            <Dialog open={open} onOpenChange={(open) => {
                if (!open) {
                    if (document.body.style.pointerEvents === 'none') {
                        document.body.style.pointerEvents = '';
                    }
                    onClose();
                }
            }}>
                <DialogContent 
                    className="p-0 sm:max-w-[425px]"
                    onInteractOutside={() => {
                        if (document.body.style.pointerEvents === 'none') {
                            document.body.style.pointerEvents = '';
                        }
                    }}
                >
                    {content}
                </DialogContent>
            </Dialog>
        )
    }

    return (
        <Drawer open={open} onOpenChange={(open) => {
            if (!open) {
                if (document.body.style.pointerEvents === 'none') {
                    document.body.style.pointerEvents = '';
                }
                onClose();
            }
        }}>
            <DrawerContent 
                className="p-0"
                onInteractOutside={() => {
                    if (document.body.style.pointerEvents === 'none') {
                        document.body.style.pointerEvents = '';
                    }
                }}
            >
                {content}
            </DrawerContent>
        </Drawer>
    )
}