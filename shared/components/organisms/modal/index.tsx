import { useEffect, useRef } from 'react'
import {
    Dialog,
    DialogContent,
} from '../../ui/dialog'

import { Drawer, DrawerContent } from '../../ui/drawer'

import useMediaQuery from '../../../hooks/use-media-query'
import ModalHeader from './header'
import { useModal } from './provider'

export function ModalContainer() {
    const { modals, remove, safeTryClose, patch } = useModal()
    const isDesktop = useMediaQuery('(min-width: 768px)')
    const dialogRef = useRef<HTMLDivElement>(null)
    
    useEffect(() => {
        if (modals.length > 0) {
            modals.forEach(modal => {
                const needsPatching = 
                    modal.props.confirmClose === undefined && 
                    modal.props.alwaysConfirmClose === undefined &&
                    !modal.props.confirmationProps;
                
                if (needsPatching) {
                    patch({
                        name: modal.name,

                        confirmationProps: {
                            title: "Kapatmak istediğinize emin misiniz?",
                            description: "Modalı kapatmak üzeresiniz. Devam etmek istiyor musunuz?",
                            confirmText: "Evet, kapat",
                            cancelText: "Hayır, devam et",
                            iconName: "untitled:alert-triangle"
                        }
                    });
                }
            });
        }
    }, [modals, patch]);

    useEffect(() => {
        const resetBodyStyle = () => {
            if (document.body.style.pointerEvents === 'none') {
                document.body.style.pointerEvents = '';
            }
        };

        const handleVisibilityChange = () => {
            if (modals.length === 0) {
                resetBodyStyle();
            }
        };

        window.addEventListener('visibilitychange', handleVisibilityChange);
        
        if (modals.length === 0) {
            resetBodyStyle();
        }

        return () => {
            window.removeEventListener('visibilitychange', handleVisibilityChange);

            resetBodyStyle();
        };
    }, [modals.length]);

    useEffect(() => {
        function handleKeyDown(e: KeyboardEvent) {
            if (e.key === 'Escape') {
                e.stopPropagation()
                if (!modals[0]?.props.disableClose) {
                    handleCloseAll()
                }
            }
        }

        if (modals.length > 0) {
            document.addEventListener('keydown', handleKeyDown, { capture: true })
        }
        
        return () => {
            document.removeEventListener('keydown', handleKeyDown, { capture: true })
        }
    }, [modals])

    if (modals.length === 0)
        return null

    const handleCloseAll = () => {
        modals.forEach(modal => safeTryClose(modal.name))
        if (document.body.style.pointerEvents === 'none') {
            document.body.style.pointerEvents = '';
        }
    }

    const modalSize = modals[0].props.size || 'small'
    const sizeClass = modalSize === 'small' ? 'sm:max-w-[425px]' : 'sm:max-w-[900px]'
    const disableClose = !!modals[0].props.disableClose

    const content = (
        <>
            {modals.map((modal) => {
                const Comp = modal.component
                return (
                    <Comp
                        key={modal.id}
                        {...modal.props}
                        show={modal.show}
                        onHide={() => safeTryClose(modal.name)}
                    />
                )
            })}
        </>
    )

    return isDesktop ? (
        <Dialog open={true} onOpenChange={open => {
            if (!open && !disableClose) {
                document.body.style.pointerEvents = '';
                handleCloseAll();
            }
        }}>
            <DialogContent 
                ref={dialogRef} 
                className={`p-0 ${sizeClass} ${modals[0].props.overflowHidden !== false ? 'overflow-hidden' : ''}`} 
                disableClose={disableClose}
                onPointerDownOutside={e => {
                    const target = e.target as HTMLElement
                    if (target.closest('[data-radix-popper-content-wrapper]')) {
                        e.preventDefault()
                    }
                }}
                onInteractOutside={() => {
                    if (document.body.style.pointerEvents === 'none') {
                        document.body.style.pointerEvents = '';
                    }
                }}
            >
                <ModalHeader title={modals[0].props.header?.title} description={modals[0].props.header?.description} />
                {content}
            </DialogContent>
        </Dialog>
    ) : (
        <Drawer open={true} onOpenChange={open => {
            if (!open && !disableClose) {
                // Reset pointer-events style on body before closing
                document.body.style.pointerEvents = '';
                handleCloseAll();
            }
        }}>
            <DrawerContent 
                className="p-0"
                onPointerDownOutside={e => {
                    // Prevent closing when clicking inside dropdown content
                    const target = e.target as HTMLElement
                    if (target.closest('[data-radix-popper-content-wrapper]')) {
                        e.preventDefault()
                    }
                }}
                onInteractOutside={() => {
                    // Ensure pointer-events style is cleared when interacting outside
                    if (document.body.style.pointerEvents === 'none') {
                        document.body.style.pointerEvents = '';
                    }
                }}
            >
                <ModalHeader title={modals[0].props.header?.title} description={modals[0].props.header?.description} />
                {content}
            </DrawerContent>
        </Drawer>
    )
}
