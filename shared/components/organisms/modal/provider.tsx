import type { ReactNode } from "react";
import { nanoid } from "nanoid";
import React, { createContext, useCallback, useContext, useState, useRef, useEffect } from "react";
import { flushSync } from 'react-dom';
import { ConfirmationDialog } from "./confirmation";

export interface ModalProps {
  name?: string;
  header?: {
    title: string | ReactNode;
    description: string;
  } | null;
  disableClose?: boolean;
  hasUnsavedChanges?: boolean;
  hasData?: boolean;
  confirmClose?: boolean;
  alwaysConfirmClose?: boolean;
  overflowHidden?: boolean;
  confirmationProps?: {
    title?: string;
    description?: string;
    confirmText?: string;
    cancelText?: string;
    iconName?: string;
  };
  [key: string]: any;
}

interface ModalState {
  id: string;
  name: string;
  component: React.ComponentType<any>;
  props: ModalProps;
  show: boolean;
}

interface ModalContextValue {
  modals: ModalState[];
  open: (component: React.ComponentType<any>, props?: ModalProps) => void;
  hide: (name: string) => void;
  remove: (name: string) => void;
  patch: (props: ModalProps) => void;
  safeTryClose: (name: string, forceClose?: boolean) => void;
}

const ModalContext = createContext<ModalContextValue | null>(null);

export function ModalProvider({ children }: { children: ReactNode }) {
  const [modals, setModals] = useState<ModalState[]>([]);
  const [confirmingClose, setConfirmingClose] = useState<string | null>(null);

  const modalsRef = useRef<ModalState[]>(modals);

  useEffect(() => {
    modalsRef.current = modals;
  }, [modals]);

  const open = useCallback(
    (component: React.ComponentType<any>, props: ModalProps = {}) => {
      const modalName =
        props.name || component.displayName || `dynamic_modal_${nanoid()}`;

      setModals((current) => {
        const existing = current.find((m) => m.name === modalName);
        if (existing) {
          if (existing.show) {
            return current;
          }
          return current.map((m) =>
            m.name === modalName ? { ...m, show: true } : m
          );
        }

        const newModal: ModalState = {
          id: nanoid(),
          name: modalName,
          component,
          props: { ...props, name: modalName },
          show: true,
        };

        return [...current, newModal];
      });
    },
    []
  );

  const hide = useCallback((name: string) => {
    setModals((current) =>
      current.map((m) => (m.name === name ? { ...m, show: false } : m))
    );
  }, []);

  const remove = useCallback((name: string) => {
    setModals((current) => current.filter((m) => m.name !== name));
  }, []);

  const patch = (props: ModalProps) => {
    console.log("Patching modal props:", props);
    if (!props.name) return;

    console.log("Actualatching modal props:", props.name);

    flushSync(() => {
      setModals((current) =>
        current.map((m) =>
          m.name === props.name ? { ...m, props: { ...m.props, ...props } } : m
        )
      );
    });
  };

  const safeTryClose = (name: string, forceClose = false) => {
    const modal = modalsRef.current.find((m) => m.name === name);

    if (!modal) return;

    console.log("found modal props", modal);

    if (
      !forceClose &&
      ((modal.props.hasUnsavedChanges && modal.props.confirmClose !== false) ||
        (modal.props.hasData && modal.props.alwaysConfirmClose))
    ) {
      setConfirmingClose(name);
    } else {
      remove(name);
    }
  };

  const handleConfirmationClose = () => {
    setConfirmingClose(null);
  };

  const handleConfirmationConfirm = () => {
    if (confirmingClose) {
      remove(confirmingClose);
      setConfirmingClose(null);
    }
  };

  const confirmingModal = confirmingClose
    ? modals.find((m) => m.name === confirmingClose)
    : null;

  return (
    <ModalContext.Provider
      value={{ modals, open, hide, remove, patch, safeTryClose }}
    >
      {children}
      {confirmingModal && (
        <ConfirmationDialog
          open={true}
          onClose={handleConfirmationClose}
          onConfirm={handleConfirmationConfirm}
          {...confirmingModal.props.confirmationProps}
        />
      )}
    </ModalContext.Provider>
  );
}

export function useModal(): ModalContextValue {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error("useModal must be used within a ModalProvider.");
  }
  return context;
}
