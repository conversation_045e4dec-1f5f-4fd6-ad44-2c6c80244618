import type { Column } from '@tanstack/react-table'
import React from 'react'
import Iconify from '../../atoms/Iconify'
import { Button } from '../../ui/button'

interface SortHeaderProps<T> {
  column: Column<T, unknown>
  title: React.ReactNode
}

export function SortHeader<T>({ column, title }: SortHeaderProps<T>) {
  const sorted = column.getIsSorted()
  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(sorted === 'asc')}
      className="p-0 font-medium hover:bg-transparent hover:text-primary text-left group"
    >
      <div className="flex items-center text-xs">
        {title}
        {sorted === 'asc' ? (
          <Iconify name="untitled:arrow-up" className="size-3 text-primary mx-1" />
        ) : sorted === 'desc' ? (
          <Iconify name="untitled:arrow-down" className="size-3 text-primary mx-1" />
        ) : (
          <Iconify name="untitled:chevron-selector-vertical" className="size-3 text-gray-500 mx-1" />
        )}
      </div>
    </Button>
  )
}