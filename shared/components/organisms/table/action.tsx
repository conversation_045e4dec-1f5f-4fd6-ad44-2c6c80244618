import type { PropsWithChildren, ReactNode } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "../../ui/dropdown-menu";

interface Props {
  action: ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

function Action({
  children,
  action,
  open,
  onOpenChange,
}: PropsWithChildren<Props>) {
  return (
    <DropdownMenu open={open} onOpenChange={onOpenChange}>
      <DropdownMenuTrigger className="flex items-center">
        {action}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-full max-w-[220px] shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)] rounded-md border-gray-200 border-solid border-[1px]"
      >
        {children}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default Action;
