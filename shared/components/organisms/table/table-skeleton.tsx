import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../ui/table';

interface TableSkeletonProps {
  columns: number;
  rows: number;
  className?: string;
  roundless?: boolean;
}

export function TableSkeleton({ 
  columns = 5, 
  rows = 10, 
  className,
  roundless
}: TableSkeletonProps) {
  return (
    <div className={className || "w-full h-full flex flex-col justify-start gap-4"}>
      <div className="flex flex-col gap-2">
        <div
          className={`overflow-hidden border border-gray-200 ${
            roundless ? "border-x-0 border-b-0" : "rounded-xl shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)]"
          }`}
        >
          <Table>
            <TableHeader>
              <TableRow>
                {Array(columns)
                  .fill(0)
                  .map((_, i) => (
                    <TableHead key={`header-${i}`}>
                      <div className="h-6 bg-gray-200 rounded animate-pulse w-full max-w-[150px]" />
                    </TableHead>
                  ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array(rows)
                .fill(0)
                .map((_, rowIndex) => (
                  <TableRow key={`row-${rowIndex}`}>
                    {Array(columns)
                      .fill(0)
                      .map((_, colIndex) => (
                        <TableCell key={`cell-${rowIndex}-${colIndex}`}>
                          <div 
                            className="h-4 bg-gray-200 rounded animate-pulse" 
                            style={{
                              width: `${Math.max(50, Math.min(95, Math.random() * 100))}%`,
                              animationDelay: `${(rowIndex * columns + colIndex) * 0.05}s`
                            }}
                          />
                        </TableCell>
                      ))}
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
