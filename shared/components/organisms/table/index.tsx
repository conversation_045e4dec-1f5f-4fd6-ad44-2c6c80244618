import type {
  ColumnDef,
  ColumnFiltersState,
  OnChangeFn,
  SortingState,
  TableOptions,
  Table as TableType,
  VisibilityState
} from "@tanstack/react-table";
import type { TFunction } from "i18next";

import { Button } from "../../ui/button";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";

import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { AnimatedTransition } from "../../molecules/table-transition";
import { TableSkeleton } from "./table-skeleton";

interface DataTableProps<TData> {
  columns: ColumnDef<TData, any>[];
  data: TData[];
  empty?: React.ReactNode;
  className?: string;
  header?: (table: ReturnType<typeof useReactTable>) => React.ReactNode;
  isPaginated?: boolean;
  roundless?: boolean;
  meta?: {
    [key: string]: any;
  };
  onRowClick?: (row: TData) => void;
  footer?: (table: ReturnType<typeof useReactTable>) => React.ReactNode;
  tableOptions?: Partial<TableOptions<TData>>;
  onPageChange?: (page: number) => void;
  onRowSelectionChange?: Dispatch<SetStateAction<Record<string, boolean>>>;
  rowSelection?: Record<string, boolean>;
  onSortingChange?: OnChangeFn<SortingState>;
  isLoading?: boolean;
}

interface DataTablePaginationProps<TData> {
  table: TableType<TData>;
  onPageChange?: (page: number) => void;
  isLoading?: boolean;
}

export interface ColumnMeta {
  t: TFunction;
}

export { TableSkeleton } from './table-skeleton';

export function DataTablePagination<TData>({
  table,
  onPageChange,
  isLoading,
}: DataTablePaginationProps<TData>) {
  const { t } = useTranslation("common");

  if (table.getPageCount() <= 1 || isLoading) {
    return null;
  }

  const handlePageChange = (pageIndex: number) => {
    if (onPageChange) {
      onPageChange(pageIndex + 1);
    }
    table.setPageIndex(pageIndex);
  };

  const currentPage = table.getState().pagination.pageIndex;
  const totalPages = table.getPageCount();


  const getVisiblePageNumbers = () => {
    const pageNumbers = [];
    
    if (totalPages <= 7) {
    
      for (let i = 0; i < totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      pageNumbers.push(0);
      
      if (currentPage > 3) {
        pageNumbers.push(-1);
      }
      
      const startPage = Math.max(1, Math.min(currentPage - 1, totalPages - 5));
      const endPage = Math.min(startPage + 3, totalPages - 2);
      
      for (let i = startPage; i < endPage; i++) {
        pageNumbers.push(i);
      }
      
      if (currentPage < totalPages - 4) {
      
        pageNumbers.push(-2);
      }
      
    
      pageNumbers.push(totalPages - 1);
    }
    
    return pageNumbers;
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-wrap items-center justify-center w-full gap-2 mb-4">
        <Button
          variant="outline"
          onClick={() => {
            const prevPage = table.getState().pagination.pageIndex - 1;
            if (prevPage >= 0) {
              handlePageChange(prevPage);
            }
          }}
          disabled={!table.getCanPreviousPage()}
          className="items-center sm:px-4"
          size="sm"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="hidden sm:inline ml-1">{t("table.pagination.previous")}</span>
        </Button>

        <div className="hidden sm:flex items-center space-x-1">
          {getVisiblePageNumbers().map((page, i) => {
            if (page < 0) {
            
              return <div key={`ellipsis-${i}`} className="px-2">...</div>;
            }
            
            return (
              <Button
                key={page}
                variant="outline"
                onClick={() => handlePageChange(page)}
                disabled={currentPage === page}
                size="sm"
                className="h-8 w-8 p-0"
              >
                <span>{page + 1}</span>
              </Button>
            );
          })}
        </div>

        {/* Mobile page indicator */}
        <div className="sm:hidden flex items-center">
          <span className="text-sm text-gray-600">
            {currentPage + 1} / {totalPages}
          </span>
        </div>

        <Button
          variant="outline"
          onClick={() => {
            const nextPage = table.getState().pagination.pageIndex + 1;
            if (nextPage < table.getPageCount()) {
              handlePageChange(nextPage);
            }
          }}
          disabled={!table.getCanNextPage()}
          className="items-center sm:px-4"
          size="sm"
        >
          <span className="hidden sm:inline mr-1">{t("table.pagination.next")}</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

export function DataTable<TData>({
  columns,
  data,
  empty = "No data",
  className,
  header,
  isPaginated = true,
  meta,
  onRowClick,
  tableOptions = {},
  onPageChange,
  roundless,
  rowSelection,
  onRowSelectionChange,
  onSortingChange,
  isLoading,
}: DataTableProps<TData>) {
  const [_rowSelection, _setRowSelection] = useState({});

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const pageIndex = tableOptions?.state?.pagination?.pageIndex;
  const pageCount = tableOptions?.pageCount;
  
  useEffect(() => {
    if (
      pageIndex !== undefined && 
      pageCount !== undefined && 
      pageIndex > 0 && 
      data.length === 0 && 
      !isLoading && 
      onPageChange
    ) {
      const lastValidPage = Math.max(1, pageCount);
      onPageChange(lastValidPage);
    }
  }, [data, pageIndex, pageCount, isLoading, onPageChange]);

  const handleSortingChange: OnChangeFn<SortingState> = (updaterOrValue) => {
    let updatedSorting: SortingState;
    
    if (typeof updaterOrValue === 'function') {
      updatedSorting = updaterOrValue(sorting);
    } else {
      updatedSorting = updaterOrValue;
    }
    
    setSorting(updatedSorting);
    if (onSortingChange) {
      onSortingChange(updaterOrValue);
    }
  };

  const table = useReactTable<TData>({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onRowSelectionChange: onRowSelectionChange ?? _setRowSelection,
    onSortingChange: handleSortingChange,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection: rowSelection ?? _rowSelection,
    },
    meta,
    ...tableOptions,
  });


  const columnCount = columns.length || 5;
  const rowCount = data.length > 0 ? Math.min(data.length, 10) : 5;
  

  const hasData = table.getRowModel().rows?.length > 0;
  
  return (
    <div className={!hasData && !isLoading ? "w-full h-full flex items-center justify-center" : className}>
      <AnimatedTransition 
        loading={!!isLoading} 
        skeleton={<TableSkeleton columns={columnCount} rows={rowCount} roundless={roundless} />}
      >
        {hasData ? (
          <div className="w-full h-full flex flex-col justify-start gap-4">
            <div className="flex flex-col gap-2">
              {header &&
                typeof header === "function" &&
                header(table as unknown as ReturnType<typeof useReactTable>)}

              <div
                className={`overflow-hidden border border-gray-200 ${roundless ? "border-x-0 border-b-0" : "rounded-xl shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)]"}`}
              >
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => {
                          return (
                            <TableHead key={header.id}>
                              {header.isPlaceholder
                                ? null
                                : flexRender(
                                    header.column.columnDef.header,
                                    header.getContext()
                                  )}
                            </TableHead>
                          );
                        })}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows.map((row) => (
                      <TableRow
                        key={row.id}
                        data-state={row.getIsSelected() && "selected"}
                        onClick={
                          onRowClick
                            ? () => {
                                return onRowClick(row.original);
                              }
                            : undefined
                        }
                        className={
                          onRowClick ? "cursor-pointer hover:bg-gray-50" : ""
                        }
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id}>
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>

            {isPaginated && (
              <DataTablePagination 
                table={table} 
                onPageChange={onPageChange} 
                isLoading={isLoading} 
              />
            )}
          </div>
        ) : (
          empty
        )}
      </AnimatedTransition>
    </div>
  );
}
