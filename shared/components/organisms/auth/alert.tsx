import { AlertCircle } from 'lucide-react'

interface AuthAlertProps {
    title: string
    description: string
}

export function AuthAlert({ title, description }: AuthAlertProps) {
    return (
        <div className="rounded-lg border border-gray-300 bg-white p-4 shadow-sm my-6">
            <div className="flex items-start gap-4">
                <div className="rounded-full bg-white border-2 border-red-300 p-[3px] animate-pulse">
                    <div className="rounded-full bg-white border-2 border-red-300 p-[3px]">
                        <AlertCircle className="h-5 w-5 text-red-400" />
                    </div>
                </div>

                <div>
                    <h2 className="font-medium text-gray-800">{title}</h2>
                    <p className="mt-1 text-sm text-gray-600">{description}</p>
                </div>
            </div>
        </div>
    )
}
