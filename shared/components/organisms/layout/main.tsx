import type { HTMLMotionProps } from 'motion/react'
import { cn } from '../../../lib/utils'
import { motion } from 'motion/react'

interface MainProps extends HTMLMotionProps<'main'> {
    fixed?: boolean
}

export function Main({ fixed, className, ...props }: MainProps) {
    return (
        <motion.main
            key={window.location.pathname}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className={cn(
                'peer-[.header-fixed]/header:mt-16',
                'p-6',
                fixed && 'fixed-main flex flex-grow flex-col overflow-hidden',
                'flex flex-col items-center justify-center w-full h-full container',
                className,
            )}
            {...props}
        />
    )
}

Main.displayName = 'Main'
