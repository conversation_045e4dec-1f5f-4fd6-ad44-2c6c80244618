import type { NavItem, SidebarData } from "./types";
import Logo from "../../atoms/logo";
import Iconify from "../../atoms/Iconify";
import { Button } from "../../ui/button";
import { NavGroup } from "../../organisms/layout/nav-group";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarTrigger,
  useSidebar,
} from "../../ui/sidebar";
import { useTranslation } from "react-i18next";
import React from "react";
import { NavUser } from "./nav-user";

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  sidebarData: SidebarData | SidebarData[];
  user?: {
    name: string;
    email: string;
    avatar: string;
  };
  api: any
}

export function AppSidebar({ sidebarData, user, api, ...props }: AppSidebarProps) {
  const { state, isMobile, setOpenMobile } = useSidebar();
  const { t } = useTranslation("common");

  const handleCloseMobileMenu = React.useCallback(() => {
    setOpenMobile(false);
  }, [setOpenMobile]);

  return (
    <Sidebar 
      collapsible={isMobile ? "offcanvas" : "icon"} 
      variant="floating" 
      {...props}
    >
      <SidebarHeader className="gap-5 px-4">
        <div className="flex items-center justify-between">
          <Logo
            className={`flex-shrink-0 transition-all duration-300 ease-in-out ${state === "collapsed" && !isMobile ? "w-full" : "px-0 md:px-8"}`}
            state={state}
          />
          {isMobile && (
            <Button 
              variant="ghost" 
              size="icon" 
              className="ml-auto rounded-full h-8 w-8 hover:bg-gray-100" 
              onClick={handleCloseMobileMenu}
            >
              <Iconify name="untitled:x" className="h-5 w-5" />
            </Button>
          )}
        </div>
      </SidebarHeader>
      <SidebarContent>
        {(Array.isArray(sidebarData) ? sidebarData : [sidebarData]).flatMap((data) =>
          data.navGroups.map((group: SidebarData['navGroups'][number]) => (
            <NavGroup
              api={api}
              key={group.titleKey}
              title={t(group.titleKey)}
              titleKey={group.titleKey}
              items={group.items.map((item: typeof group.items[number]) => {
                const base = {
                  titleKey: item.titleKey,
                  badge: item.badge,
                  icon: item.icon,
                  disabledKey: item.disabledKey,
                };

                if (item.items) {
                  return {
                    ...base,
                    items: item.items.map((subItem: typeof item.items[number]) => ({
                      ...subItem,
                      titleKey: subItem.titleKey,
                    })),
                  } as unknown as NavItem;
                } else {
                  return {
                    ...base,
                    url: item.url,
                  } as unknown as NavItem;
                }
              })}
            />
          ))
        )}
      </SidebarContent>
      {user && (
        <SidebarFooter className="mt-auto">
          <NavUser user={user} />
        </SidebarFooter>
      )}
    </Sidebar>
  );
}
