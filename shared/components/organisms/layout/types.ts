import type { LinkProps } from "@tanstack/react-router";
import type { IconNames } from "../../atoms/Iconify";

interface User {
  name: string;
  email: string;
  avatar: string;
}

interface BaseNavItem {
  titleKey: string;
  badge?: string | React.FC;
  icon?: IconNames;
  disabledKey?: string;
}

type NavLink = BaseNavItem & {
  url: LinkProps["to"];
  items?: never;
  sideElement?: React.FC;
};

type NavCollapsible = BaseNavItem & {
  items: (BaseNavItem & { url: LinkProps["to"] })[];
  url?: never;
};

type NavItem = NavCollapsible | NavLink;

interface NavGroupType {
  titleKey: string;
  items: NavItem[];
}

interface SidebarData {
  user?: User;
  navGroups: NavGroupType[];
}

export type { NavCollapsible, NavGroupType, NavItem, NavLink, SidebarData };
