import { useEffect, useState } from "react";
import { SidebarMenuSubItem, SidebarMenuSubButton } from "../../ui/sidebar";
import { useTranslation } from "react-i18next";
import Iconify, { IconNames } from "../../atoms/Iconify";
import { globalSettingsKeys } from "../../../hooks";

interface DocumentData {
    value: {
        agreement?: { url: string };
        about?: { url: string };
        kvkk?: { url: string };
        manual?: { url: string };
        [key: string]: { url: string } | undefined;
    }
}


export const DocumentList = () => {
    const [documents, setDocuments] = useState<DocumentData | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const { t } = useTranslation("common");

    useEffect(() => {
        const fetchDocuments = async () => {
            try {
                setIsLoading(true);

                const endpoint = `/api/setting/global/${globalSettingsKeys.documents.pdf()}`;
                const response = await fetch(endpoint);

                if (!response.ok) {
                    console.warn(`API returned ${response.status}: ${response.statusText}`);
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    console.warn('Expected JSON response but got:', contentType);
                    throw new Error(`Expected JSON response but got ${contentType}`);
                }

                const data = await response.json();
                setDocuments(data.value);
            } finally {
                setIsLoading(false);
            }
        };

        fetchDocuments();
    }, []);

    if (isLoading) {
        return (
            <>
                <SidebarMenuSubItem>
                    <div className="flex items-center justify-center py-2">
                        <div className="animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent" />
                    </div>
                </SidebarMenuSubItem>
            </>
        );
    }

    if (!documents) {
        return null;
    }

    const handleOpenPdf = (url: string) => {
        window.open(url, "_blank");
    };

    const documentTitleMap: Record<string, string> = {
        about: "sidebar.items.documents.about",
        manual: "sidebar.items.documents.manual",
        // agreement: "sidebar.items.documents.agreement",
        // kvkk: "sidebar.items.documents.kvkk",
    };

    return (
        <>
            {Object.entries(documents).map(([docType, docData]) => {
                if (!docData) return null;

                return (
                    <SidebarMenuSubItem key={docType} className="list-none">
                        {documentTitleMap[docType] && docData.url && (
                            <SidebarMenuSubButton
                                onClick={() => handleOpenPdf(docData.url)}
                                className="cursor-pointer flex text-gray-600 hover:text-gray-800 justify-between [&_svg]:text-gray-400 [&_svg]:hover:text-gray-600 before:content-none"
                            >
                                <span>{t(documentTitleMap[docType] || docType)}</span>

                                <Iconify name="untitled:link-external-02" />
                            </SidebarMenuSubButton>
                        )}
                    </SidebarMenuSubItem>
                );
            })}
        </>
    );
};