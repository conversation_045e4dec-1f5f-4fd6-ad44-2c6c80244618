import Iconify from '../../atoms/Iconify'
import { cn } from '../../../lib/utils'
import { Avatar, AvatarFallback, AvatarImage } from '../../ui/avatar'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '../../ui/dropdown-menu'
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
} from '../../ui/sidebar'
import { Link } from '@tanstack/react-router'
import { BadgeCheck, Bell, LogOut } from 'lucide-react'

export function NavUser({
    user,
}: {
    user: {
        name: string
        email: string
        avatar: string
    }
}) {
    const { isMobile, state } = useSidebar()

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className={cn(
                                "h-auto p-3 items-start data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)] border-gray-200 border-solid border-[1px]",
                                isMobile && "py-4 px-5"
                            )}
                        >
                            {state === 'expanded' || isMobile
                                ? (
                                        <>
                                            <div className="grid flex-1 text-left text-sm leading-tight">
                                                <span className={cn(
                                                    "truncate leading-[20px] font-semibold text-gray-900",
                                                    isMobile && "text-base"
                                                )}>
                                                    {user.name}
                                                </span>
                                                <span className="truncate leading-[20px] text-sm text-gray-600 font-normal">
                                                    {user.email}
                                                </span>
                                            </div>
                                            <Iconify
                                                name="untitled:chevron-selector-vertical"
                                                className="size-5 text-gray-400"
                                            />
                                        </>
                                    )
                                : (
                                        <Avatar className="h-8 w-8 rounded-lg">
                                            <AvatarImage src={user.avatar} alt={user.name} />
                                            <AvatarFallback className="rounded-lg">TU</AvatarFallback>
                                        </Avatar>
                                    )}
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                        side={isMobile ? 'bottom' : (state === 'collapsed' ? 'right' : 'top')}
                        align={state === 'collapsed' ? 'start' : 'end'}
                        sideOffset={state === 'collapsed' ? 4 : 8}
                    >
                        <DropdownMenuLabel className="p-0 font-normal">
                            <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                <Avatar className="h-8 w-8 rounded-lg">
                                    <AvatarImage src={user.avatar} alt={user.name} />
                                    <AvatarFallback className="rounded-lg">TU</AvatarFallback>
                                </Avatar>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate font-semibold">{user.name}</span>
                                    <span className="truncate text-xs">{user.email}</span>
                                </div>
                            </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <DropdownMenuItem asChild>
                                <Link to="/settings/account">
                                    <BadgeCheck className="mr-2" />
                                    Ayarlar
                                </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                                <Link to="/settings/notifications">
                                    <Bell className="mr-2" />
                                    Bildirimler
                                </Link>
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="cursor-pointer text-red-500">
                            <LogOut className="mr-2" />
                            Çıkış Yap
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
