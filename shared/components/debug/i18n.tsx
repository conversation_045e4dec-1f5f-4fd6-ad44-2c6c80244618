import { useTranslation } from 'react-i18next'

export function I18nDebug() {
    const { t, i18n } = useTranslation()

    return (
        <div className="p-4 border border-yellow-400 bg-yellow-50 rounded">
            <h3 className="font-bold text-lg">i18n Debug</h3>
            <div className="space-y-2 mt-2">
                <p>
                    Current language:
                    <strong>{i18n.language}</strong>
                </p>
                <p>
                    Available languages:
                    {Object.keys(i18n.options.resources || {}).join(', ')}
                </p>
                <p>
                    Test translation:
                    <strong>{t('title')}</strong>
                </p>
                <div className="mt-2 space-x-2">
                    <button
                        className="px-3 py-1 bg-blue-500 text-white rounded"
                        onClick={() => i18n.changeLanguage('en')}
                    >
                        Switch to English
                    </button>
                    <button
                        className="px-3 py-1 bg-blue-500 text-white rounded"
                        onClick={() => i18n.changeLanguage('tr')}
                    >
                        Switch to Turkish
                    </button>
                </div>
            </div>
        </div>
    )
}
