import { PanelLeft } from "lucide-react";
import { useTranslation } from 'react-i18next';
import { cn } from "../../lib/utils";
import { But<PERSON> } from "./button";
import { useSidebar } from "./sidebar";
import * as React from "react";

const SidebarTrigger = React.forwardRef<
  React.ElementRef<typeof Button>,
  React.ComponentProps<typeof Button>
>(({ className, onClick, ...props }, ref) => {
  const { toggleSidebar } = useSidebar();
  const { t } = useTranslation('common');

    return (
        <Button
            ref={ref}
            data-sidebar="trigger"
            variant="ghost"
            size="icon"
            className={cn('h-7 w-7', className)}
            onClick={(event) => {
                onClick?.(event)
                toggleSidebar()
            }}
            {...props}
        >
            <PanelLeft />
            <span className="sr-only">{t("sidebar.toggle")}</span>
        </Button>
    )
})
SidebarTrigger.displayName = 'SidebarTrigger'

export { SidebarTrigger }
