import type React from 'react';
import type { DefaultValues } from 'react-hook-form';
import type { z } from 'zod';
import type { FieldConfig } from './types';

// Define a more general type for Zod schemas without constraint issues
export type ZodObjectOrWrapped = any;

/**
 * Beautify a camelCase string.
 * e.g. "myString" -> "My String"
 */
export function beautifyObjectName(string: string) {
    // if numbers only return the string
    let output = string.replace(/([A-Z])/g, ' $1')
    output = output.charAt(0).toUpperCase() + output.slice(1)
    return output
}

/**
 * Get the lowest level Zod type.
 * This will unpack optionals, refinements, etc.
 */
export function getBaseSchema(schema: z.ZodTypeAny | null | undefined): z.ZodTypeAny | null {
    if (!schema)
        return null
    if (schema._def && 'innerType' in schema._def) {
        return getBaseSchema(schema._def.innerType)
    }
    if (schema._def && 'schema' in schema._def) {
        return getBaseSchema(schema._def.schema)
    }

    return schema
}

/**
 * Get the type name of the lowest level Zod type.
 * This will unpack optionals, refinements, etc.
 */
export function getBaseType(schema: z.ZodTypeAny): string {
    const baseSchema = getBaseSchema(schema)
    return baseSchema ? baseSchema._def.typeName : ''
}

/**
 * Search for a "ZodDefault" in the Zod stack and return its value.
 */
export function getDefaultValueInZodStack(schema: z.ZodTypeAny): any {
    // Handle ZodDefault separately to get its default value
    if (schema._def.typeName === 'ZodDefault') {
        return (schema as z.ZodDefault<z.ZodTypeAny>)._def.defaultValue()
    }

    // Navigate through nested schemas
    if ('innerType' in schema._def) {
        return getDefaultValueInZodStack(schema._def.innerType)
    }
    
    if ('schema' in schema._def) {
        return getDefaultValueInZodStack(schema._def.schema)
    }

    return undefined
}

/**
 * Get all default values from a Zod schema.
 */
export function getDefaultValues<Schema extends z.ZodObject<any, any>>(
    schema: Schema,
    fieldConfig?: FieldConfig | Record<string, any>,
) {
    if (!schema)
        return null
    const { shape } = schema
  type DefaultValuesType = DefaultValues<Partial<z.infer<Schema>>>
  const defaultValues = {} as DefaultValuesType
  if (!shape)
      return defaultValues

  for (const key of Object.keys(shape)) {
      const item = shape[key] as z.ZodAny

      if (getBaseType(item) === 'ZodObject') {
          const defaultItems = getDefaultValues(
              getBaseSchema(item) as unknown as z.ZodObject<any, any>,
              fieldConfig?.[key] as FieldConfig<z.infer<Schema>>,
          )

          if (defaultItems !== null) {
              for (const defaultItemKey of Object.keys(defaultItems)) {
                  const pathKey = `${key}.${defaultItemKey}` as keyof DefaultValuesType
                  defaultValues[pathKey] = defaultItems[defaultItemKey]
              }
          }
      }
      else {
          let defaultValue = getDefaultValueInZodStack(item)
          if (
              (defaultValue === null || defaultValue === '')
              && fieldConfig?.[key]?.inputProps
          ) {
              defaultValue = (fieldConfig?.[key]?.inputProps as unknown as any)
                  .defaultValue
          }
          if (defaultValue !== undefined) {
              defaultValues[key as keyof DefaultValuesType] = defaultValue
          }
      }
  }

  return defaultValues
}

// Update the getObjectFormSchema to handle any schema safely
export function getObjectFormSchema(schema: any): z.ZodObject<any, any> {
    if (!schema) return null as any;
    
    try {
        // Handle ZodEffects
        if (schema._def && schema._def.typeName === 'ZodEffects') {
            return getObjectFormSchema(schema._def.schema)
        }
        
        // Handle ZodReadonly and other wrapper types 
        if (schema._def && ('schema' in schema._def)) {
            return getObjectFormSchema(schema._def.schema);
        }
        
        if (schema._def && ('innerType' in schema._def)) {
            return getObjectFormSchema(schema._def.innerType);
        }
        
        // Return the schema as an object if it appears to be one
        if (schema._def && schema._def.typeName === 'ZodObject') {
            return schema;
        }
        
        return schema;
    } catch (e) {
        // Fallback for any errors
        console.error("Error processing schema:", e);
        return schema;
    }
}

/**
 * Convert a Zod schema to HTML input props to give direct feedback to the user.
 * Once submitted, the schema will be validated completely.
 */
export function zodToHtmlInputProps(
    schema: z.ZodTypeAny,
): React.InputHTMLAttributes<HTMLInputElement> {
    if (['ZodOptional', 'ZodNullable'].includes(schema._def.typeName)) {
        return {
            ...zodToHtmlInputProps(
                'innerType' in schema._def ? schema._def.innerType : schema
            ),
            required: false,
        }
    }

    // If the schema doesn't have checks, just return required true
    if (!('checks' in schema._def)) {
        return {
            required: true,
        }
    }

    const { checks } = schema._def
    const inputProps: React.InputHTMLAttributes<HTMLInputElement> = {
        required: true,
    }
    const type = getBaseType(schema)

    for (const check of checks) {
        if (check.kind === 'min') {
            if (type === 'ZodString') {
                inputProps.minLength = check.value
            }
            else {
                inputProps.min = check.value
            }
        }
        if (check.kind === 'max') {
            if (type === 'ZodString') {
                inputProps.maxLength = check.value
            }
            else {
                inputProps.max = check.value
            }
        }
    }

    return inputProps
}

/**
 * Sort the fields by order.
 * If no order is set, the field will be sorted based on the order in the schema.
 */

export function sortFieldsByOrder<SchemaType extends z.ZodObject<any, any>>(
    fieldConfig: FieldConfig<z.infer<SchemaType>> | undefined,
    keys: string[],
) {
    const sortedFields = keys.sort((a, b) => {
        const fieldA: number = (fieldConfig?.[a]?.order as number) ?? 0
        const fieldB = (fieldConfig?.[b]?.order as number) ?? 0
        return fieldA - fieldB
    })

    return sortedFields
}
