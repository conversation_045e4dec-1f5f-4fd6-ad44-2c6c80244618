import type { ControllerRenderProps, FieldValues } from "react-hook-form";
import type * as z from "zod";
import type { INPUT_COMPONENTS } from "./config";

export interface FieldConfigItem {
  namespace?: string;
  description?: React.ReactNode;
  isHide?: boolean;
  onChange?: ((value: any) => void) | ((value: any) => Promise<any>);
  inputProps?: React.InputHTMLAttributes<HTMLInputElement> &
    React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
      showLabel?: boolean;
      emptyText?: string;
      searchText?: string;
    };
  label?: string;
  fieldType?:
    | keyof typeof INPUT_COMPONENTS
    | React.FC<AutoFormInputComponentProps>;

  renderParent?: (props: {
    children: React.ReactNode;
  }) => React.ReactElement | null;

  order?: number;

  options?:
    | Array<{ label: string; value: string }>
    | ((
        page: number,
        search: string
      ) => Promise<{
        items: Array<{ label: string; value: string }>;
        nextPage: number;
      }>);

  selectedOptionLabel?: (value: string) => Promise<string>;
}

export type FieldConfig<T = any> = {
  [key: string]: FieldConfigItem & { 
    onlyIf?: (values: any) => boolean 
  };
};

export type FieldConfigFn = (values: any) => any;

export type AnyFieldConfig = FieldConfig | FieldConfigFn;

export enum DependencyType {
  DISABLES,
  REQUIRES,
  HIDES,
  SETS_OPTIONS,
}

interface BaseDependency<T = any> {
  sourceField: keyof T;
  type: DependencyType;
  targetField: keyof T;
  when: (sourceFieldValue: any, targetFieldValue: any) => boolean;
}

export type ValueDependency<T = any> =
  BaseDependency<T> & {
    type:
      | DependencyType.DISABLES
      | DependencyType.REQUIRES
      | DependencyType.HIDES;
  };

export type EnumValues = readonly [string, ...string[]];

export type OptionsDependency<T = any> =
  BaseDependency<T> & {
    type: DependencyType.SETS_OPTIONS;
    options: EnumValues;
  };

export type Dependency<T = any> =
  | ValueDependency<T>
  | OptionsDependency<T>;

/**
 * A FormInput component can handle a specific Zod type (e.g. "ZodBoolean")
 */
export interface AutoFormInputComponentProps {
  zodInputProps: React.InputHTMLAttributes<HTMLInputElement>;
  field: ControllerRenderProps<FieldValues, any>;
  fieldConfigItem: FieldConfigItem;
  label: string;
  isRequired: boolean;
  fieldProps: any;
  zodItem: z.ZodTypeAny;
  className?: string;
  namespace?: string;
}