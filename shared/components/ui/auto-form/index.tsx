"use client";
import type { DefaultVal<PERSON>, FormState, UseFormReturn } from "react-hook-form";
import type { z } from "zod";
import type { AnyFieldConfig, Dependency } from "./types";

import { zodResolver } from "@hookform/resolvers/zod";
import { cn } from "../../../lib/utils";
import { Button } from "../../ui/button";
import { Form } from "../../ui/form";

import React from "react";
import { useForm } from "react-hook-form";
import AutoFormObject from "./fields/object";
import { getDefaultValues, getObjectFormSchema } from "./utils";

export function AutoFormSubmit({
  children,
  className,
  disabled,
}: {
  children?: React.ReactNode;
  className?: string;
  disabled?: boolean;
}) {
  return (
    <Button type="submit" disabled={disabled} className={className}>
      {children ?? "Submit"}
    </Button>
  );
}

export type { FieldConfig, FieldConfigFn } from "./types";

function AutoForm<SchemaType extends z.ZodTypeAny>({
  formSchema,
  values: valuesProp,
  onValuesChange: onValuesChangeProp,
  onParsedValuesChange,
  onSubmit: onSubmitProp,
  onValidChange,
  fieldConfig: _fieldConfig,
  children,
  className,
  dependencies,
  namespace,
}: {
  formSchema: SchemaType;
  values?: Partial<z.infer<SchemaType>>;
  onValuesChange?: (
    values: Partial<z.infer<SchemaType>>,
    form: UseFormReturn<z.infer<SchemaType>>
  ) => void;
  onParsedValuesChange?: (
    values: Partial<z.infer<SchemaType>>,
    form: UseFormReturn<z.infer<SchemaType>>
  ) => void;
  onSubmit?: (
    values: z.infer<SchemaType>,
    form: UseFormReturn<z.infer<SchemaType>>
  ) => void;
  onValidChange?: (isValid: boolean) => void;
  fieldConfig?: AnyFieldConfig; // Use the union type
  children?:
    | React.ReactNode
    | ((formState: FormState<z.infer<SchemaType>>) => React.ReactNode);
  className?: string;
  dependencies?: Dependency<z.infer<SchemaType>>[];
  namespace?: string;
}) {

  const badFieldConfig =
    typeof _fieldConfig === "function"
      ? _fieldConfig(valuesProp)
      : _fieldConfig;

  const objectFormSchema = getObjectFormSchema(formSchema);
  const defaultValues: DefaultValues<z.infer<typeof objectFormSchema>> | null =
    getDefaultValues(objectFormSchema, badFieldConfig);

  const form = useForm<z.infer<typeof objectFormSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues ?? undefined,
    values: valuesProp,
    reValidateMode: "onChange",
  });

  
  const values = form.watch();
  const fieldConfig =
    typeof _fieldConfig === "function"
      ? _fieldConfig(values as z.infer<SchemaType>)
      : _fieldConfig;

  onValidChange?.(form.formState.isValid);

  function onSubmit(values: z.infer<typeof formSchema>) {
    const parsedValues = formSchema.safeParse(values);
    if (parsedValues.success) {
      onSubmitProp?.(parsedValues.data, form);
    } else {
      console.error("Form validation failed:", parsedValues.error);
    }
  }

  React.useEffect(() => {
    const subscription = form.watch((values) => {
      onValuesChangeProp?.(values, form);
      const parsedValues = formSchema.safeParse(values);
      if (parsedValues.success) {
        onParsedValuesChange?.(parsedValues.data, form);
      }
    });

    return () => subscription.unsubscribe();
  }, [form, formSchema, onValuesChangeProp, onParsedValuesChange]);

  const renderChildren =
    typeof children === "function"
      ? children(form.formState as FormState<z.infer<SchemaType>>)
      : children;

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit((values) => {
              onSubmit(values);
            })(e);
          }}
          className={cn("space-y-5", className)}
        >
          <AutoFormObject
            schema={objectFormSchema}
            form={form}
            dependencies={dependencies}
            fieldConfig={fieldConfig}
            namespace={namespace}
          />

          {renderChildren}
        </form>
      </Form>
    </div>
  );
}

export default AutoForm;
