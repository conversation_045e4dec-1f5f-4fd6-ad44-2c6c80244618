import type * as z from 'zod'
import { FormControl, FormItem, FormMessage } from '../../../ui/form'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '../../../ui/select'
import AutoFormLabel from '../common/label'
import AutoFormTooltip from '../common/tooltip'
import type { AutoFormInputComponentProps } from '../types'
import { getBaseSchema } from '../utils'

export default function AutoFormEnum({
    label, 
    isRequired,
    field,
    fieldConfigItem,
    zodItem,
    fieldProps,
}: AutoFormInputComponentProps) {
  let values: [string, string][] = [];

  if (fieldConfigItem.options && Array.isArray(fieldConfigItem.options)) {
    values = fieldConfigItem.options.map((option) => [
      option.value,
      option.label,
    ]);
  } else {
    const baseValues = (getBaseSchema(zodItem) as unknown as z.ZodEnum<any>)
      ?._def?.values;

    if (!Array.isArray(baseValues)) {
      values = baseValues && typeof baseValues === 'object' 
        ? Object.entries(baseValues) 
        : [];
    } else {
      values = baseValues.map((value) => [value, value]);
    }
  }

  function findItem(value: any) {
    return values.find((item) => item[0] === value);
  }

  if (fieldConfigItem.isHide) return null;

  if (field.value && !findItem(field.value)) {
    // todo: this is bad, but i don't want to use a useEffect here
    field.onChange(null);
  }

  return (
    <FormItem>
      <AutoFormLabel
        label={fieldConfigItem?.label || label}
        isRequired={isRequired}
      />
      <FormControl>
        <Select
          onValueChange={field.onChange}
          defaultValue={field.value}
          {...fieldProps}
        >
          <SelectTrigger className={fieldProps.className}>
            <SelectValue placeholder={fieldConfigItem.inputProps?.placeholder}>
              {field.value ? findItem(field.value)?.[1] : "Select an option"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {values.map(([value, label]) => (
              <SelectItem value={value} key={value}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </FormControl>
      <AutoFormTooltip fieldConfigItem={fieldConfigItem} />
      <FormMessage namespace={fieldConfigItem?.namespace} />
    </FormItem>
  );
}
