import type { AutoFormInputComponentProps } from '../types'
import { FormControl, FormItem, FormMessage } from '../../../ui/form'
import { Input } from '../../../ui/input'
import AutoFormLabel from '../common/label'
import AutoFormTooltip from '../common/tooltip'
import { useTranslation } from 'react-i18next'

export default function AutoFormInput({
    label,
    isRequired,
    fieldConfigItem,
    fieldProps,
}: AutoFormInputComponentProps) {
    const { t } = useTranslation(); // Use translation
    const { showLabel: _showLabel, ...fieldPropsWithoutShowLabel } = fieldProps
    const showLabel = _showLabel === undefined ? true : _showLabel
    const type = fieldProps.type || 'text'
    const translatedLabel = t(fieldConfigItem?.label || label);
    const translatedPlaceholder = t(fieldProps?.placeholder || '');

    if (fieldConfigItem.isHide)
        return null

    return (
        <div className="flex flex-row  items-center space-x-2">
            <FormItem className="flex w-full flex-col justify-start">
                {showLabel && (
                    <AutoFormLabel
                        label={translatedLabel}
                        isRequired={isRequired}
                    />
                )}
                <FormControl>
                    <Input
                        type={type}
                        {...fieldPropsWithoutShowLabel}
                        placeholder={translatedPlaceholder}
                    />
                </FormControl>
                <AutoFormTooltip fieldConfigItem={fieldConfigItem} />
                <FormMessage namespace={fieldConfigItem?.namespace} />
            </FormItem>
        </div>
    )
}
