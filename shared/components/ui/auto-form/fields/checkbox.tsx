import type { AutoFormInputComponentProps } from '../types'
import { Checkbox } from '../../../ui/checkbox'
import { FormControl, FormItem, FormMessage } from '../../../ui/form'
import AutoFormLabel from '../common/label'
import AutoFormTooltip from '../common/tooltip'
import { useCallback, memo } from 'react'

const AutoFormCheckbox = memo(function AutoFormCheckbox({
    label,
    isRequired,
    field,
    fieldConfigItem,
    fieldProps,
}: AutoFormInputComponentProps) {
    if (fieldConfigItem.isHide)
        return null
    
    const handleCheckedChange = useCallback((checked: boolean | 'indeterminate') => {
        if (field.onChange) {
            const newValue = checked === 'indeterminate' ? false : checked;
            field.onChange(newValue);
        }
    }, [field]);

    return (
        <div>
            <FormItem>
                <div className="mb-3 flex items-center gap-3">
                    <FormControl>
                        <Checkbox
                            checked={!!field.value}
                            onCheckedChange={handleCheckedChange}
                            {...fieldProps}
                        />
                    </FormControl>
                    <AutoFormLabel
                        label={fieldConfigItem?.label || label}
                        isRequired={isRequired}
                    />
                </div>
            </FormItem>
            <AutoFormTooltip fieldConfigItem={fieldConfigItem} />
            <FormMessage namespace={fieldConfigItem?.namespace} />
        </div>
    )
})

export default AutoFormCheckbox
