import type { AutoFormInputComponentProps } from "../types";
import { FormControl, FormItem, FormMessage } from "../../../ui/form";
import { Input } from "../../../ui/input";
import AutoFormLabel from "../common/label";
import AutoFormTooltip from "../common/tooltip";
import { cn } from "../../../../lib/utils";

export default function AutoFormNumber({
  label,
  isRequired,
  fieldConfigItem,
  fieldProps,
}: AutoFormInputComponentProps) {
  const { showLabel: _showLabel, ...fieldPropsWithoutShowLabel } = fieldProps;
  const showLabel = _showLabel === undefined ? true : _showLabel;

  if (fieldConfigItem.isHide) return null;

  return (
    <FormItem>
      {showLabel && (
        <AutoFormLabel
          label={fieldConfigItem?.label || label}
          isRequired={isRequired}
        />
      )}
      <FormControl>
        <Input
          type="number"
          {...fieldPropsWithoutShowLabel}
          className={cn(
            "[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",
            fieldPropsWithoutShowLabel?.className
          )}
        />
      </FormControl>
      <AutoFormTooltip fieldConfigItem={fieldConfigItem} />
      <FormMessage namespace={fieldConfigItem?.namespace} />
    </FormItem>
  );
}
