import { FileIcon, Trash2 } from "lucide-react";
import React, { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import FeaturedIcon from "../../../atoms/featured-icon";
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../ui/form";
import type { AutoFormInputComponentProps } from "../types";
import { toast } from "sonner";

export default function CustomFileUploader({
  isRequired,
  fieldConfigItem,
  fieldProps,
  field,
}: AutoFormInputComponentProps): React.ReactElement | null {
  const [files, setFiles] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const multiple = fieldConfigItem?.inputProps?.multiple || false;
  const maxCount = (fieldConfigItem?.inputProps as any)?.maxCount || 1; // todo: any
  const onChangeHandler = fieldConfigItem?.onChange;
  const { t } = useTranslation("common");

  const maxSize = (fieldConfigItem?.inputProps as any)?.maxSize || 10 * 1024 * 1024;
  const acceptTypes = fieldConfigItem?.inputProps?.accept || "application/pdf";

  const handleFilesChange = async (selectedFiles: File[]) => {
    const acceptList = acceptTypes.split(",").map((type) => type.trim());

    const validFiles = selectedFiles.filter((file) => {
      return acceptList.some((type) => {
        if (type.startsWith(".")) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        if (type.endsWith("/*")) {
          const category = type.split("/")[0];
          return file.type.startsWith(category + "/");
        }
        return file.type === type;
      });
    });

    if (validFiles.length === 0) return;

    // todo: old state of files is disregarded
    const updatedFiles = multiple ? [...files, ...validFiles] : validFiles;
    if (updatedFiles.length > maxCount) {
      toast.error(
        t("max_files_exceeded", {
          maxCount,
        })
      );
      return;
    }

    setFiles(updatedFiles);

    const fileIds = await Promise.all(
      updatedFiles.map(async (file) => {
        if (onChangeHandler) {
          return await onChangeHandler(file);
        }
        return file.name;
      })
    );

    field.onChange(multiple ? fileIds : fileIds[0]);
  };

  const handleRemoveFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index);
    setFiles(updatedFiles);

    const fileIds = updatedFiles.map((file) => file.name);
    field.onChange(multiple ? fileIds : fileIds[0]);
  };

  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      if (!isDragging) {
        setIsDragging(true);
      }
    },
    [isDragging]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleFilesChange(Array.from(e.dataTransfer.files));
        e.dataTransfer.clearData();
      }
    },
    [handleFilesChange]
  );

  return (
    <FormItem>
      {fieldProps.label && (
        <FormLabel>
          {fieldProps.label}
          {isRequired && <span className="text-destructive ml-1">*</span>}
        </FormLabel>
      )}
      <FormControl>
        <div>
          <div
            className={`border border-dashed rounded-md p-4 transition-colors ${
              isDragging ? "bg-primary/10 border-primary" : ""
            }`}
            onDragEnter={handleDragEnter}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <FeaturedIcon
              variant="alternative"
              className="mx-auto h-12 w-12 text-muted-foreground"
              name="untitled:upload-cloud-02"
            />

            <div className="mt-4 text-center justify-center flex flex-col items-center text-sm leading-6 text-muted-foreground">
              <label
                htmlFor="file-upload"
                className="relative cursor-pointer rounded-md font-semibold text-gray-900 hover:text-primary/80 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              >
                <span>{t("autoFormFields.select_file")}</span>
                <input
                  {...(fieldProps
                    ? Object.fromEntries(
                        Object.entries(fieldProps).filter(
                          ([key]) => key !== "value" && key !== "defaultValue"
                        )
                      )
                    : {})}
                  type="file"
                  accept={acceptTypes}
                  multiple={multiple}
                  onChange={(e) => {
                    if (e.target.files) {
                      handleFilesChange(Array.from(e.target.files));
                    }
                  }}
                  className="hidden"
                  id="file-upload"
                />
              </label>

              <p className="text-xs leading-5 text-muted-foreground mt-2 whitespace-pre-line">
                {t("autoFormFields.allowed_file_formats_multiple", {
                  size: Math.floor(maxSize / 1024 / 1024),
                  formats: acceptTypes.replace(/\./g, " ").toUpperCase(),
                })}
              </p>
            </div>
          </div>
          <div className="mt-4 space-y-2">
            {files.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between border p-2 rounded-md"
              >
                <div className="flex items-center space-x-2">
                  <FileIcon size={16} className="text-gray-300" />
                  <span className="text-xs text-gray-600">{file.name}</span>
                </div>
                <button
                  type="button"
                  onClick={() => handleRemoveFile(index)}
                  className="text-destructive"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            ))}
          </div>
        </div>
      </FormControl>
      <FormMessage namespace={fieldConfigItem?.namespace} />
    </FormItem>
  );
}
