import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { Check, ChevronDown, Loader2, Search, X } from "lucide-react";
import { nanoid } from "nanoid";
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDebounce } from "../../../../hooks/use-debounce";
import { cn } from "../../../../lib/utils";
import { Button } from "../../button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "../../command";
import { FormControl, FormItem } from "../../form";
import { Input } from "../../input";
import { Popover, PopoverContentNoPortal, PopoverTrigger } from "../../popover";
import AutoFormLabel from "../common/label";
import { AutoFormInputComponentProps } from "../types";
import { useTranslation } from "react-i18next";

interface Framework {
  value: string;
  label: string;
}

interface ApiPage {
  items: Framework[];
  nextPage?: number | undefined;
}

export default function AutoFormSearchEnum({
  label,
  isRequired,
  field,
  fieldConfigItem,
  zodItem,
  fieldProps,
}: AutoFormInputComponentProps) {
  const { t } = useTranslation("common");
  const [open, setOpen] = useState(false);
  const [_search, setSearch] = useState("");

  const currentValue = field.value;

  const setValue = useCallback(
    (newValue: string) => {
      if (field.onChange) {
        setTimeout(() => field.onChange(newValue), 0);
      }
    },
    [field]
  );

  const search = useDebounce(_search, 300);
  const idRef = useRef<string>(nanoid());
  const [width, setWidth] = useState(0);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const scrollThrottleRef = useRef<NodeJS.Timeout | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const fetcher = useMemo(
    () =>
      typeof fieldConfigItem.options === "function"
        ? fieldConfigItem.options
        : () => ({ items: [], nextPage: 1 }),
    [fieldConfigItem.options]
  );

  const selectedOptionLabelFn = (fieldConfigItem as any).selectedOptionLabel;

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    refetch,
    isFetching,
  } = useInfiniteQuery<ApiPage>({
    queryFn: ({ pageParam = 0 }) => fetcher(pageParam as number, search),
    queryKey: ["auto-form-select-search", idRef.current, search],
    getNextPageParam: (lastPage, pages) => lastPage.nextPage,
    initialPageParam: 0,
    enabled: open,
    staleTime: 5 * 60 * 1000,
    retry: 2,
    refetchOnWindowFocus: false,
  });

  const { data: nameData, isLoading: nameLoading } = useQuery<string>({
    queryKey: ["auto-form-select-name", idRef.current, currentValue],
    queryFn: selectedOptionLabelFn
      ? () => selectedOptionLabelFn(currentValue)
      : () => Promise.resolve(currentValue),
    enabled: Boolean(selectedOptionLabelFn && currentValue),
    staleTime: 10 * 60 * 1000,
    retry: 2,
  });

  useLayoutEffect(() => {
    if (open && triggerRef.current) {
      setWidth(triggerRef.current.offsetWidth);
    }
  }, [open]);

  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
    }
  }, [open]);

  const onScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const target = e.target as HTMLDivElement;

      if (scrollThrottleRef.current) {
        clearTimeout(scrollThrottleRef.current);
      }

      scrollThrottleRef.current = setTimeout(() => {
        if (
          hasNextPage &&
          !isFetchingNextPage &&
          target.scrollHeight - target.scrollTop <= target.clientHeight + 120
        ) {
          fetchNextPage();
        }
      }, 200);
    },
    [fetchNextPage, hasNextPage, isFetchingNextPage]
  );

  useEffect(() => {
    return () => {
      if (scrollThrottleRef.current) {
        clearTimeout(scrollThrottleRef.current);
      }
    };
  }, []);

  const handleSelect = useCallback(
    (currentSelectedValue: string) => {
      const newValue =
        currentSelectedValue === currentValue ? "" : currentSelectedValue;
      setValue(newValue);
      setOpen(false);
    },
    [setValue, currentValue]
  );

  const handleClear = useCallback(() => {
    setValue("");
    setSearch("");
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [setValue]);

  const renderedItems = useMemo(() => {
    if (!data?.pages || data.pages.length === 0) {
      return [];
    }

    const allItems = data?.pages.flatMap((page) => page.items) || [];

    const uniqueItems = Array.from(
      new Map(allItems.map((item) => [item.value, item])).values()
    );

    return uniqueItems.map((item) => (
      <CommandItem
        key={item.value}
        value={item.value}
        onSelect={handleSelect}
        className="flex items-center justify-between"
      >
        <span className="truncate">{item.label}</span>
        <Check
          className={cn(
            "ml-2 size-4 shrink-0",
            currentValue === item.value ? "opacity-100" : "opacity-0"
          )}
        />
      </CommandItem>
    ));
  }, [data?.pages, currentValue, handleSelect]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value);
    },
    []
  );

  const showEmptyState = useMemo(() => {
    return (
      search.trim().length > 0 &&
      renderedItems.length === 0 &&
      !isLoading &&
      !isFetching
    );
  }, [search, renderedItems.length, isLoading, isFetching]);

  return (
    <FormItem>
      <AutoFormLabel
        label={fieldConfigItem?.label || label}
        isRequired={isRequired}
      />
      <FormControl>
        <Popover
          open={open}
          onOpenChange={(newOpen) => {
            setOpen(newOpen);
            if (newOpen) {
              setSearch("");
              refetch();
            }
          }}
        >
          <PopoverTrigger asChild>
            <Button
              ref={triggerRef}
              variant="outline"
              role="combobox"
              aria-expanded={open}
              disabled={fieldProps.disabled}
              className={cn(
                "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)] ring-offset-background focus:outline-none focus:ring-2 focus:ring-primary disabled:cursor-not-allowed disabled:opacity-50",
                (!currentValue || !nameData || nameLoading) && "text-gray-500"
              )}
            >
              {nameLoading || isLoading ? (
                <span className="flex text-xs flex-row flex-shrink-0 items-center gap-2 text-gray-500">
                  <Loader2 className="h-4 w-4 animate-spin inline-flex mr-2" />
                  {t("loading")}
                </span>
              ) : nameData ? (
                <span className="truncate">{nameData}</span>
              ) : (
                <span className="text-gray-500 truncate">
                  {fieldConfigItem.inputProps?.placeholder ??
                    fieldProps.placeholder ??
                    "Seçin..."}
                </span>
              )}
              <ChevronDown className="h-4 w-4 opacity-50 ml-2 shrink-0" />
            </Button>
          </PopoverTrigger>
          <PopoverContentNoPortal
            className="w-full p-0 pointer-events-auto"
            onWheel={(e) => {
              e.stopPropagation();
            }}
          >
            <Command shouldFilter={false}>
              <div className="flex items-center border-b px-3">
                <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                <Input
                  ref={inputRef}
                  placeholder={fieldProps.searchText ?? "Arama yapın..."}
                  className="border-none outline-none focus-visible:ring-0 focus-visible:border-none shadow-none flex-1 py-2"
                  value={_search}
                  onChange={handleSearchChange}
                  onKeyDown={(e) => {
                    if (e.key === "Escape") {
                      setOpen(false);
                    }
                  }}
                  autoFocus
                />
                {_search && (
                  <button
                    onClick={handleClear}
                    className="p-1 rounded-full hover:bg-gray-100"
                  >
                    <X className="h-4 w-4 opacity-50" />
                  </button>
                )}
              </div>
              <CommandList
                className="max-h-[200px]"
                onScroll={onScroll}
                style={{ width }}
              >
                <CommandEmpty className="w-full py-3 px-2 text-center">
                  {showEmptyState ? (
                    <div className="text-xs px-2 text-gray-500">
                      {fieldProps.emptyText ?? "Sonuç bulunamadı"}
                    </div>
                  ) : isLoading || isFetching ? (
                    <span className="px-2 text-xs flex flex-row items-center justify-center gap-2 text-gray-500">
                      <Loader2 className="h-4 w-4 animate-spin" />{" "}
                      {t("loading")}
                    </span>
                  ) : (
                    <div className="text-xs px-2 text-gray-500">
                      {t("start_typing_to_search")}
                    </div>
                  )}
                </CommandEmpty>
                <CommandGroup>{renderedItems}</CommandGroup>
              </CommandList>
            </Command>
          </PopoverContentNoPortal>
        </Popover>
      </FormControl>
    </FormItem>
  );
}
