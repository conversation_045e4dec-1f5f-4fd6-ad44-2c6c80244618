import React, { useState } from 'react';
import { Trash2, FileIcon } from 'lucide-react';
import FeaturedIcon from '../atoms/featured-icon';
import { useTranslation } from 'react-i18next';

interface FileUploaderProps {
    label?: string;
    required?: boolean;
    multiple?: boolean;
    maxSize?: number; // in bytes
    onChange?: (files: File[] | File) => void;
    onFileProcess?: (file: File) => Promise<string | number>;
    accept?: string;
    className?: string;
}

export default function FileUploader({
    label,
    required = false,
    multiple = false,
    maxSize = 10 * 1024 * 1024, // Default 10MB
    onChange,
    onFileProcess,
    accept = "application/pdf",
    className,
}: FileUploaderProps): React.ReactElement {
    const [files, setFiles] = useState<File[]>([]);
    const { t } = useTranslation('common');

    const handleFilesChange = async (selectedFiles: File[]) => {
        const validFiles = selectedFiles.filter(file => 
            accept.includes(file.type) && file.size <= maxSize);

        if (validFiles.length === 0) return;

        const updatedFiles = multiple ? [...files, ...validFiles] : validFiles;
        setFiles(updatedFiles);

        if (onChange) {
            onChange(multiple ? updatedFiles : updatedFiles[0]);
        }

        if (onFileProcess) {
            const fileIds = await Promise.all(
                updatedFiles.map(file => onFileProcess(file))
            );
            
            // You could do something with fileIds here if needed
        }
    };

    const handleRemoveFile = (index: number) => {
        const updatedFiles = files.filter((_, i) => i !== index);
        setFiles(updatedFiles);

        if (onChange) {
            onChange(multiple ? updatedFiles : updatedFiles[0] || null);
        }
    };

    return (
        <div className={className}>
            {label && (
                <div className="text-sm font-medium mb-1.5">
                    {label}
                    {required && <span className="text-red-500 ml-1">*</span>}
                </div>
            )}
            <div>
                <div className="border border-dashed rounded-md p-4">
                    <FeaturedIcon variant="alternative" className="mx-auto h-12 w-12 text-muted-foreground" name="untitled:upload-cloud-02" />

                    <div className="mt-4 text-center justify-center flex text-sm leading-6 text-muted-foreground">
                        <label htmlFor="file-upload" className="relative cursor-pointer rounded-md font-semibold text-gray-900 hover:text-primary/80 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2">
                            <span>{t('autoFormFields.select_file')}</span>
                            <input
                                type="file"
                                accept={accept}
                                multiple={multiple}
                                onChange={(e) => {
                                    if (e.target.files) {
                                        handleFilesChange(Array.from(e.target.files));
                                    }
                                }}
                                className="hidden"
                                id="file-upload"
                            />
                            <p className="text-xs leading-5 text-muted-foreground">
                                {t('autoFormFields.allowed_file_formats', { size: maxSize / 1024 / 1024 })}
                            </p>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    );
}
