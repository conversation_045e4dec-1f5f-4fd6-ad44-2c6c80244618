import * as React from "react";
import { Drawer as DrawerPrimitive } from "vaul";
import { cn } from "../../lib/utils";

const Drawer = ({
  shouldScaleBackground = true,
  ...props
}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (
  <DrawerPrimitive.Root
    shouldScaleBackground={shouldScaleBackground}
    {...props}
  />
);
Drawer.displayName = "Drawer";

// Add explicit type annotation to fix the TypeScript error
const DrawerTrigger: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Trigger>
> = DrawerPrimitive.Trigger;
DrawerTrigger.displayName = "DrawerTrigger";

// Add explicit type annotation to fix the TypeScript error
const DrawerClose: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Close>
> = DrawerPrimitive.Close;
DrawerClose.displayName = "DrawerClose";

// Add explicit type annotation to fix the TypeScript error
const DrawerOverlay: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Overlay>
> = ({ className, ...props }) => (
  <DrawerPrimitive.Overlay
    className={cn("fixed inset-0 z-50 bg-black/80", className)}
    {...props}
  />
);
DrawerOverlay.displayName = "DrawerOverlay";

// Add explicit type annotation to fix the TypeScript error
const DrawerContent: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Content>
> = ({ className, children, ...props }) => (
  <DrawerPrimitive.Portal>
    <DrawerOverlay />
    <DrawerPrimitive.Content
      className={cn(
        "fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background",
        className
      )}
      {...props}
    >
      <div className="mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted" />
      {children}
    </DrawerPrimitive.Content>
  </DrawerPrimitive.Portal>
);
DrawerContent.displayName = "DrawerContent";

const DrawerHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("grid gap-1.5 p-4 text-center sm:text-left", className)}
    {...props}
  />
);
DrawerHeader.displayName = "DrawerHeader";

const DrawerFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("mt-auto flex flex-col gap-2 p-4", className)}
    {...props}
  />
);
DrawerFooter.displayName = "DrawerFooter";

// Add explicit type annotation to fix the TypeScript error
const DrawerTitle: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Title>
> = ({ className, ...props }) => (
  <DrawerPrimitive.Title
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
);
DrawerTitle.displayName = "DrawerTitle";

// Add explicit type annotation to fix the TypeScript error
const DrawerDescription: React.FC<
  React.ComponentProps<typeof DrawerPrimitive.Description>
> = ({ className, ...props }) => (
  <DrawerPrimitive.Description
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
);
DrawerDescription.displayName = "DrawerDescription";

export {
  Drawer,
  DrawerTrigger,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerFooter,
  DrawerTitle,
  DrawerDescription,
  DrawerOverlay,
};
