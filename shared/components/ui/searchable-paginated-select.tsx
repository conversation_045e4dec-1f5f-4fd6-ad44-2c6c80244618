import { useLayoutEffect, useRef, useState } from "react";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { Popover, PopoverContentNoPortal, PopoverTrigger } from "./popover";
import { But<PERSON> } from "./button";
import { Check, ChevronDown, ChevronsUpDown } from "lucide-react";
import { Command } from "cmdk";
import {
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "./command";
import { cn } from "../../lib/utils";
import { nanoid } from "nanoid";
import { Input } from "./input";
import { useDebounce } from "../../hooks/use-debounce";

interface ListContent {
  value: string;
  label: string;
}

interface ApiPage {
  items: ListContent[];
  nextPage?: number;
}

export default function SearchablePaginatedSelect(props: {
  value: string | null;
  setValue: (value: string) => void;
  fetcher: (page: number, search: string) => Promise<ApiPage>;
  labeler: (value: string | null) => Promise<string | null>;
  disabled?: boolean;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyText?: string;
}) {
  const [open, setOpen] = useState(false);
  const [_search, setSearch] = useState("");
  const search = useDebounce(_search, 500);
  const idRef = useRef(nanoid());
  const [width, setWidth] = useState(0);
  const triggerRef = useRef<HTMLButtonElement>(null);

  const { value, setValue, fetcher, labeler } = props;

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } =
    useInfiniteQuery<ApiPage>({
      queryFn: ({ pageParam = 1 }) => fetcher(pageParam as number, search), // todo: type
      queryKey: ["auto-form-select-search", idRef.current, search],
      getNextPageParam: (lastPage) => lastPage.nextPage,
      initialPageParam: 1,
      enabled: open,
    });

  const { data: nameData } = useQuery({
    queryKey: ["auto-form-select-name", idRef.current, value],
    // todo: any
    queryFn: labeler.bind(null, value),
  });

  useLayoutEffect(() => {
    if (open && triggerRef.current) {
      setWidth(triggerRef.current.offsetWidth);
    }
  }, [open]);

  const onScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    if (
      hasNextPage &&
      !isFetchingNextPage &&
      target.scrollHeight - target.scrollTop <= target.clientHeight + 80
    ) {
      fetchNextPage();
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          ref={triggerRef}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)] ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 font-normal ",
            !nameData && "text-gray-500"
          )}
          disabled={props.disabled}
        >
          {(nameData as string) ?? props.placeholder ?? ""}
          <ChevronDown className="opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContentNoPortal
        className="w-full p-0 pointer-events-auto"
        onWheel={(e) => {
          e.stopPropagation();
        }}
      >
        <Command>
          <Input
            placeholder={props.searchPlaceholder ?? ""}
            className="border-none outline-none focus-visible:ring-0 focus-visible:border-none shadow-none"
            value={_search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <CommandList
            className="max-h-[200px]"
            onScroll={onScroll}
            style={{ width }}
          >
            <CommandEmpty>{props.emptyText ?? ""}</CommandEmpty>
            <CommandGroup>
              {(data?.pages ?? []).flatMap((page) =>
                page.items.map((item) => (
                  <CommandItem
                    key={item.value}
                    value={item.value}
                    onSelect={(currentValue) => {
                      setValue(currentValue === value ? "" : currentValue);
                      setOpen(false);
                    }}
                  >
                    {item.label}
                    <Check
                      className={cn(
                        "ml-auto",
                        value === item.value ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContentNoPortal>
    </Popover>
  );
}
