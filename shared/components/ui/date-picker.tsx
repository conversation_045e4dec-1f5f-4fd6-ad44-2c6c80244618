"use client";
import { <PERSON><PERSON> } from "./button";
import { Calendar, type CalendarProps } from "./calendar";

import {
  Popover,
  PopoverContent,
  PopoverContentNoPortal,
  PopoverTrigger,
} from "./popover";
import { cn } from "../../lib/utils";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { forwardRef } from "react";
import { useTranslation } from "react-i18next";
import { useDateLocale } from "../../hooks/use-date-locale";

export const DatePicker = forwardRef<
  HTMLDivElement,
  {
    date?: Date;
    setDate: (date?: Date) => void;
    className?: string;
    placeholder?: string;
    disableBefore?: Date,
    disableAfter?: Date
    disabled?: boolean
  }
>(({ date, setDate, className, placeholder, disableBefore, disableAfter, disabled }, ref) => {
  const { t } = useTranslation("common");
  const locale = useDateLocale();

  return (
    <Popover modal>
      <PopoverTrigger asChild>
        <Button
          disabled={disabled}
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? (
            format(date, "PPP", {
              locale,
            })
          ) : (
            <span>{ placeholder ?? t("pick_date")}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0 z-[10000]" ref={ref}>
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          initialFocus
          disabled={{
            before: disableBefore,
            after: disableAfter!,
          }}
        />
      </PopoverContent>
    </Popover>
  );
});
