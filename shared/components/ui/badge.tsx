import type { VariantProps } from 'class-variance-authority'
import { cn } from '../../lib/utils'
import { cva } from 'class-variance-authority'
import * as React from 'react'

const badgeVariants = cva(
    'inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    {
        variants: {
            variant: {
                default:
          'border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80',
                secondary:
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
                destructive:
          'border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80',
                outline: 'text-foreground',
                "outline-primary": 'text-primary border-primary/50 bg-primary/5',
            },
            color: {
                red: 'bg-red-500 text-white hover:bg-red-600 border-transparent',
                green: 'bg-green-500 text-white hover:bg-green-600 border-transparent',
                blue: 'bg-blue-500 text-white hover:bg-blue-600 border-transparent',
                yellow: 'bg-yellow-500 text-black hover:bg-yellow-600 border-transparent',
                purple: 'bg-purple-500 text-white hover:bg-purple-600 border-transparent',
                gray: 'bg-gray-500 text-white hover:bg-gray-600 border-transparent',
            },
        },
        compoundVariants: [
            {
                variant: 'outline',
                color: 'red',
                className: 'bg-transparent border-red-200 bg-red-50 text-red-500 hover:bg-red-50',
            },
            {
                variant: 'outline',
                color: 'green',
                className: 'bg-transparent border-green-200 bg-green-50 text-green-500 hover:bg-green-50',
            },
            {
                variant: 'outline',
                color: 'blue',
                className: 'bg-transparent border-blue-200 bg-blue-50 text-blue-500 hover:bg-blue-50',
            },
            {
                variant: 'outline',
                color: 'yellow',
                className: 'bg-transparent border-yellow-200 bg-yellow-50 text-yellow-500 hover:bg-yellow-50',
            },
            {
                variant: 'outline',
                color: 'purple',
                className: 'bg-transparent border-purple-200 bg-purple-50 text-purple-500 hover:bg-purple-50',
            },
            {
                variant: 'outline',
                color: 'gray',
                className: 'bg-transparent border-gray-200 bg-gray-50 text-gray-500 hover:bg-gray-50',
            },
        ],
        defaultVariants: {
            variant: 'default',
        },
    },
)

export interface BadgeProps
    extends Omit<React.HTMLAttributes<HTMLDivElement>, 'color'>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, color, ...props }: BadgeProps) {
    return (
        <div className={cn(badgeVariants({ variant, color }), className)} {...props} />
    )
}

export { Badge, badgeVariants }
