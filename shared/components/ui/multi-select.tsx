import { cva, type VariantProps } from "class-variance-authority";
import {
  ChevronDown,
  Search,
  WandSparkles,
  XCircle
} from "lucide-react";
import * as React from "react";
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";

import { useTranslation } from "react-i18next";
import { cn } from "../../lib/utils";
import { Badge } from "./badge";
import { Button } from "./button";
import { Checkbox } from "./checkbox";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList
} from "./command";
import { Input } from "./input";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";

/**
 * Variants for the multi-select component to handle different styles.
 * Uses class-variance-authority (cva) to define different styles based on "variant" prop.
 */
const multiSelectVariants = cva(
  "m-1 transition ease-in-out delay-150 duration-300",
  {
    variants: {
      variant: {
        default:
          "border-foreground/10 text-foreground bg-card hover:bg-card/80",
        secondary:
          "border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        inverted: "inverted",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const CheckboxItem = React.memo(({ 
  checked, 
  onChange, 
  className, 
  children 
}: { 
  checked: boolean; 
  onChange: () => void; 
  className?: string; 
  children: React.ReactNode 
}) => {
  return (
    <Checkbox
      checked={checked}
      onCheckedChange={onChange}
      onClick={(e) => e.stopPropagation()}
      className={className}
    >
      {children}
    </Checkbox>
  );
});
CheckboxItem.displayName = "CheckboxItem";

/**
 * Props for MultiSelect component
 */
interface MultiSelectProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof multiSelectVariants> {
  /**
   * An array of option objects to be displayed in the multi-select component.
   * Each option object has a label, value, and an optional icon.
   */
  options: {
    /** The text to display for the option. */
    label: string;
    /** The unique value associated with the option. */
    value: string;
    /** Optional icon component to display alongside the option. */
    icon?: React.ComponentType<{ className?: string }>;
  }[];

  /**
   * Callback function triggered when the selected values change.
   * Receives an array of the new selected values.
   */
  onValueChange: (value: string[]) => void;

  /** The default selected values when the component mounts. */
  defaultValue?: string[];

  /**
   * Placeholder text to be displayed when no values are selected.
   * Optional, defaults to "Select options".
   */
  placeholder?: string;

  /**
   * Animation duration in seconds for the visual effects (e.g., bouncing badges).
   * Optional, defaults to 0 (no animation).
   */
  animation?: number;

  /**
   * Maximum number of items to display. Extra selected items will be summarized.
   * Optional, defaults to 3.
   */
  maxCount?: number;

  /**
   * The modality of the popover. When set to true, interaction with outside elements
   * will be disabled and only popover content will be visible to screen readers.
   * Optional, defaults to false.
   */
  modalPopover?: boolean;

  /**
   * If true, renders the multi-select component as a child of another component.
   * Optional, defaults to false.
   */
  asChild?: boolean;

  /**
   * Additional class names to apply custom styles to the multi-select component.
   * Optional, can be used to add custom styles.
   */
  className?: string;
}

type MultiSelectState = {
  selectedValues: string[];
  isPopoverOpen: boolean;
  isAnimating: boolean;
  width: number;
  search: string;
}

export const MultiSelect = React.memo(({
  options,
  onValueChange,
  variant,
  defaultValue = [],
  placeholder = "Select options",
  animation = 0,
  maxCount = 3,
  modalPopover = false,
  asChild = false,
  className,
  state: __state,
  setState: __setState,
  ...props
}: MultiSelectProps & {
  state?: MultiSelectState,
  setState?: React.Dispatch<React.SetStateAction<MultiSelectState>>;
}) => {
  const [_state, _setState] = useState({
    selectedValues: defaultValue,
    isPopoverOpen: false,
    isAnimating: false,
    width: 0,
    search: ""
  });

  const state = __state || _state;
  const setState = __setState || _setState;
  
  const triggerRef = useRef<HTMLButtonElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { t } = useTranslation("common");
  
  const updateState = useCallback((updates: Partial<typeof state>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);
  
  useEffect(() => {
    if (state.isPopoverOpen) {
      updateState({ search: "" });
    }
  }, [state.isPopoverOpen, updateState]);

  const filteredOptions = useMemo(() => {
    return options.filter((o) => 
      o.label.toLowerCase().includes(state.search.toLowerCase())
    );
  }, [options, state.search]);

  const handleInputKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();
    } else if (event.key === "Backspace" && !event.currentTarget.value) {
      const newSelectedValues = [...state.selectedValues];
      newSelectedValues.pop();
      

      setState(prev => {
        onValueChange(newSelectedValues);
        return { ...prev, selectedValues: newSelectedValues };
      });
    }
  }, [state.selectedValues, onValueChange]);

  useLayoutEffect(() => {
    if (state.isPopoverOpen && triggerRef.current) {
      updateState({ width: triggerRef.current.offsetWidth });
    }
  }, [state.isPopoverOpen, updateState]);

  const toggleOption = useCallback((option: string) => {
    setState(prev => {
      const newValues = prev.selectedValues.includes(option)
        ? prev.selectedValues.filter((value) => value !== option)
        : [...prev.selectedValues, option];
      
      // Call onValueChange synchronously
      onValueChange(newValues);
      
      return {
        ...prev,
        selectedValues: newValues
      };
    });
  }, [onValueChange]);

  const handleClear = useCallback(() => {
    setState(prev => {
      // Call onValueChange synchronously
      onValueChange([]);
      return { ...prev, selectedValues: [] };
    });
  }, [onValueChange]);

  const handleTogglePopover = useCallback(() => {
    updateState({ isPopoverOpen: !state.isPopoverOpen });
  }, [state.isPopoverOpen, updateState]);

  const clearExtraOptions = useCallback(() => {
    setState(prev => {
      const newValues = prev.selectedValues.slice(0, maxCount);
      // Call onValueChange synchronously
      onValueChange(newValues);
      
      return {
        ...prev,
        selectedValues: newValues
      };
    });
  }, [maxCount, onValueChange]);

  // Toggle all handler
  const toggleAll = useCallback(() => {
    setState(prev => {
      if (prev.selectedValues.length === options.length) {
        // Clear all values
        onValueChange([]);
        return { ...prev, selectedValues: [] };
      } else {
        // Select all values
        const allValues = options.map((option) => option.value);
        onValueChange(allValues);
        return { ...prev, selectedValues: allValues };
      }
    });
  }, [options, onValueChange]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateState({ search: e.target.value });
  }, [updateState]);

  const isAllSelected = useMemo(() => 
    state.selectedValues.length === options.length, 
    [state.selectedValues.length, options.length]
  );
  
  const toggleAnimation = useCallback(() => {
    updateState({ isAnimating: !state.isAnimating });
  }, [state.isAnimating, updateState]);

  return (
    <Popover
      open={state.isPopoverOpen}
      onOpenChange={(open) => {
        updateState({ isPopoverOpen: open });
      }}
      modal={modalPopover}
    >
      <PopoverTrigger asChild>
        <Button
          ref={triggerRef}
          onClick={handleTogglePopover}
          variant="outline"
          className={cn(
            "flex min-h-9 h-auto w-full items-center justify-between whitespace-nowrap rounded-lg border border-gray-300 bg-transparent px-1 py-1 text-sm shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)] ring-offset-background focus:outline-none focus:ring-2 focus:ring-primary disabled:cursor-not-allowed disabled:opacity-50",
            multiSelectVariants({ variant }),
            className
          )}
          {...props}
        >
          {state.selectedValues.length > 0 ? (
            <div className="flex justify-between items-center w-full">
              <div className="flex flex-wrap items-center">
                {state.selectedValues.slice(0, maxCount).map((value) => {
                  const option = options.find((o) => o.value === value);
                  const IconComponent = option?.icon;
                  return (
                    <Badge
                      key={value}
                      onClick={(event) => {
                        event.stopPropagation();
                        toggleOption(value);
                      }}
                      className={cn(
                        "cursor-pointer",
                        state.isAnimating ? "animate-bounce" : "",
                        multiSelectVariants({ variant }),
                        "relative z-40"
                      )}
                      style={{ animationDuration: `${animation}s` }}
                    >
                      {IconComponent && (
                        <IconComponent className="h-4 w-4 mr-2" />
                      )}
                      {option?.label}
                      <XCircle
                        className="ml-2 h-4 w-4 cursor-pointer pointer-events-auto"
                        onClick={(event) => {
                          event.stopPropagation();
                          toggleOption(value);
                        }}
                      />
                    </Badge>
                  );
                })}
                {state.selectedValues.length > maxCount && (
                  <Badge
                    onClick={(event) => {
                      event.stopPropagation();
                      clearExtraOptions();
                    }}
                    className={cn(
                      "cursor-pointer",
                      "bg-transparent text-foreground border-foreground/1 hover:bg-transparent",
                      state.isAnimating ? "animate-bounce" : "",
                      multiSelectVariants({ variant })
                    )}
                    style={{ animationDuration: `${animation}s` }}
                  >
                    {`+ ${state.selectedValues.length - maxCount} more`}
                    <XCircle
                      className="ml-2 h-4 w-4 cursor-pointer pointer-events-auto"
                      onClick={(event) => {
                        event.stopPropagation();
                        clearExtraOptions();
                      }}
                    />
                  </Badge>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between w-full mx-auto">
              <span className="text-sm text-muted-foreground mx-2">
                {placeholder}
              </span>
              <ChevronDown className="h-4 cursor-pointer text-muted-foreground mx-2" />
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-auto p-0 rounded-md border bg-popover shadow-md pointer-events-auto"
        align="start"
        onEscapeKeyDown={() => updateState({ isPopoverOpen: false })}
        onOpenAutoFocus={e => e.preventDefault()}
        onCloseAutoFocus={e => e.preventDefault()}
        onWheel={(e) => e.stopPropagation()}
        style={{ width: state.width > 0 ? state.width : undefined }}
      >
        <Command>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              ref={inputRef}
              placeholder={t("search")}
              value={state.search}
              onChange={handleSearchChange}
              onKeyDown={handleInputKeyDown}
              autoFocus
              onPointerDownCapture={(e) => e.stopPropagation()}
              onMouseDown={(e) => e.stopPropagation()}
              onTouchStart={(e) => e.stopPropagation()}
              className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none"
            />
          </div>
          <CommandList className="max-h-[300px] overflow-y-auto">
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              <CommandItem
                key="all"
                onSelect={toggleAll}
                className="cursor-pointer"
                onPointerDown={(e) => e.preventDefault()}
                onClick={(e) => e.preventDefault()}
              >
                <CheckboxItem
                  checked={isAllSelected}
                  onChange={toggleAll}
                  className="mr-2"
                >
                  <span></span>
                </CheckboxItem>
                <span>({t("select_all")})</span>
              </CommandItem>
              {filteredOptions.map((option) => {
                const isSelected = state.selectedValues.includes(option.value);
                const handleOptionToggle = () => toggleOption(option.value);
                
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={handleOptionToggle}
                    className="cursor-pointer"
                    onPointerDown={(e) => e.preventDefault()}
                    onClick={(e) => e.preventDefault()}
                  >
                    <CheckboxItem
                      checked={isSelected}
                      onChange={handleOptionToggle}
                      className="mr-2"
                    >
                      <span></span>
                    </CheckboxItem>
                    {option.icon && (
                      <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                    )}
                    <span>{option.label}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
      {animation > 0 && state.selectedValues.length > 0 && (
        <WandSparkles
          className={cn(
            "cursor-pointer my-2 text-foreground bg-background w-3 h-3",
            state.isAnimating ? "" : "text-muted-foreground"
          )}
          onClick={toggleAnimation}
        />
      )}
    </Popover>
  );
});

MultiSelect.displayName = "MultiSelect";
