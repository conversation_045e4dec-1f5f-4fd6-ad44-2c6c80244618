import * as React from 'react';
import { cn } from '../../lib/utils';

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn('animate-pulse rounded-md bg-muted', className)}
      {...props}
    />
  );
}

export { Skeleton };

export interface TableSkeletonProps {
  columns: number
  rows?: number
}
export function TableSkeleton({ columns, rows = 5 }: TableSkeletonProps) {
  const header = Array.from({ length: columns }).map((_, i) => (
    <Skeleton key={i} className="h-5 w-full" />
  ))
  return (
    <div className="w-full space-y-2">
      <div
        className="grid gap-4"
        style={{ gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))` }}
      >
        {header}
      </div>
      {Array.from({ length: rows }).map((_, r) => (
        <div
          key={r}
          className="grid gap-4"
          style={{ gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))` }}
        >
          {header}
        </div>
      ))}
    </div>
  )
}
