import React from 'react';
import { But<PERSON> } from '../ui/button';
import { Menu } from 'lucide-react';
import { cn } from '../../lib/utils';
import { useSidebar } from '../ui/sidebar';

export function MobileMenuButton({ className, ...props }: React.ComponentProps<typeof Button>) {
  const { toggleSidebar, isMobile } = useSidebar();

  if (!isMobile) return null;
  
  return (
    <Button
      variant="ghost"
      size="icon"
      className={cn('h-9 w-9 md:hidden', className)}
      onClick={toggleSidebar}
      aria-label="Toggle mobile menu"
      {...props}
    >
      <Menu className="h-5 w-5" />
    </Button>
  );
}
