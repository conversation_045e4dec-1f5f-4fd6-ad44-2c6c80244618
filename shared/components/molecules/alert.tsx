import type { IconNames } from '../atoms/Iconify'
import Iconify from '../atoms/Iconify'
import { cn } from '../../lib/utils'

interface AlertProps {
    variant?: 'warning' | 'error' | 'success'
    iconless?: boolean
    title: string
    children: React.ReactNode
    actions?: React.ReactNode
}

const variantStyles = {
    warning: {
        bg: 'bg-yellow-50',
        title: 'text-yellow-800',
        content: 'text-yellow-700',
        icon: 'text-yellow-400',
    },
    error: {
        bg: 'bg-red-50',
        title: 'text-red-800',
        content: 'text-red-700',
        icon: 'text-red-400',
    },
    success: {
        bg: 'bg-green-50',
        title: 'text-green-800',
        content: 'text-green-700',
        icon: 'text-green-400',
    },
}

const icons: {
    [key: string]: IconNames
} = {
    warning: 'untitled:info-octagon',
    error: 'untitled:x-circle',
    success: 'untitled:check-circle',
}

export default function Alert({ variant = 'warning', title, children, actions, iconless }: AlertProps) {
    const styles = variantStyles[variant]
    const Icon = icons[variant]

    return (
        <div className={cn('rounded-md p-4', styles.bg)}>
            <div className="flex">
                {!iconless && (
                    <div className="shrink-0">
                        <Iconify name={Icon} className={cn('h-5 w-5', styles.icon)} />
                    </div>
                )}
                <div className="ml-3">
                    <h3 className={cn('text-sm font-medium', styles.title)}>{title}</h3>
                    <div className={cn('mt-2 text-sm', styles.content)}>{children}</div>
                    {actions && (
                        <div className="mt-4">
                            <div className="-mx-2 -my-1.5 flex">
                                {actions}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}
