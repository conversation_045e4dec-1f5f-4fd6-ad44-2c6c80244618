"use client";

import * as React from "react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { DateRange } from "react-day-picker";

import { cn } from "../../lib/utils";
import { Button } from "../../components/ui/button";
import { Calendar } from "../../components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../components/ui/popover";
import { useDateLocale } from "../../hooks/use-date-locale";
import { useTranslation } from "react-i18next";

export function DateRangePicker({
  className,
  value,
  setValue,
}: React.HTMLAttributes<HTMLDivElement> & {
  value?: DateRange;
  setValue: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
}) {
  const dateLocale = useDateLocale();
  const { t } = useTranslation("common");

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-[300px] justify-start text-left font-normal",
              !value && "text-muted-foreground"
            )}
          >
            <CalendarIcon />
            {value?.from ? (
              format(value.from, "LLL dd, y", {
                locale: dateLocale,
              }) +
              (value.to
                ? " - " +
                  format(value.to, "LLL dd, y", {
                    locale: dateLocale,
                  })
                : "")
            ) : (
              <span>{t("date_picker_placeholder")}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={value?.from}
            selected={value}
            onSelect={setValue}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
