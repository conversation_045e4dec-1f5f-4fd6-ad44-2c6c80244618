import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { useTranslation } from 'react-i18next'

interface LanguageSwitcherProps {
    changeLanguage: (language: string) => void;
}

export function LanguageSwitcher({ changeLanguage }: LanguageSwitcherProps) {
        const { t, i18n } = useTranslation('settings')

        return (
                <Select
                        value={i18n.language}
                        onValueChange={(value) => {
                                changeLanguage(value)
                        }}
                >
                        <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder={t('selectLangTitle')} />
                        </SelectTrigger>
                        <SelectContent>
                                <SelectItem value="en">{t('setLanguageEnglish')}</SelectItem>
                                <SelectItem value="tr">{t('setLanguageTurkish')}</SelectItem>
                        </SelectContent>
                </Select>
        )
}
