import { AnimatePresence, motion } from 'motion/react'
import React from 'react'

interface AnimatedTransitionProps {
    loading: boolean
    skeleton: React.ReactNode
    children: React.ReactNode
}

const skeletonVariants = {
    hidden: { opacity: 0, y: 0 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
}

const dataVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
}

const transition = {
    duration: 0.2,
    ease: [0.87, 0, 0.13, 1],
}

export function AnimatedTransition({ loading, skeleton, children }: AnimatedTransitionProps) {
    return (
        <AnimatePresence mode="wait">
            {loading
                ? (
                        <motion.div
                            key="skeleton"
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                            variants={skeletonVariants}
                            transition={transition}
                            className="h-full w-full"
                        >
                            {skeleton}
                        </motion.div>
                    )
                : (
                        <motion.div
                            key="data"
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                            variants={dataVariants}
                            transition={transition}
                            className="h-full w-full"
                        >
                            {children}
                        </motion.div>
                    )}
        </AnimatePresence>
    )
}
