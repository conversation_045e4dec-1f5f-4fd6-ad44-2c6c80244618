import type { Locale } from "date-fns";
import Iconify from "../../components/atoms/Iconify";
import { Button } from "../../components/ui/button";
import { Calendar } from "../../components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../components/ui/popover";
import { format } from "date-fns";
import React from "react";

interface CalendarPickerProps {
  locale: Locale;

  range: [Date | null, Date | null];
  onSelect: (range: [Date | null, Date | null]) => void;
  placeholder: React.ReactNode;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

export const CalendarPicker: React.FC<CalendarPickerProps> = ({
  locale,
  range,
  onSelect,
  placeholder,
  isOpen,
  setIsOpen,
}) => {
  const formattedRange =
    range[0] && range[1]
      ? `${format(range[0], "PPP", { locale })} - ${format(range[1], "PPP", { locale })}`
      : placeholder;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-full">
          <Iconify name="untitled:calendar" className="size-5" />
          {formattedRange}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="end">
        <Calendar
          initialFocus
          mode="range"
          defaultMonth={range[0] || undefined}
          selected={
            range[0] ? { from: range[0], to: range[1] || undefined } : undefined
          }
          onSelect={(d) => {
            const { from, to } = d || { from: null, to: null };
            onSelect([from || null, to || null]);

            if (from && to) setIsOpen(false);
          }}
          numberOfMonths={1}
          locale={locale}
        />
      </PopoverContent>
    </Popover>
  );
};
