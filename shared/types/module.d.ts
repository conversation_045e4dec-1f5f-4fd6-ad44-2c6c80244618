// https://vitejs.dev/guide/api-hmr.html
interface ViteHotContext {
    readonly data: any

    // accept(): void
    accept: ((cb?: (mod: ModuleNamespace | undefined) => void) => void) & ((dep: string, cb: (mod: ModuleNamespace | undefined) => void) => void) & ((deps: readonly string[], cb: (mods: Array<ModuleNamespace | undefined>) => void) => void)

    dispose: (cb: (data: any) => void) => void
    decline: () => void
    invalidate: () => void

    // `InferCustomEventPayload` provides types for built-in Vite events
    on: <T extends string>(event: T, cb: (payload: InferCustomEventPayload<T>) => void) => void
    send: <T extends string>(event: T, data?: InferCustomEventPayload<T>) => void
}

// Allow for virtual module imports
// https://vitejs.dev/guide/api-plugin.html#virtual-modules-convention
declare module 'virtual:*'

declare module '*.svg' {
  const content: React.FunctionComponent<React.SVGAttributes<SVGElement>>;
  export default content;
}

declare module '*.png' {
  const content: string;
  export default content;
}

declare module '*.jpg' {
  const content: string;
  export default content;
}

declare module '*.json' {
  const content: any;
  export default content;
}

declare module 'virtual:i18next-loader' {
  const resources: any;
  export default resources;
}
