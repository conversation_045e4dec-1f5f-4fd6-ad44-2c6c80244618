import React from 'react'

interface SearchContextType {
    open: boolean
    setOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const SearchContext = React.createContext<SearchContextType | null>(null)

interface Props {
    children: React.ReactNode
}

export function SearchProvider({ children }: Props) {
    const [open, setOpen] = React.useState(false)

    return (
        <SearchContext.Provider value={{ open, setOpen }}>
            {children}
        </SearchContext.Provider>
    )
}

export function useSearch() {
    const searchContext = React.useContext(SearchContext)

    if (!searchContext) {
        throw new Error('useSearch has to be used within <SearchContext.Provider>')
    }

    return searchContext
}
