{
    "compilerOptions": {
        "tsBuildInfoFile": "../node_modules/.tmp/tsconfig.node.tsbuildinfo",
        "target": "ES2022",
        "lib": ["ES2023"],
        "moduleDetection": "force",
        "module": "ESNext",

        /* Bundler mode */
        "moduleResolution": "bundler",

        /* Linting */
        "strict": true,
        "noFallthroughCasesInSwitch": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "isolatedModules": true,
        "skipLibCheck": true,
        "noUncheckedSideEffectImports": true,
        "types": ["vite/client"],
        "composite": true
    },
    "include": ["../vite.config.ts", "../src/**/*.ts", "../types/**/*.d.ts"]
}