{
    "compilerOptions": {
        "composite": true,
        "tsBuildInfoFile": "../node_modules/.tmp/tsconfig.app.tsbuildinfo",
        "target": "ES2020",
        "jsx": "react-jsx",
        "lib": ["ES2020", "DOM", "DOM.Iterable"],
        "moduleDetection": "force",
        "useDefineForClassFields": true,
        "module": "ESNext",

        /* Bundler mode */
        "moduleResolution": "bundler",
        "paths": {
            "@/*": [
                "../src/*"
            ],
            "@/constants/*": [
                "../src/constants/*"
            ]
        },
        "declaration": true,
        "emitDeclarationOnly": true,
        "outDir": "../dist",

        /* Linting */
        "strict": true,
        "noFallthroughCasesInSwitch": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "isolatedModules": true,
        "skipLibCheck": true,
        "noUncheckedSideEffectImports": true,
        "types": ["vite/client"],
        "downlevelIteration": true
    },
    "include": [
        "../src/**/*.ts", 
        "../src/**/*.tsx", 
        "../types/**/*.d.ts"
    ],
    "exclude": ["node_modules", "dist"]
}