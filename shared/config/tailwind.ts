import { fontFamily } from 'tailwindcss/defaultTheme'

const theme = {
    container: {
        center: 'true',
        padding: '2rem',
        screens: {
            '2xl': '1400px',
        },
    },
    extend: {
        fontFamily: {
            inter: [
                'Inter',
                ...fontFamily.sans,
            ],
            manrope: [
                'Manrope',
                ...fontFamily.sans,
            ],
        },
        fontSize: {
            'base': '16px',
            '5xl': '24px',
            'sm': '14px',
            'inherit': 'inherit',
        },
        borderRadius: {
            lg: 'var(--radius)',
            md: 'calc(var(--radius) - 2px)',
            sm: 'calc(var(--radius) - 4px)',
        },
        colors: {
            'base-black': '#000',
            'base-white': '#fff',
            'brand-300': '#d6bbfb',
            'brand-600': '#7f56d9',
            'dimgray': 'rgba(82, 82, 82, 0.5)',
            'error-500': '#f04438',
            'error-600': '#d92d20',
            'gray-50': '#fafafa',
            'gray-100': '#f5f5f5',
            'gray-200': '#e9eaeb',
            'gray-300': '#d5d7da',
            'gray-400': '#a4a7ae',
            'gray-500': '#717680',
            'gray-600': '#535862',
            'gray-700': '#414651',
            'gray-800': '#252b37',
            'gray-900': '#181d27',
            'orange-500': '#ef6820',
            'orange-600': '#e04f16',
            'success-50': '#ecfdf3',
            'success-300': '#75e0a7',
            'success-500': '#17b26a',
            'success-600': '#079455',
            'success-700': '#067647',
            'gradient-skeuemorphic-gradient-border': 'rgba(255, 255, 255, 0.12)',
            'accent': {
                DEFAULT: 'hsl(var(--accent))',
                foreground: 'hsl(var(--accent-foreground))',
            },
            'background': 'hsl(var(--background))',
            'border': 'hsl(var(--border))',
            'card': {
                DEFAULT: 'hsl(var(--card))',
                foreground: 'hsl(var(--card-foreground))',
            },
            'chart': {
                1: 'hsl(var(--chart-1))',
                2: 'hsl(var(--chart-2))',
                3: 'hsl(var(--chart-3))',
                4: 'hsl(var(--chart-4))',
                5: 'hsl(var(--chart-5))',
            },
            'destructive': {
                DEFAULT: 'hsl(var(--destructive))',
                foreground: 'hsl(var(--destructive-foreground))',
            },
            'foreground': 'hsl(var(--foreground))',
            'input': 'hsl(var(--input))',
            'muted': {
                DEFAULT: 'hsl(var(--muted))',
                foreground: 'hsl(var(--muted-foreground))',
            },
            'popover': {
                DEFAULT: 'hsl(var(--popover))',
                foreground: 'hsl(var(--popover-foreground))',
            },
            'primary': {
                DEFAULT: 'hsl(var(--primary))',
                foreground: 'hsl(var(--primary-foreground))',
            },
            'ring': 'hsl(var(--ring))',
            'secondary': {
                DEFAULT: 'hsl(var(--secondary))',
                foreground: 'hsl(var(--secondary-foreground))',
            },
            'sidebar': {
                'DEFAULT': 'hsl(var(--sidebar-background))',
                'foreground': 'hsl(var(--sidebar-foreground))',
                'primary': 'hsl(var(--sidebar-primary))',
                'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
                'accent': 'hsl(var(--sidebar-accent))',
                'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
                'border': 'hsl(var(--sidebar-border))',
                'ring': 'hsl(var(--sidebar-ring))',
            },
        },
        keyframes: {
            'accordion-down': {
                from: {
                    height: '0',
                },
                to: {
                    height: 'var(--radix-accordion-content-height)',
                },
            },
            'accordion-up': {
                from: {
                    height: 'var(--radix-accordion-content-height)',
                },
                to: {
                    height: '0',
                },
            },
        },
        animation: {
            'accordion-down': 'accordion-down 0.2s ease-out',
            'accordion-up': 'accordion-up 0.2s ease-out',
        },
    },
}

export { theme }