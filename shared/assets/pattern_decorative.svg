<svg width="480" height="480" viewBox="0 0 480 480" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_1144_19170" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="480" height="480">
<rect width="480" height="480" fill="url(#paint0_radial_1144_19170)"/>
</mask>
<g mask="url(#mask0_1144_19170)">
<circle cx="240" cy="240" r="47.5" stroke="#E9EAEB"/>
<circle cx="240" cy="240" r="79.5" stroke="#E9EAEB"/>
<circle cx="240" cy="240" r="111.5" stroke="#E9EAEB"/>
<circle cx="240" cy="240" r="143.5" stroke="#E9EAEB"/>
<circle cx="240" cy="240" r="143.5" stroke="#E9EAEB"/>
<circle cx="240" cy="240" r="175.5" stroke="#E9EAEB"/>
<circle cx="240" cy="240" r="207.5" stroke="#E9EAEB"/>
<circle cx="240" cy="240" r="239.5" stroke="#E9EAEB"/>
</g>
<defs>
<radialGradient id="paint0_radial_1144_19170" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(240 240) rotate(90) scale(240 240)">
<stop/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
