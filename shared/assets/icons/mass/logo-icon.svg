<svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ddiii_3338_315353)">
<g clip-path="url(#clip0_3338_315353)">
<rect x="3" width="48" height="48" rx="12" fill="#FF4405"/>
<rect width="48" height="48" transform="translate(3)" fill="url(#paint0_linear_3338_315353)"/>
<g filter="url(#filter1_d_3338_315353)">
<path d="M38 36.7408V13.2592C38 11.4529 36.6505 10 34.993 10H19.007C17.3495 10 16 11.4627 16 13.2592V36.7408C16 38.5471 17.3495 40 19.007 40H34.9839C36.6414 40 37.9909 38.5373 37.9909 36.7408" fill="url(#paint1_linear_3338_315353)" shape-rendering="crispEdges"/>
</g>
<path d="M27.13 22.7H31.63L26.31 33.27L27.36 26.01H22L25.04 16H29.23L27.13 22.7Z" fill="#FF4405"/>
</g>
<rect x="4" y="1" width="46" height="46" rx="11" stroke="url(#paint2_linear_3338_315353)" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_ddiii_3338_315353" x="0" y="-3" width="54" height="57" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.162923 0 0 0 0 0.162923 0 0 0 0 0.162923 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3338_315353"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect2_dropShadow_3338_315353"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.164706 0 0 0 0 0.164706 0 0 0 0 0.164706 0 0 0 0.14 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_3338_315353" result="effect2_dropShadow_3338_315353"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_3338_315353" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_3338_315353"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_3338_315353" result="effect4_innerShadow_3338_315353"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect5_innerShadow_3338_315353"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_3338_315353" result="effect5_innerShadow_3338_315353"/>
</filter>
<filter id="filter1_d_3338_315353" x="3" y="-79" width="35" height="119" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-9" dy="-85"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.291748 0 0 0 0 0.291748 0 0 0 0 0.291748 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3338_315353"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3338_315353" result="shape"/>
</filter>
<linearGradient id="paint0_linear_3338_315353" x1="24" y1="5.96047e-07" x2="26" y2="48" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0.12"/>
</linearGradient>
<linearGradient id="paint1_linear_3338_315353" x1="27" y1="10" x2="27" y2="36.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_3338_315353" x1="27" y1="0" x2="27" y2="48" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.12"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_3338_315353">
<rect x="3" width="48" height="48" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
