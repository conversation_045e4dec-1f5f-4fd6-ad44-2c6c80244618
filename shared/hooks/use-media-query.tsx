import { useEffect, useState } from 'react'

function useMediaQuery(query: string) {
    const [matches, setMatches] = useState(
        () => typeof window !== 'undefined' && window.matchMedia(query).matches,
    )

    useEffect(() => {
        const media = window.matchMedia(query)
        const listener = () => setMatches(media.matches)

        media.addEventListener('change', listener)
        return () => {
            media.removeEventListener('change', listener)
        }
    }, [query])

    return matches
}

export default useMediaQuery
