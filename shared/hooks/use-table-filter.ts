import { useState } from 'react'

interface DateRange {
    from: Date | null
    to: Date | null
}

export function useTableFilter() {
    const [searchQuery, setSearchQuery] = useState('')
    const [dateRange, setDateRange] = useState<DateRange>({ from: null, to: null })
    const [isCalendarOpen, setIsCalendarOpen] = useState(false)

    const isDateSelected = dateRange.from !== null


    return { searchQuery, setSearchQuery, dateRange, setDateRange, isCalendarOpen, setIsCalendarOpen, isDateSelected }
}
