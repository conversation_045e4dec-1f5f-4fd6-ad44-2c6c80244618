import { useQuery, UseQueryResult } from '@tanstack/react-query';

/**
 * Type definitions for the global settings structure
 */
export interface GlobalSettings {
  complaints: {
    value: {
      categories: {
        complaint: {
          label: {
            EN: string;
            TR: string;
          };
          subcategories: {
            [key: string]: {
              label: {
                EN: string;
                TR: string;
              };
            };
          };
        };
        suggestion: {
          label: {
            EN: string;
            TR: string;
          };
          subcategories: {
            [key: string]: {
              label: {
                EN: string;
                TR: string;
              };
            };
          };
        };
      };
    }
  };
  documents: {
    faq: {
      value: Array<{
        question: {
          EN: string;
          TR: string;
        };
        answer: {
          EN: string;
          TR: string;
        };
      }>;
    };
    pdf: {
      value: {
        about: { url: string };
        kvkk: { url: string };
        agreement: { url: string };
        manual: { url: string };
      }
    };
    upload: {
      value: {
        limits: {
          size: number;
          mimetypes: string[];
        };
      }
    };
  };
  notifications: {
    categories: {
      [key: string]: {
        label: {
          EN: string;
          TR: string;
        };
      };
    };
    defaults: {
      unexpected: {
        threshold: number;
      };
    };
  };
  subscriptions: {
    usage: {
      range: {
        time: {
          hour: number;
          day: number;
          month: number;
          year: number;
        };
      };
    };
  };
}

interface UseGlobalSettingsOptions {
  /**
   * Time in milliseconds after which the data is considered stale. Defaults to 5 minutes.
   */
  staleTime?: number;
  
  /**
   * Whether to fetch the data on mount. Defaults to true.
   */
  enabled?: boolean;
}

// Type helper to get the type for a specific path in the GlobalSettings object
type PathValue<T, P extends string> = 
  P extends `${infer K}.${infer Rest}` 
    ? K extends keyof T 
      ? PathValue<T[K], Rest> 
      : unknown 
    : P extends keyof T 
      ? T[P] 
      : unknown;

/**
 * Custom hook for fetching global settings from the API using the provided api function.
 * 
 * @param keyPath - The dot-notation path to the specific setting to fetch (e.g., 'complaints.categories')
 * @param api - The api function to use for the request
 * @param options - Optional configuration options
 * @returns The query result containing the requested settings data
 * 
 * @example
 * // Fetch all complaint categories
 * const { data, isLoading, error } = useGlobalSettings('complaints.categories', api);
 * 
 * // Fetch subscription usage range settings
 * const { data } = useGlobalSettings('subscriptions.usage.range', api);
 */
export function useGlobalSettings<
  TPath extends string,
  TData = PathValue<GlobalSettings, TPath>
>(
  keyPath: TPath,
  api: (endpoint: string, options?: RequestInit) => Promise<any>,
  options: UseGlobalSettingsOptions = {}
): UseQueryResult<TData, Error> {
  const {
    staleTime = 5 * 60 * 1000, // 5 minutes
    enabled = true,
  } = options;

  return useQuery<TData>({
    queryKey: ['globalSettings', keyPath],
    queryFn: async () => {
      const endpoint = `/setting/global/${keyPath}`;
      return (await api(endpoint)) as TData;
    },
    staleTime,
    enabled,
  });
}

export function useManyGlobalSettings(keyPaths: string[], api: (endpoint: string, options?: RequestInit) => Promise<any>) {
  return useQuery({
    queryKey: ['globalSettings', ...keyPaths],
    queryFn: async () => {
      const endpoints = keyPaths.map((keyPath) => `/setting/global/${keyPath}`);
      const responses = await Promise.all(endpoints.map((endpoint) => api(endpoint)));
      return Object.fromEntries(keyPaths.map((keyPath, index) => [keyPath, responses[index]?.value]));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: keyPaths.length > 0,
  });
}

/**
 * Type-safe utility functions for specific global settings
 */
export const globalSettingsKeys = {
  complaints: {
    categories: () => 'complaints.categories' as const,
    all: () => 'complaints' as const,
  },
  documents: {
    faq: () => 'documents.faq' as const,
    pdf: () => 'documents.pdf' as const,
    upload: {
      limits: () => 'documents.upload.limits' as const,
    },
  },
  notifications: {
    categories: () => 'notifications.categories' as const,
    defaults: () => 'notifications.defaults' as const,
  },
  subscriptions: {
    usage: {
      range: () => 'subscriptions.usage.range' as const,
    },
  },
};