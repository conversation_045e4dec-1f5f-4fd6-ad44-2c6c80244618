function validateTaxIdentificationNumber(value: string): boolean {
    if (!/^\d{10}$/.test(value)) {
        return false
    }

    const digits = value.split('').map(Number)

    let sum = 0
    for (let i = 0; i < 9; i++) {
        let c = digits[i] + (9 - i)
        if (c >= 10) {
            c -= 9
        }
        sum += c
    }

    const checkDigit = (sum * 9) % 10

    return checkDigit === digits[9]
}

function validateTurkishID(tcno: string) {
    if (!/^\d{11}$/.test(tcno))
        return false

    if (tcno[0] === '0')
        return false

    let odd = 0
    let even = 0
    let sum = 0
    const check1 = Number.parseInt(tcno[9])
    const check2 = Number.parseInt(tcno[10])

    for (let i = 0; i < 9; i++) {
        const digit = Number.parseInt(tcno[i])
        if (i % 2 === 0)
            odd += digit
        else even += digit
        sum += digit
    }

    const check1Valid = (odd * 7 - even) % 10 === check1
    const check2Valid = (sum + check1) % 10 === check2

    return check1Valid && check2Valid
}

export { validateTaxIdentificationNumber, validateTurkishID }
