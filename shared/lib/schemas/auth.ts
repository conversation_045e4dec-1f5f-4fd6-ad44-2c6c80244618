import * as z from 'zod'
import { validateTurkishID } from './validation'

export const loginSchema = z.object({
    email: z.string().email('auth:invalid_email'),
    password: z.string().min(6, 'auth:password_min_length'),
})

export const registerSchema = z
    .object({
        tcNo: z
            .string()
            .length(11, 'auth:tc_id_length')
            .regex(/^\d+$/, 'auth:tc_id_digits_only')
            .refine(validateTurkishID, 'auth:tc_id_invalid'),
        phone: z
            .string()
            .regex(
                /^5\d{9}$|^5\d{2}\s\d{3}\s\d{2}\s\d{2}$/,
                'auth:invalid_phone',
            )
            .transform((val) => {
                const cleaned = val.replace(/\s+/g, '')
                return cleaned.replace(/(\d{3})(\d{3})(\d{2})(\d{2})/, '$1 $2 $3 $4')
            }),
        email: z.string().email('auth:invalid_email'),
        password: z.string().min(6, 'auth:password_min_length'),
        confirmPassword: z.string().min(6, 'auth:password_min_length'),
        acceptTerms: z.boolean().refine(val => val === true, {
            message: 'auth:terms_required',
        }),
    })
    .refine(data => data.password === data.confirmPassword, {
        message: 'auth:passwords_not_match',
        path: ['confirmPassword'],
    })

export const forgotPasswordSchema = z.object({
    email: z.string().email('auth:invalid_email'),
})

export const resetPasswordSchema = z
    .object({
        password: z
            .string()
            .min(8, 'auth:password_min_length')
            .regex(
                /^(?=.*[a-z])(?=.*[A-Z!@#$%^&*(),.?":{}|<>])(?=.*\d).{8,}$/,
                'auth:password_requirements',
            ),
        confirmPassword: z.string(),
    })
    .refine(data => data.password === data.confirmPassword, {
        message: 'auth:passwords_not_match',
        path: ['confirmPassword'],
    })

export type LoginFormType = z.infer<typeof loginSchema>
export type RegisterFormType = z.infer<typeof registerSchema>
export type ForgotPasswordFormType = z.infer<typeof forgotPasswordSchema>
export type ResetPasswordFormType = z.infer<typeof resetPasswordSchema>
