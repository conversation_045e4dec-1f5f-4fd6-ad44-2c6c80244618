import type { ClassValue } from 'clsx'
import { clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

export function buildQuery(params: Record<string, any>): string {
  const esc = encodeURIComponent
  const query = Object.entries(params)
    .filter(([_, v]) => v !== null && v !== undefined)
    .map(([k, v]) => `${esc(k)}=${esc(v)}`)
    .join('&')
  return query ? `?${query}` : ''
}
