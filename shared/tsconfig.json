{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": ".", "composite": true, "jsx": "react-jsx", "strict": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["./*"], "@components/*": ["components/*"], "@hooks/*": ["hooks/*"], "@lib/*": ["lib/*"]}}, "include": ["src/**/*", "components/**/*", "hooks/**/*", "lib/**/*", "assets/icons/**/*.json", "types/**/*.d.ts"], "exclude": ["node_modules", "dist"]}