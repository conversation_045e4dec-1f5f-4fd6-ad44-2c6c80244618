/// <reference types="vite/client" />

declare module '*.mdx' {
  import React from 'react';
  const MDXComponent: React.ComponentType;
  export default MDXComponent;
}

// Extend ImportMeta interface for Vite environment variables
interface ImportMeta {
  env: {
    MODE: string;
    BASE_URL: string;
    PROD: boolean;
    DEV: boolean;
    [key: string]: any;
  };
}

// Add type declarations for @mass/shared/assets imports
declare module '@mass/shared/assets/*' {
  const content: string;
  export default content;
}

// Fix specific imports from @mass/shared/assets
declare module '@mass/shared/assets/logo-big.png' {
  const content: string;
  export default content;
}

declare module '@mass/shared/assets/pattern_decorative1.svg' {
  const content: string;
  export default content;
}

declare module '@mass/shared/assets/pattern_decorative.svg' {
  const content: string;
  export default content;
}

// Fix the virtual i18next-loader module
declare module 'virtual:i18next-loader' {
  const resources: any;
  export default resources;
}

// Add CSS module declaration
declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}
