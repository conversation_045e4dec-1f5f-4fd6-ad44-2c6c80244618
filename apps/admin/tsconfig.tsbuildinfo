{"root": ["./src/global.d.ts", "./src/routetree.gen.ts", "./src/constants/sidebar-data.ts", "./src/features/auth/data/queries.ts", "./src/features/notification-management/queries.ts", "./src/services/api.ts", "./src/services/enums.ts", "./src/services/api/auth.ts", "./src/services/api/complaints.ts", "./src/services/api/subscriptions.ts", "./src/stores/auth.ts", "./src/test/setup.ts", "./src/utils/i18n.ts", "./src/utils/useglobalsetting.ts", "./src/main.tsx", "./src/features/auth/layout.tsx", "./src/features/auth/pages/forgot-password/index.tsx", "./src/features/auth/pages/forgot-password/components/forgot-password-form.tsx", "./src/features/auth/pages/forgot-password/components/forgot-password-success.tsx", "./src/features/auth/pages/login/index.tsx", "./src/features/auth/pages/login/components/login-form.tsx", "./src/features/auth/pages/otp/index.tsx", "./src/features/auth/pages/otp/components/otp-form.tsx", "./src/features/auth/pages/register/index.tsx", "./src/features/auth/pages/reset-password/index.tsx", "./src/features/auth/pages/reset-password/components/reset-password-form.tsx", "./src/features/auth/pages/reset-password/components/reset-password-success.tsx", "./src/features/complaint-management/index.tsx", "./src/features/complaint-management/components/modals/add.tsx", "./src/features/complaint-management/components/modals/delete.tsx", "./src/features/complaint-management/components/modals/info.tsx", "./src/features/complaint-management/components/modals/publish.tsx", "./src/features/dashboard/index.tsx", "./src/features/dashboard/components/bar-chart.tsx", "./src/features/dashboard/components/device-usage-chart.tsx", "./src/features/dashboard/components/events-list.tsx", "./src/features/dashboard/components/retention-chart.tsx", "./src/features/document-management/index.tsx", "./src/features/document-management/components/faq-card.tsx", "./src/features/document-management/components/skeleton-loader.tsx", "./src/features/document-management/components/modals/delete.tsx", "./src/features/document-management/components/modals/publish.tsx", "./src/features/document-management/components/modals/rename.tsx", "./src/features/document-management/pages/agreement-guidelines.tsx", "./src/features/document-management/pages/faq.tsx", "./src/features/document-management/pages/info.tsx", "./src/features/errors/forbidden.tsx", "./src/features/errors/general-error.tsx", "./src/features/errors/maintenance-error.tsx", "./src/features/errors/not-found-error.tsx", "./src/features/errors/unauthorized-error.tsx", "./src/features/notification-management/index.tsx", "./src/features/notification-management/settings.tsx", "./src/features/notification-management/components/columns.tsx", "./src/features/notification-management/components/skeleton-loader.tsx", "./src/features/notification-management/components/modals/add.tsx", "./src/features/notification-management/components/modals/delete.tsx", "./src/features/notification-management/components/modals/info.tsx", "./src/features/notification-management/components/modals/notification.tsx", "./src/features/notification-management/components/modals/publish.tsx", "./src/features/settings/index.tsx", "./src/features/settings/components/faq-card.tsx", "./src/features/settings/components/skeleton-loader.tsx", "./src/features/settings/components/modals/delete.tsx", "./src/features/settings/components/modals/info.tsx", "./src/features/settings/components/modals/publish.tsx", "./src/features/settings/pages/account-settings.tsx", "./src/features/settings/pages/page-settings.tsx", "./src/features/settings/pages/parameter-settings.tsx", "./src/features/user-management/index.tsx", "./src/features/user-management/components/columns.tsx", "./src/features/user-management/components/skeleton-loader.tsx", "./src/features/user-management/components/modals/delete.tsx", "./src/features/user-management/components/modals/info.tsx", "./src/routes/__root.tsx", "./src/routes/(auth)/500.tsx", "./src/routes/(auth)/forgot-password.lazy.tsx", "./src/routes/(auth)/login.tsx", "./src/routes/(auth)/otp.tsx", "./src/routes/(auth)/reset-password.lazy.tsx", "./src/routes/(errors)/401.lazy.tsx", "./src/routes/(errors)/403.lazy.tsx", "./src/routes/(errors)/404.lazy.tsx", "./src/routes/(errors)/500.lazy.tsx", "./src/routes/(errors)/503.lazy.tsx", "./src/routes/_authenticated/index.tsx", "./src/routes/_authenticated/logout.tsx", "./src/routes/_authenticated/route.tsx", "./src/routes/_authenticated/complaint-management/index.lazy.tsx", "./src/routes/_authenticated/dashboard/index.lazy.tsx", "./src/routes/_authenticated/document-management/agreement-guidelines.lazy.tsx", "./src/routes/_authenticated/document-management/edas-info.lazy.tsx", "./src/routes/_authenticated/document-management/faq.lazy.tsx", "./src/routes/_authenticated/document-management/index.lazy.tsx", "./src/routes/_authenticated/notification-management/history.lazy.tsx", "./src/routes/_authenticated/notification-management/settings.lazy.tsx", "./src/routes/_authenticated/settings/account-settings.lazy.tsx", "./src/routes/_authenticated/settings/index.lazy.tsx", "./src/routes/_authenticated/settings/page-settings.lazy.tsx", "./src/routes/_authenticated/settings/parameter-settings.lazy.tsx", "./src/routes/_authenticated/user-management/index.lazy.tsx", "./types/declarations.d.ts", "./types/global.d.ts", "./types/module.d.ts"], "version": "5.7.3"}