{
  "references": [
    { "path": "../../shared/config/tsconfig.app.json" },
    { "path": "../../shared/config/tsconfig.node.json" }
  ],
  "files": [],
  "compilerOptions": {
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "downlevelIteration": true,
    "types": ["vite/client", "react", "react-dom", "node"],
    "strictNullChecks": true,
    // disable all file emission (no .js or .d.ts)
    "noEmit": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "types/**/*.d.ts"
  ]
}