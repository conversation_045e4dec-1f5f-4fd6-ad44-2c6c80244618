import { useLogoutMutation } from "@/features/auth/data/queries";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect } from "react";

export const Route = createFileRoute("/_authenticated/logout")({
  component: RouteComponent,
});

function RouteComponent() {
  const logout = useLogoutMutation();

  const navigate = useNavigate();

  useEffect(() => {
    logout.mutate();
  }, []);

  useEffect(() => {
    if (logout.isSuccess || logout.isError) {
      navigate({ to: "/login" });
    }
  }, [logout.status]);

  // todo: better loading state
  return null;
}
