import { sidebarData } from "@/constants/sidebar-data";
import api from "@/services/api";
import SkipToMain from "@mass/shared/components/atoms/skip-to-main";
import { AppSidebar } from "@mass/shared/components/organisms/layout/app-sidebar";
import { SidebarProvider } from "@mass/shared/components/ui/sidebar";
import { SearchProvider } from "@mass/shared/context/search-context";
import { cn } from "@mass/shared/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute, Outlet } from "@tanstack/react-router";
import Cookies from "js-cookie";

export const Route = createFileRoute("/_authenticated")({
  component: RouteComponent,
});

function RouteComponent() {
  const {} = useQuery({
    queryKey: ["canView"],
    queryFn: async () => {
      await api("/auth/can/admin.view");
    },
  });

  const defaultOpen = Cookies.get("sidebar:state") !== "false";
  return (
    <SearchProvider>
      <SidebarProvider defaultOpen={defaultOpen}>
        <SkipToMain />
        <AppSidebar sidebarData={sidebarData} api={api}/>
        <div
          id="content"
          className={cn(
            "ml-auto w-full max-w-full",
            "peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]",
            "peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]",
            "transition-[width] duration-200 ease-linear",
            "flex h-svh flex-col"
          )}
        >
          <Outlet />
        </div>
      </SidebarProvider>
    </SearchProvider>
  );
}
