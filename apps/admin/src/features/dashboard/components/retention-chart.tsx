"use client";

import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

export function RetentionChart(props: {
  data: {
    days: number;
    ratio: number;
  }[];
}) {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <LineChart
        data={props.data}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#888888" opacity={0.2} />
        <XAxis
          dataKey="days"
          stroke="#888888"
          tickFormatter={(d) => `${d}. Gün`}
        />
        <YAxis
          stroke="#888888"
          tickFormatter={(value) => `${Math.round(value * 100)}%`}
        />
        <Tooltip
          labelFormatter={(label: number) => `${label}. Gün <PERSON>`}
          formatter={(value: number) => [
            `${Math.round(value * 100)}%`,
            "<PERSON><PERSON>ıcı Tutma Oranı",
          ]}
        />
        <Line
          type="monotone"
          dataKey="ratio"
          stroke="#3b82f6"
          strokeWidth={2}
          dot={{ r: 4 }}
          activeDot={{ r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
