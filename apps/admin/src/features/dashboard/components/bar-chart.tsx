"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";

export function Bar<PERSON>hart(props: { data: { date: Date; count: number }[] }) {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <RechartsBarChart data={props.data}>
        <XAxis
          dataKey="date"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          interval={0}
          tickFormatter={(d) =>
            d.toLocaleDateString("tr-TR", {
              month: "short",
              year: "2-digit",
            })
          }
        />
        <YAxis
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${value}`}
        />
        <Tooltip
          labelFormatter={(label: Date) =>
            label.toLocaleDateString("tr-TR", {
              month: "long",
              year: "numeric",
            })
          }
          formatter={(value: number) => [
            `${value.toLocaleString()} k<PERSON><PERSON><PERSON><PERSON><PERSON>`,
            "<PERSON>kt<PERSON>",
          ]}
        />
        <Bar dataKey="count" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
      </RechartsBarChart>
    </ResponsiveContainer>
  );
}
