"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
  Tooltip,
} from "recharts";

const COLORS = ["#0BA5EC", "#EAAA08", "#66C61C", "#D444F1", "#F63D68"];

export function DeviceUsageChart(props: {
  data: {name: string, ratio: number}[]
}) {
  return (
    <div className="h-[350px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={props.data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={120}
            fill="#8884d8"
            dataKey="ratio"
            label={({ name, ratio }) =>
              `${name}: ${Math.round(ratio * 100)}%`
            }
          >
            {props.data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [`${Math.round(value * 100)}%`, "Kullanım Oranı"]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
