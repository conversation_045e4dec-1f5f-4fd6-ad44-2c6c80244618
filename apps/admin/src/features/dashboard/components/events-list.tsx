import { cn } from "@mass/shared/lib/utils";
import { calculateChangePercentage } from "../utils/calculate-change-percentage";

export function EventsList(props: {
  data: { type: string; subtype: string; count: number; prev: number }[];
}) {
  return (
    <div className="space-y-8">
      {props.data.map((event, index) => (
        <div key={index} className="flex items-center">
          <div className="flex h-9 w-9 items-center justify-center rounded-full bg-blue-100">
            <span className="text-sm font-medium text-blue-600">
              {index + 1}
            </span>
          </div>
          <div className="ml-4 space-y-1">
            <span className="text-sm font-medium leading-none">
              {event.subtype}
              <span className="text-xs font-thin"> {event.type}</span>
            </span>
            <p className="text-sm text-muted-foreground">
              Toplam: {event.count}
            </p>
          </div>
          <div
            className={cn(
              "ml-auto font-medium",
              "text-" +
                calculateChangePercentage(event.count, event.prev).color +
                "-500"
            )}
          >
            {calculateChangePercentage(event.count, event.prev).text}
          </div>
        </div>
      ))}
    </div>
  );
}
