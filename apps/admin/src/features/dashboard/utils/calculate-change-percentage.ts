export const calculateChangePercentage = (
  current: number | undefined,
  previous: number | undefined
) => {
  current = current ?? 0;
  previous = previous ?? 0;

  if (previous === 0) {
    return current > 0
      ? {
          text: "+∞%",
          color: "green",
        }
      : {
          text: "0%",
          color: "yellow",
        };
  }

  const changePercentage = Math.round(((current - previous) / previous) * 1000) / 10;
  return {
    text: `${changePercentage > 0 ? "+" : ""}${changePercentage}%`,
    color: changePercentage > 0 ? "green" : changePercentage < 0 ? "red" : "yellow",
  };
};
