import { sidebarData } from "@/constants/sidebar-data";
import { Head<PERSON> } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@mass/shared/components/ui/card";
import { Activity, BarChart3, <PERSON><PERSON>ointer, Users } from "lucide-react";
import { EventsList } from "./components/events-list";
import { BarChart } from "./components/bar-chart";
import { DeviceUsageChart } from "./components/device-usage-chart";
import { RetentionChart } from "./components/retention-chart";
import { useQuery } from "@tanstack/react-query";
import { analyticsService } from "@/services/api/dashboard";
import { cn } from "@mass/shared/lib/utils";
import React from "react";
import { calculateChangePercentage } from "./utils/calculate-change-percentage";

const AnalyticsCard = (props: {
  curr: number | undefined;
  currText?: string | undefined;
  prevText?: string | undefined;
  prev?: number | undefined;
  title: string;
  icon: any; // todo
}) => {
  const changePercentage = calculateChangePercentage(props.curr, props.prev);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{props.title}</CardTitle>
        {React.createElement(props.icon, {
          className: "h-4 w-4 text-muted-foreground",
        })}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {props.currText ?? (props?.curr ?? 0).toLocaleString()}
        </div>
        {props.prev === undefined ? null : (
          <p className="text-xs text-muted-foreground">
            <span
              className={cn(
                "font-bold",
                "text-" + changePercentage.color + "-500"
              )}
            >
              {changePercentage.text}
            </span>{" "}
            {props.prevText ?? "geçen aydan"}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default function Dashboard() {
  const { data } = useQuery({
    queryKey: ["analytics"],
    queryFn: analyticsService.data,
    staleTime: 60 * 1000,
    refetchInterval: 60 * 1000,
  });

  const secondsToHumanReadable = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 3600));
    const hours = Math.floor((seconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.round(seconds % 60);

    let text = "";
    if (days > 0) text += `${days} gün `;
    if (hours > 0) text += `${hours} sa `;
    if (minutes > 0) text += `${minutes} dk `;
    if (remainingSeconds > 0 || text === "") text += `${remainingSeconds} sn`;
    return text.trim();
  };

  console.log("data", data)

  return (
    <>
      <Header
        sidebarData={sidebarData}
        title="Genel Bakış"
        description="Genel bakış sayfası, sistemin genel durumu hakkında bilgi verir."
      />
      <Main>
        <div className="flex min-h-screen flex-col bg-background w-full">
          <main className="flex-1 space-y-4">
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <AnalyticsCard
                  curr={data?.total?.curr}
                  prev={data?.total?.prev}
                  title="Toplam Kullanıcı"
                  icon={Users}
                />
                <AnalyticsCard
                  curr={data?.active?.curr}
                  prev={data?.active?.prev}
                  title="Aktif Kullanıcı"
                  icon={Activity}
                />
                <AnalyticsCard
                  curr={data?.duration?.curr}
                  prev={data?.duration?.prev}
                  title="Ortalama Oturum Süresi"
                  icon={BarChart3}
                  currText={secondsToHumanReadable(data?.duration?.curr ?? 0)}
                />
                <AnalyticsCard
                  curr={data?.live?.curr}
                  prev={data?.live?.prev}
                  title="Şu Anda Aktif"
                  icon={MousePointer}
                  prevText="geçen saatten"
                />
              </div>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                <Card className="col-span-4">
                  <CardHeader>
                    <CardTitle>Aylık Kullanıcı Aktivitesi</CardTitle>
                  </CardHeader>
                  <CardContent className="pl-2">
                    <BarChart data={data?.perMonth ?? []} />
                  </CardContent>
                </Card>
                <Card className="col-span-3">
                  <CardHeader>
                    <CardTitle>Popüler Analitik Olayları</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <EventsList data={data?.events ?? []} />
                  </CardContent>
                </Card>
              </div>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                <Card className="col-span-3">
                  <CardHeader>
                    <CardTitle>Cihaz Kullanım Dağılımı</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <DeviceUsageChart data={data?.perDevice ?? []} />
                  </CardContent>
                </Card>
                <Card className="col-span-4">
                  <CardHeader>
                    <CardTitle>Kullanıcı Tutma Oranı</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RetentionChart data={data?.survival ?? []} />
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </Main>
    </>
  );
}
