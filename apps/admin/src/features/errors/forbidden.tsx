import { Button } from '@mass/shared/components/ui/button'
import { useNavigate, useRouter } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'

export default function ForbiddenError() {
    const navigate = useNavigate()
    const { history } = useRouter()
    const { t } = useTranslation('errors')

    return (
        <div className="h-svh">
            <div className="m-auto flex h-full w-full flex-col items-center justify-center gap-2">
                <h1 className="text-[7rem] font-bold leading-tight">403</h1>
                <span className="font-medium">{t('forbidden.title')}</span>
                <p className="text-center text-muted-foreground">
                    {t('forbidden.description')}
                </p>
                <div className="mt-6 flex gap-4">
                    <Button variant="outline" onClick={() => history.go(-1)}>
                        {t('go_back')}
                    </Button>
                    <Button onClick={() => navigate({ to: '/' })}>
                        {t('back_to_home')}
                    </Button>
                </div>
            </div>
        </div>
    )
}
