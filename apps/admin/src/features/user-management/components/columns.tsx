import { ColumnDef } from "@tanstack/react-table";
import Iconify from "@mass/shared/components/atoms/Iconify";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { Checkbox } from "@mass/shared/components/ui/checkbox";
import Delete from "./modals/delete";
import { Button } from "@mass/shared/components/ui/button";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  tckn: string;
  createdAt: string;
}

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends unknown> {
    t: (key: string) => string;
    open: (component) => void;
  }
}

const SortableHeader = ({ column, children }) => (
  <Button
    variant="ghost"
    className="p-0 font-medium hover:bg-transparent text-left flex items-center gap-1"
    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  >
    {children}
    {column.getIsSorted() === "asc" ? (
      <Iconify name="untitled:arrow-up" className="size-3.5 text-primary" />
    ) : column.getIsSorted() === "desc" ? (
      <Iconify name="untitled:arrow-down" className="size-3.5 text-primary" />
    ) : (
      <Iconify name="untitled:chevron-selector-vertical" className="size-3.5 text-gray-500" />
    )}
  </Button>
);

export const columns: ColumnDef<User>[] = [
  {
    id: "select",
    header: ({ table, column }) => (
      <div className="pl-4 flex items-center gap-4">
        <Checkbox
          checked={
            table.getIsSomeRowsSelected()
              ? "indeterminate"
              : table.getIsAllRowsSelected()
          }
          onCheckedChange={(value) =>
            table.toggleAllRowsSelected(!!value)
          }
          aria-label="Tümünü seç"
          className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
        />
        <SortableHeader column={column}>TCKN</SortableHeader>
      </div>
    ),
    cell: ({ row }) => (
      <div className="pl-4 flex gap-4 items-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={`Seç ${row.id}`}
          onClick={(e) => e.stopPropagation()}
          className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
        />
        <div>
          <div className="font-medium">
            {row.original.firstName} {row.original.lastName}
          </div>
          <div className="text-sm text-gray-500">{row.original.tckn}</div>
        </div>
      </div>
    ),
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: "createdAt",
    id: "createdAt",
    header: ({ column }) => (
      <div className="text-right flex justify-end">
        <SortableHeader column={column}>Kayıt Tarihi</SortableHeader>
      </div>
    ),
    cell: ({ row }) => {
      const value = row.getValue("createdAt") as string;
      return <div className="text-right">{format(new Date(value), "dd MMM, yyyy", { locale: tr })}</div>;
    },
    enableSorting: true,
  },
  {
    id: "actions",
    header: "",
    cell: ({ row, table }) => {
      const meta = table.options.meta || {};
      const open = (meta as any).open;

      const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (!open) return;
        
        open(Delete, {
          name: "delete-user-modal",
          ids: [row.original.id],
          onConfirm: () => {
            return new Promise<void>((resolve) => {
              console.log("Deleting user:", row.original.id);

              resolve();
            });
          }
        });
      };

      return (
        <div className="flex justify-end">
          <button
            className="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded-md cursor-pointer"
            onClick={handleDelete}
            title="Sil"
          >
            <Iconify name="untitled:trash-01" className="size-4 text-gray-500" />
          </button>
        </div>
      );
    },
  },
];
