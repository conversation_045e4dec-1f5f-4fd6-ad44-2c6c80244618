import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { sidebarData } from "@/constants/sidebar-data";
import { Input } from "@mass/shared/components/ui/input";
import { Button } from "@mass/shared/components/ui/button";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Delete from "./components/modals/delete";
import api from "@/services/api";
import Info from "./components/modals/info";
import { useState } from "react";

export default function UserManagement() {
  const { open } = useModal();
  const [tckn, setTckn] = useState("");

  return (
    <>
      <Header
        sidebarData={sidebarData}
        title="Kullanıcı Yönetimi"
        description="Kullanıcı bilgilerini buradan silebilirsiniz"
      />
      <Main>
        <div className="p-4 border rounded-md flex flex-col gap-6">
          <div className="text-center flex flex-col gap-4">
            <div className="text-3xl">Kullanıcı Yönetimi</div>
            <div className="">
              Bu panelden TCKN ile sorgulama yaparak kayıtlı kullanıcıların
              verilerini silebilirsiniz.
            </div>
          </div>
          <div className=" flex items-center justify-center gap-2">
            <Input
              placeholder="TCKN giriniz..."
              value={tckn}
              onChange={(e) => setTckn(e.target.value)}
            />
            <Button
              variant={"destructive"}
              onClick={() => {
                open(Delete, {
                  onConfirm: async () => {
                    const userResponse = await fetch(
                      "/api/auth/account/delete/" + encodeURIComponent(tckn),
                      {
                        method: "DELETE",
                      }
                    );

                    if (userResponse.ok) {
                      setTckn("");
                    } else {
                      open(Info, {
                        title: "Kullanıcı Silme Hatası",
                        description:
                          "Belirtilen TCKN ile kullanıcı bulunamadı.",
                      });
                    }
                  },
                });
              }}
            >
              Sil
            </Button>
          </div>
        </div>
      </Main>
    </>
  );
}
