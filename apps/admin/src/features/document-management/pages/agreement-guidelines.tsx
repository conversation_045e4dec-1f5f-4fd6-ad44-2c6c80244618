import { Button } from "@mass/shared/components/ui/button";
import FileUploader from "@mass/shared/components/ui/file-uploader";
import Iconify from "@mass/shared/components/atoms/Iconify";
import { useState } from "react";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { sidebarData } from "@/constants/sidebar-data";
import { useGlobalSetting } from "@/utils/useGlobalSetting";
import api from "@/services/api";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Publish from "../components/modals/publish";

export default function DocumentDetail() {
  const [kvkkUrl, setKvkkUrl] = useState("" as string | undefined);
  const [agreementUrl, setAgreementUrl] = useState("" as string | undefined);
  const [aboutUrl, setAboutUrl] = useState("" as string | undefined);
  const [manualUrl, setManualUrl] = useState("" as string | undefined);

  const [editingKvkk, setEditingKvkk] = useState(false);
  const [editingAgreement, setEditingAgreement] = useState(false);
  const [editingAbout, setEditingAbout] = useState(false);
  const [editingManual, setEditingManual] = useState(false);

  const [kvkkFile, setKvkkFile] = useState<File | null>(null);
  const [agreementFile, setAgreementFile] = useState<File | null>(null);
  const [aboutFile, setAboutFile] = useState<File | null>(null);
  const [manualFile, setManualFile] = useState<File | null>(null);

  const [kvkkFileUploading, setKvkkFileUploading] = useState(false);
  const [agreementFileUploading, setAgreementFileUploading] = useState(false);
  const [aboutFileUploading, setAboutFileUploading] = useState(false);
  const [manualFileUploading, setManualFileUploading] = useState(false);

  const { update: updateAgreement, isLoading: agreementLoading } =
    useGlobalSetting<string>("documents.pdf.agreement.url", (url) =>
      setAgreementUrl(url)
    );
  const { update: updateAbout, isLoading: aboutLoading } =
    useGlobalSetting<string>("documents.pdf.about.url", (url) =>
      setAboutUrl(url)
    );
  const { update: updateKvkk, isLoading: kvkkLoading } =
    useGlobalSetting<string>("documents.pdf.kvkk.url", (url) =>
      setKvkkUrl(url)
    );
  const { update: updateManual, isLoading: manualLoading } =
    useGlobalSetting<string>("documents.pdf.manual.url", (url) =>
      setManualUrl(url)
    );

  const { open } = useModal();

  const guidelineItems = [
    {
      title: "Kullanım Kılavuzu",
      description:
        "Kullanım Kılavuzu belgesinin PDF olarak yüklenip güncellendiği alan",
      url: manualUrl,
      setUrl: setManualUrl,
      editing: editingManual,
      setEditing: setEditingManual,
      file: manualFile,
      setFile: setManualFile,
      uploading: manualFileUploading,
      setUploading: setManualFileUploading,

      update: updateManual,
      loading: manualLoading,
    },
    {
      title: "Kullanıcı Sözleşmesi",
      description:
        "Kullanıcı Sözleşmesi belgesinin PDF olarak yüklenip güncellendiği alan",
      url: agreementUrl,
      setUrl: setAgreementUrl,
      editing: editingAgreement,
      setEditing: setEditingAgreement,
      file: agreementFile,
      setFile: setAgreementFile,
      uploading: agreementFileUploading,
      setUploading: setAgreementFileUploading,

      update: updateAgreement,
      loading: agreementLoading,
    },
    {
      title: "KVKK Sözleşmesi",
      description:
        "KVKK bilgilendirme belgesinin PDF olarak yüklenip güncellendiği alan",
      url: kvkkUrl,
      setUrl: setKvkkUrl,
      editing: editingKvkk,
      setEditing: setEditingKvkk,
      file: kvkkFile,
      setFile: setKvkkFile,
      uploading: kvkkFileUploading,
      setUploading: setKvkkFileUploading,

      update: updateKvkk,
      loading: kvkkLoading,
    },
    {
      title: "Hakkında",
      description:
        "Uygulama hakkında belgesinin PDF olarak yüklenip güncellendiği alan",
      url: aboutUrl,
      setUrl: setAboutUrl,
      editing: editingAbout,
      setEditing: setEditingAbout,
      file: aboutFile,
      setFile: setAboutFile,
      uploading: aboutFileUploading,
      setUploading: setAboutFileUploading,

      update: updateAbout,
      loading: aboutLoading,
    },
  ];
  return (
    <>
      <Header
        title="Belge Yönetimi"
        sidebarData={sidebarData}
        description="Kullanım Kılavuzu, Kullanıcı Sözleşmesi, KVKK ve Uygulama Hakkında belgelerinin PDF formatında yüklenip güncellendiği alan."
      />

      <Main>
        <div className="flex flex-col w-full h-full">
          <>
            {guidelineItems.map((item, index) => (
              <div key={index}>
                <div className="w-full flex justify-between py-4 border-b border-gray-200">
                  {item.loading ? (
                    "Yükleniyor..."
                  ) : (
                    <>
                      <div className="flex flex-col">
                        <span className="text-xl font-semibold text-gray-950">
                          {item.title}
                        </span>
                        <span className="text-sm text-gray-500">
                          {item.description}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        {item.editing ? null : (
                          <Button
                            variant="outline"
                            onClick={() => {
                              item.setEditing(true);
                              item.setUploading(true);
                            }}
                          >
                            <Iconify
                              name="untitled:edit-05"
                              className="size-4 mr-2"
                            />
                            Düzenle
                          </Button>
                        )}
                        {item.editing ? (
                          <>
                            <Button
                              variant="outline"
                              onClick={() => {
                                item.setFile(null);
                                item.setEditing(false);
                                item.setUploading(false);
                              }}
                            >
                              <Iconify
                                name="untitled:x"
                                className="size-4 mr-2"
                              />
                              İptal
                            </Button>
                            <Button
                              onClick={() => {
                                open(Publish, {
                                  title: "Yayınla",
                                  description:
                                    "Bu belgeyi yayınlamak istediğinize emin misiniz? Bu işlem geri alınamaz.",
                                  onConfirm: async () => {
                                    item.setFile(null);
                                    item.setEditing(false);
                                    item.setUploading(false);

                                    if (item.url) item.update(item.url);
                                  },
                                });
                              }}
                              disabled={item.uploading}
                            >
                              <Iconify
                                name="untitled:check"
                                className="size-4 mr-2"
                              />
                              Yayınla
                            </Button>
                          </>
                        ) : null}
                      </div>
                    </>
                  )}
                </div>

                <div className="py-4 flex flex-col gap-4">
                  {item.editing ? (
                    <>
                      <FileUploader
                        required={true}
                        onChange={async (files) => {
                          item.setUploading(true);

                          let file: File | null = null;
                          if (Array.isArray(files)) {
                            if (files.length > 0) {
                              file = files[0];
                            } else {
                              file = null;
                            }
                          } else {
                            if (files) {
                              file = files;
                            } else {
                              file = null;
                            }
                          }

                          item.setFile(file);

                          if (!file) return;

                          const response = (await api("/document", {
                            method: "POST",
                            body: JSON.stringify({
                              name: file.name,
                              size: file.size,
                              mimeType: file.type,
                            }),
                          })) as {
                            url: string;
                            id: string;
                          };

                          // directly PUT the file to the url
                          const uploadResponse = await fetch(response.url, {
                            method: "PUT",
                            headers: {
                              "Content-Type": file.type,
                            },
                            body: file,
                          });
                          if (!uploadResponse.ok) {
                            console.error("Upload failed");
                            return;
                          }

                          await api(
                            "/document/" +
                              encodeURIComponent(response.id) +
                              "/done",
                            {
                              method: "PATCH",
                            }
                          );

                          const documentResponse = await api(
                            "/document/" +
                              encodeURIComponent(response.id) +
                              "?permanent=true"
                          );

                          item.setUrl(documentResponse.url);
                          item.setUploading(false);
                        }}
                        accept="application/pdf"
                      />

                      {item.file ? (
                        <div className="flex items-center gap-2 p-3 rounded-2xl bg-gray-50 mt-2">
                          <Iconify name="mass:pdf" className="size-8" />
                          <div className="flex flex-col">
                            <span className="font-medium text-sm">
                              {item.file.name}
                            </span>
                            <span className="text-sm text-gray-500">
                              {Math.round(item.file.size / 1024)} KB
                            </span>
                          </div>
                        </div>
                      ) : null}
                    </>
                  ) : item.url ? (
                    <div className="flex gap-2 p-3 rounded-2xl border shadow-sm bg-white">
                      <Iconify name="mass:pdf" className="size-12" />
                      <div className="flex justify-center gap-1 flex-col flex-1">
                        <span className="font-medium">{item.title}.pdf</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          window.open(item.url);
                        }}
                      >
                        <Iconify name="untitled:share-04" className="size-4" />
                      </Button>
                    </div>
                  ) : null}
                </div>
              </div>
            ))}
          </>
        </div>
      </Main>
    </>
  );
}
