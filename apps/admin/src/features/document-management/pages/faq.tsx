import { useState } from "react";
import { Button } from "@mass/shared/components/ui/button";
import { Separator } from "@mass/shared/components/ui/separator";
import Empty from "@mass/shared/components/atoms/empty";
import FaqCard, { FaqItem } from "../components/faq-card";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { sidebarData } from "@/constants/sidebar-data";
import { useGlobalSetting } from "@/utils/useGlobalSetting";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Publish from "../components/modals/publish";

export default function FAQ() {
  const [faqItems, setFaqItems] = useState<FaqItem[]>([]);
  const [showNewItemCard, setShowNewItemCard] = useState(false);

  const { isLoading, update } = useGlobalSetting<
    {
      question: { TR: string; EN: string };
      answer: { TR: string; EN: string };
    }[]
  >("documents.faq", (data) =>
    setFaqItems(
      data.map((d, i) => ({ id: i, ...d, isEditing: false })).reverse()
    )
  );

  const { open } = useModal();

  const handleChange = (
    id: number,
    field: "question" | "answer",
    language: "TR" | "EN",
    value: string
  ) => {
    setFaqItems(
      faqItems.map((item) =>
        item.id === id
          ? {
              ...item,
              [field]: {
                ...item[field],
                [language]: value,
              },
            }
          : item
      )
    );
  };

  const handleCancel = (id: number) => {
    const outerItem = faqItems.find((item) => item.id === id);
    if (!outerItem) return;

    if (!outerItem.oldValues)
      setFaqItems((old) => old.filter((i) => i.id !== id));
    else
      setFaqItems(
        faqItems.map((item) =>
          item.id === id
            ? {
                ...item,
                question: item.oldValues!.question,
                answer: item.oldValues!.answer,
                isEditing: false,
              }
            : item
        )
      );
  };

  const handleEdit = (id: number) => {
    setFaqItems((old) =>
      old.map((item) =>
        item.id === id
          ? {
              ...item,
              isEditing: true,
              oldValues: JSON.parse(JSON.stringify(item)),
            }
          : item
      )
    );
  };

  const handleDelete = (id: number) => {
    setFaqItems((old) => old.filter((item) => item.id !== id));
  };

  const handleSave = (id: number) => {
    const outerItem = faqItems.find((item) => item.id === id);
    if (!outerItem) return;

    setFaqItems(
      faqItems.map((item) =>
        item.id === id
          ? {
              ...item,
              isEditing: false,
              oldValues: undefined,
            }
          : item
      )
    );
  };

  const handleMove = (id: number, up: boolean) => {
    const index = faqItems.findIndex((item) => item.id === id);
    if (index === -1) return;

    const newIndex = up ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= faqItems.length) return;

    setFaqItems((oldItems) => {
      const newItems = [...oldItems];
      const [movedItem] = newItems.splice(index, 1);
      newItems.splice(newIndex, 0, movedItem);
      return newItems;
    });
  };

  return (
    <>
      <Header
        title="Belge Yönetimi"
        sidebarData={sidebarData}
        description="Uygulama hakkında sıkça sorulan soruların ekle-çıkar-düzenle alanı"
      />

      <Main>
        {isLoading ? (
          "Yükleniyor..."
        ) : (
          <div className="space-y-8 w-full h-full">
            <div className="relative w-full">
              <Separator className="my-8 w-full" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-background px-2 flex gap-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowNewItemCard(true);
                      setFaqItems((old) => [
                        {
                          id: old.length,
                          question: { TR: "", EN: "" },
                          answer: { TR: "", EN: "" },
                          oldValues: null,
                          isEditing: true,
                        },
                        ...old,
                      ]);
                    }}
                  >
                    Yeni Soru Ekle
                  </Button>
                  <Button
                    onClick={() => {
                      faqItems.forEach((item) => {
                        handleSave(item.id);
                      });

                      open(Publish, {
                        name: "publish-faq-modal",
                        title: "Yayınla",
                        description:
                          "Aşağıdaki sıkça sorulan soruları kullanıcılara yayınlamak istediğinize emin misiniz?",
                        onConfirm: () => {
                          return new Promise<void>(async (resolve) => {
                            await update(faqItems);
                            resolve();
                          });
                        },
                      });
                    }}
                    disabled={
                      faqItems.some((item) => {
                        return (
                          !item.question.TR ||
                          !item.answer.TR ||
                          !item.question.EN ||
                          !item.answer.EN
                        );
                      }) || faqItems.length === 0
                    }
                  >
                    Yayınla
                  </Button>
                </div>
              </div>
            </div>
            {faqItems.map((item, index, self) => (
              <FaqCard
                isFirst={index === 0}
                isLast={index === self.length - 1}
                key={item.id}
                item={item}
                index={index + 1}
                isNew={false}
                onEdit={() => handleEdit(item.id)}
                onDelete={() => handleDelete(item.id)}
                onCancel={() => handleCancel(item.id)}
                onSave={() => handleSave(item.id)}
                onChange={(field, language, value) =>
                  handleChange(item.id, field, language, value)
                }
                onMoveUp={() => handleMove(item.id, true)}
                onMoveDown={() => handleMove(item.id, false)}
              />
            ))}

            {faqItems.length === 0 && !showNewItemCard && (
              <div className="pt-24">
                <Empty
                  title="Henüz Soru Yok"
                  description="Sık sorulan sorular ve cevaplar ekleyerek kullanıcılara yardımcı olabilirsiniz."
                />
              </div>
            )}
          </div>
        )}
      </Main>
    </>
  );
}
