import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { sidebarData } from "@/constants/sidebar-data";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import api from "@/services/api";
import { DataTable } from "@mass/shared/components/organisms/table/index";
import Action from "@mass/shared/components/organisms/table/action";
import { useState } from "react";
import Iconify from "@mass/shared/components/atoms/Iconify";
import { DropdownMenuItem } from "@mass/shared/components/ui/dropdown-menu";
import Rename from "../components/modals/rename";

export default function DocumentList() {
  const { data = [] } = useQuery({
    queryKey: ["regions"],
    queryFn: async () => {
      return (await api("/region")) as { id: string; name: string }[];
    },
  });
  const { open } = useModal();

  const queryClient = useQueryClient();

  const handleEdit = (id: string) => {
    const edas = data.find((item) => item.id === id);
    if (!edas) return;

    open(Rename, {
      title: "EDAŞ Adı Değiştir",
      description: "Yeni EDAŞ adını giriniz.",
      oldName: edas.name,
      onConfirm: async (name: string) => {
        await api("/region/" + encodeURIComponent(id), {
          method: "PATCH",
          body: JSON.stringify({
            name,
          }),
        });

        queryClient.invalidateQueries({ queryKey: ["regions"] });
      },
    });
  };

  return (
    <>
      <Header
        title="EDAŞ Bilgi Yönetimi"
        sidebarData={sidebarData}
        description="Platforma eklenen EDAŞ bilgilerini yönetmek için bu sayfayı kullanabilirsiniz."
      />
      <Main className="flex items-center justify-start">
        <div className="w-full">
          <DataTable
            columns={[
              { accessorKey: "id", header: "ID" },
              { accessorKey: "name", header: "İsim" },
              {
                id: "actions",
                header: "",
                cell: ({ row }) => {
                  const [ellipsisOpen, setEllipsisOpen] = useState(false);

                  return (
                    <Action
                      action={
                        <div
                          className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 cursor-pointer"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Iconify
                            name="untitled:dots-vertical"
                            className="size-4"
                          />
                        </div>
                      }
                      open={ellipsisOpen}
                      onOpenChange={setEllipsisOpen}
                    >
                      <DropdownMenuItem
                        className="flex text-xs font-medium items-center justify-between"
                        onClick={() => handleEdit(row.original.id)}
                      >
                        Düzenle
                        <Iconify name="untitled:edit-05" className="size-2" />
                      </DropdownMenuItem>
                    </Action>
                  );
                },
              },
            ]}
            data={data.sort((a, b) => a.name.localeCompare(b.name))}
          />
        </div>
      </Main>
    </>
  );
}
