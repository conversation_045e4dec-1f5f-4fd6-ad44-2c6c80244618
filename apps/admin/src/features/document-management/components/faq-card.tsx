import { But<PERSON> } from "@mass/shared/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@mass/shared/components/ui/card";
import { Input } from "@mass/shared/components/ui/input";
import { Textarea } from "@mass/shared/components/ui/textarea";
import { Pencil, Save, Trash2, X, ChevronUp, ChevronDown } from "lucide-react";
import Delete from "./modals/delete";
import { useModal } from "@mass/shared/components/organisms/modal/provider";

export interface FaqItem {
  id: number;
  question: { TR: string; EN: string };
  answer: { TR: string; EN: string };
  isEditing: boolean;
  oldValues?: FaqItem | null;
}

interface Props {
  item: FaqItem;
  index: number;
  isNew: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onCancel: () => void;
  onSave: () => void;
  onChange: (
    field: "question" | "answer",
    lang: "EN" | "TR",
    value: string
  ) => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  isFirst?: boolean;
  isLast?: boolean;
}

export default function FaqCard({
  item,
  index,
  isNew,
  onEdit,
  onDelete,
  onCancel,
  onSave,
  onChange,

  isFirst,
  isLast,
  onMoveUp,
  onMoveDown,
}: Props) {
  const title = isNew ? "Yeni Soru" : `Soru - Cevap ${index}`;

  const { open } = useModal();

  const handleDelete = () => {
    open(Delete, {
      name: "delete-faq-modal",
      title: "Soruyu Sil",
      description:
        "Bu soruyu silmek istediğinize emin misiniz? Bu işlem geri alınamaz.",
      onConfirm: () => {
        return new Promise<void>((resolve) => {
          onDelete();
          resolve();
        });
      },
    });
  };

  return (
    <Card>
      <CardHeader className="w-full flex flex-row justify-between items-center">
        <div className="font-medium text-gray-800">{title}</div>
        <div className="flex gap-2 items-center">
          {item.isEditing ? (
            <>
              <Button variant="outline" size="sm" onClick={onCancel}>
                <X className="h-4 w-4 mr-1" /> İptal
              </Button>
              <Button
                size="sm"
                onClick={onSave}
                disabled={
                  !item.question.TR ||
                  !item.answer.TR ||
                  !item.question.EN ||
                  !item.answer.EN
                }
              >
                <Save className="h-4 w-4 mr-1" /> Kaydet
              </Button>
            </>
          ) : (
            <>
              {isFirst ? null : (
                <Button
                  variant="secondary"
                  size="icon"
                  className="h-8"
                  onClick={onMoveUp}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              )}
              {isLast ? null : (
                <Button
                  variant="secondary"
                  size="icon"
                  className="h-8"
                  onClick={onMoveDown}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="secondary"
                size="icon"
                className="h-8"
                onClick={handleDelete}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={onEdit}>
                <Pencil className="h-4 w-4 mr-1" /> Düzenle
              </Button>
            </>
          )}
        </div>
      </CardHeader>
      <CardContent className="gap-4 flex flex-col">
        <div className="flex gap-4">
          <div className="flex flex-col gap-1 flex-1">
            <span className="mb-2 text-gray-700">Soru (TR):</span>
            {item.isEditing ? (
              <Input
                placeholder="Soru başlığını giriniz"
                value={item.question.TR}
                onChange={(e) => onChange("question", "TR", e.target.value)}
                className="text-lg font-semibold"
              />
            ) : (
              <div className="bg-gray-50 w-full rounded-lg p-2">
                {item.question.TR}
              </div>
            )}
          </div>
          <div className="flex flex-col gap-1 flex-1">
            <span className="mb-2 text-gray-700">Soru (EN):</span>
            {item.isEditing ? (
              <Input
                placeholder="Soru başlığını giriniz"
                value={item.question.EN}
                onChange={(e) => onChange("question", "EN", e.target.value)}
                className="text-lg font-semibold"
              />
            ) : (
              <div className="bg-gray-50 w-full rounded-lg p-2">
                {item.question.EN}
              </div>
            )}
          </div>
        </div>

        <div className="flex gap-4">
          <div className="flex flex-col gap-1 flex-1">
            <span className="mb-2 text-gray-700">Cevap (TR):</span>
            {item.isEditing ? (
              <Textarea
                placeholder="Cevabı giriniz"
                value={item.answer.TR}
                onChange={(e) => onChange("answer", "TR", e.target.value)}
                rows={4}
              />
            ) : (
              <div className="bg-gray-50 w-full rounded-lg p-2">
                {item.answer.TR}
              </div>
            )}
          </div>
          <div className="flex flex-col gap-1 flex-1">
            <span className="mb-2 text-gray-700">Cevap (EN):</span>
            {item.isEditing ? (
              <Textarea
                placeholder="Cevabı giriniz"
                value={item.answer.EN}
                onChange={(e) => onChange("answer", "EN", e.target.value)}
                rows={4}
              />
            ) : (
              <div className="bg-gray-50 w-full rounded-lg p-2">
                {item.answer.EN}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
