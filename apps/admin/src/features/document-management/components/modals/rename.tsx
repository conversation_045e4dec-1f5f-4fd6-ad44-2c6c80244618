import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { Input } from "@mass/shared/components/ui/input";
import { Label } from "@mass/shared/components/ui/label";
import { useState } from "react";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm?: (label: string) => void | Promise<void>;
  oldName: string;
}

export default function Rename({
  title,
  description,
  onHide,
  onConfirm,
  oldName,
}: Props & { title?: string; description?: string }) {
  const [name, setName] = useState<string>(oldName ?? "");
  const [error, setError] = useState<string>("");

  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <div className="-mt-12 w-full relative flex flex-col items-start justify-start gap-2 text-left text-gray-900 font-text-sm-regular">
        <div className="self-stretch relative leading-[28px] font-semibold">
          {title}
        </div>
        <div className="self-stretch relative text-sm leading-[20px] text-gray-600">
          {description}
        </div>
      </div>

      <div className="w-full flex flex-col gap-4">
        <div className="text-red-500 text-xs">{error}</div>
        <div className="grid w-full gap-1.5">
          <Label htmlFor="message" className="text-sm font-medium">
            EDAŞ Adı
          </Label>
          <Input
            placeholder="EDAŞ Adı giriniz..."
            className="resize-none"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
        </div>
        <div className="flex justify-end w-full gap-2">
          <Button variant="outline" onClick={onHide} className="w-full">
            İptal
          </Button>
          <Button
            type="button"
            className="w-full"
            onClick={() => {
              const name_ = name.trim();

              if (name_.length < 2) {
                setError("EDAŞ Adı en az 2 karakter olmalıdır.");
                return;
              }

              onConfirm && onConfirm(name_);
              onHide();
            }}
          >
            Kaydet
          </Button>
        </div>
      </div>
    </div>
  );
}
