import { useState } from "react";
import { useGlobalSetting } from "@/utils/useGlobalSetting";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { sidebarData } from "@/constants/sidebar-data";
import { ChevronRight, Edit, Trash } from "lucide-react";
import { cn } from "@mass/shared/lib/utils";
import { Button } from "@mass/shared/components/ui/button";
import { Separator } from "@mass/shared/components/ui/separator";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Add from "./components/modals/add";
import Delete from "./components/modals/delete";
import Publish from "./components/modals/publish";
import Info from "./components/modals/info";

interface ComplaintCategory {
  label: { TR: string; EN: string };
  deleted: boolean;
  subcategories: Record<string, ComplaintSubcategory>;
}

interface ComplaintSubcategory {
  label: { TR: string; EN: string };
  deleted: boolean;
  unpublished?: undefined; // this is a hack. undefined serializes to nothing when sending to the server, however we can use "in" to check if it exists
  // so we can use it to check if the subcategory is unpublished
}

function ComplaintSubcategory(
  props: ComplaintSubcategory & {
    id: string;
    upperCategory: string;
    deleteSubcategory: () => void;
    editSubcategory: (label: { TR: string; EN: string }) => void;
  }
) {
  const { open } = useModal();

  return (
    <div className="flex items-center justify-between w-full px-3 py-2 bg-slate-50 rounded-md border">
      <div className="flex items-center gap-4 text-md text-gray-600">
        <span>{props.label.TR}</span>
      </div>
      <div className="flex flex-1 items-center justify-end gap-4">
        <Button
          variant="secondary"
          size="icon"
          className="h-8"
          onClick={() => {
            open(Add, {
              title: "Șikayet / Talep Alt Kategorisini Düzenle",
              oldValues: {
                id: props.id,
                label: props.label,
              },
              onConfirm: ({ label }) => {
                props.editSubcategory(label);
              },
            });
          }}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="destructive"
          size="icon"
          className="h-8"
          onClick={() => {
            open(Delete, {
              title: "Șikayet / Talep Alt Kategorisini Sil",
              description:
                "Bu alt kategoriyi silmek istediğinize emin misiniz? Bu işlem geri alınamaz.",
              onConfirm: () => {
                props.deleteSubcategory();
              },
            });
          }}
        >
          <Trash className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

function ComplaintCategory(
  props: ComplaintCategory & {
    id: string;
    addSubcategory: (a: {
      id: string;
      label: { TR: string; EN: string };
    }) => void;
    deleteCategory: () => void;
    deleteSubcategory: (id: string) => void;
    editCategory: (label: { TR: string; EN: string }) => void;
    editSubcategory: (id: string, label: { TR: string; EN: string }) => void;
    existingIds: string[];
  }
) {
  const [collapsed, setCollapsed] = useState(true);
  const { open } = useModal();

  return (
    <div className="flex flex-col w-full border px-3 py-3 rounded-md bg-white shadow-sm cursor-pointer select-none">
      <div
        className="flex items-center justify-between w-full"
        onClick={(e) => {
          if ((e.target as any).closest("button")) return;
          setCollapsed((c) => !c);
        }}
      >
        <div className="flex items-center gap-4 text-lg text-gray-900">
          <div className="p-3 bg-blue-50 text-blue-400 rounded-md">
            <ChevronRight
              size={20}
              className={cn(
                "animate-transform transition-transform duration-200",
                {
                  "rotate-90": !collapsed,
                }
              )}
            />
          </div>
          <span>{props.label.TR}</span>
        </div>
        <div className="flex flex-1 items-center justify-end gap-4">
          <Button
            variant="secondary"
            size="icon"
            className="h-8"
            onClick={() => {
              open(Add, {
                title: "Șikayet / Talep Kategorisini Düzenle",
                oldValues: {
                  id: props.id,
                  label: props.label,
                },
                onConfirm: ({ label }) => {
                  props.editCategory(label);
                },
              });
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="destructive"
            size="icon"
            className="h-8"
            onClick={() => {
              open(Delete, {
                title: "Șikayet / Talep Kategorisini Sil",
                description:
                  "Bu kategoriyi silmek istediğinize emin misiniz? Bu işlem geri alınamaz.",
                onConfirm: () => {
                  props.deleteCategory();
                },
              });
            }}
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {collapsed ? null : (
        <>
          <div className="py-6 flex flex-col gap-2 items-center justify-center">
            {Object.entries(props.subcategories).length > 0 ? (
              Object.entries(props.subcategories)
                .filter(([key, subcategory]) => !subcategory.deleted)
                .sort((a, b) => a[0].localeCompare(b[0]))
                .map(([key, subcategory]) => (
                  <ComplaintSubcategory
                    key={key}
                    id={key}
                    upperCategory={props.id}
                    {...subcategory}
                    deleteSubcategory={() => props.deleteSubcategory(key)}
                    editSubcategory={(label) =>
                      props.editSubcategory(key, label)
                    }
                  />
                ))
            ) : (
              <>Alt kategori yok</>
            )}
          </div>
          <div className="flex items-center justify-center gap-4">
            <Button
              variant="outline"
              onClick={() => {
                open(Add, {
                  title: "Yeni Șikayet / Talep Alt Kategorisi Ekle",
                  currentIds: props.existingIds,
                  onConfirm: ({ id, label }) => {
                    props.addSubcategory({
                      id,
                      label: { TR: label.TR, EN: label.EN },
                    });
                  },
                });
              }}
            >
              Alt Kategori Ekle
            </Button>
          </div>
        </>
      )}
    </div>
  );
}

export default function ComplaintManagementSettings() {
  const [complaintCategories, setComplaintCategories] = useState<
    Record<string, ComplaintCategory>
  >({});

  const { isLoading, update } = useGlobalSetting<
    Record<
      string,
      {
        deleted?: string;
        label: { TR: string; EN: string };
        subcategories: Record<
          string,
          {
            deleted?: string;
            label: { TR: string; EN: string };
          }
        >;
      }
    >
  >("complaints.categories", (data) => {
    const mapped = Object.fromEntries(
      Object.entries(data ?? {}).map(([key, value]) => {
        const subcategories = Object.fromEntries(
          Object.entries(value.subcategories ?? {}).map(
            ([subKey, subValue]) => [
              subKey,
              {
                ...subValue,
                deleted: subValue.deleted === "true",
              },
            ]
          )
        );

        return [
          key,
          {
            ...value,
            deleted: value.deleted === "true",
            subcategories,
          },
        ];
      })
    );

    setComplaintCategories(mapped);
  });

  const { open } = useModal();

  return (
    <>
      <Header
        sidebarData={sidebarData}
        title="Șikayet / Talep Ayarları"
        description="MASS platform kullanıcılarına yönelik şikayet / talep tipi ayarları"
        className="flex flex-col"
      ></Header>
      <Main className="justify-start">
        {isLoading ? (
          "Yükleniyor..."
        ) : (
          <div className="w-full flex flex-col gap-2">
            <div className="relative w-full">
              <Separator className="my-8 w-full" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-background px-2 flex gap-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      open(Add, {
                        title: "Yeni Șikayet / Talep Tipi Ekle",
                        currentIds: Object.entries(complaintCategories)
                          .filter(([key, category]) => !category.deleted)
                          .map(([key]) => key),
                        onConfirm: ({ id, label }) => {
                          setComplaintCategories((old) => ({
                            ...old,
                            [id]: {
                              label: { TR: label.TR, EN: label.EN },
                              unpublished: undefined,
                              subcategories: {},
                              // todo: any
                              ...((old?.[id] as any) ?? {}),
                              deleted: false,
                            },
                          }));
                        },
                      });
                    }}
                  >
                    Yeni Tip Ekle
                  </Button>
                  <Button
                    onClick={() => {
                      if (
                        Object.entries(complaintCategories).filter(
                          ([key, category]) => !category.deleted
                        ).length === 0
                      ) {
                        return open(Info, {
                          title: "Yayınlama Hatası",
                          description:
                            "Henüz hiç kategori eklenmediği için yayınlama işlemi yapılamaz.",
                        });
                      }

                      const withoutSubcategories = Object.entries(
                        complaintCategories
                      )
                        .filter(([key, category]) => !category.deleted)
                        .filter(
                          ([key, category]) =>
                            Object.entries(category.subcategories).filter(
                              ([key, subcategory]) => !subcategory.deleted
                            ).length === 0
                        );
                      if (withoutSubcategories.length !== 0) {
                        return open(Info, {
                          title: "Yayınlama Hatası",
                          description:
                            "Herhangi bir kategorinin alt kategorileri boş olamaz.",
                        });
                      }
                      open(Publish, {
                        name: "publish-complaints-modal",
                        title: "Yayınla",
                        description:
                          "Aşağıdaki şikayet / talep kategorilerini yayınlamak istediğinize emin misiniz?",
                        onConfirm: async () => {
                          await new Promise<void>(async (resolve) => {
                            await update(complaintCategories as any); // todo
                            resolve();
                          });
                          window.location.reload();
                        },
                      });
                    }}
                  >
                    Yayınla
                  </Button>
                </div>
              </div>
            </div>
            {Object.entries(complaintCategories)
              .filter(([key, category]) => !category.deleted)
              .sort((a, b) => a[0].localeCompare(b[0]))
              .map(([key, category]) => (
                <ComplaintCategory
                  key={key}
                  {...category}
                  id={key}
                  existingIds={Object.entries(complaintCategories).flatMap(
                    ([key, category]) =>
                      category.deleted
                        ? []
                        : Object.entries(category.subcategories)
                            .filter(
                              ([key, subcategory]) => !subcategory.deleted
                            )
                            .map(([key]) => key)
                  )}
                  addSubcategory={({ id, label }) =>
                    setComplaintCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        subcategories: {
                          ...old[key].subcategories,
                          [id]: {
                            label,
                            epias: true,
                            edas: false,
                            fixed: false,
                            deleted: false,
                            unpublished: undefined,
                          },
                        },
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    })
                  }
                  deleteCategory={() => {
                    setComplaintCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        deleted: true,
                        unpublished: undefined,
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    });
                  }}
                  deleteSubcategory={(id) => {
                    setComplaintCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        subcategories: {
                          ...old[key].subcategories,
                          [id]: {
                            ...old[key].subcategories[id],
                            deleted: true,
                            unpublished: undefined,
                          },
                        },
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    });
                  }}
                  editCategory={(label) => {
                    setComplaintCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        label,
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    });
                  }}
                  editSubcategory={(id, label) => {
                    setComplaintCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        subcategories: {
                          ...old[key].subcategories,
                          [id]: {
                            ...old[key].subcategories[id],
                            label,
                          },
                        },
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    });
                  }}
                />
              ))}
          </div>
        )}
      </Main>
    </>
  );
}
