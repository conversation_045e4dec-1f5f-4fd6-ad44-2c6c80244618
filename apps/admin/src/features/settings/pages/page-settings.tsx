import { sidebarData } from "@/constants/sidebar-data";
import { useGlobalSetting } from "@/utils/useGlobalSetting";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { Separator } from "@mass/shared/components/ui/separator";
import { Switch } from "@mass/shared/components/ui/switch";
import * as React from "react";
import { useState } from "react";
import { toast } from "sonner";
import Publish from "../components/modals/publish";

// do not, EVER, think about making this array dynamic
const PAGE_SETTINGS = [
  {
    id: "subscriptions",
    name: "Abonelikler",
    desc: "Kullanıcının yeni abonelik ekleme, abonelik detaylarını görmesi ve abonelik bildirim tercihini güncellemesi i<PERSON> sayfa<PERSON>",
    sub: [
      { id: "view", name: "Abonelikler Ekranı" },
      { id: "add", name: "<PERSON>bone<PERSON> Ekle" },
      { id: "usage", name: "Abonelik Kullanım Sorgulama" },
      {
        id: "notifs",
        name: "Abonelik Bildirim Tercihi Güncelleme",
      },
      {
        id: "outages",
        name: "Abonelik Kesinti/Tazminat Sorgulama",
      },
      { id: "details", name: "Abonelik Detay Sorgulama" },
    ],
  },
  {
    id: "complaints",
    name: "Şikayetler",
    desc: "Kullanıcının yeni şikayet oluşturma, şikayet detayları listeleme ve şikayet takibi için kullanılan sayfalar",
    sub: [
      { id: "view", name: "Şikayetler Ekranı" },
      { id: "add", name: "Şikayet Ekle" },
      { id: "details", name: "Şikayet Detay Sorgulama" },
    ],
  },
  {
    id: "notifications",
    name: "Bildirimler",
    desc: "Kullanıcının panele gönderilen bildirimleri görebilmesi için kullandığı sayfalar",
    sub: [
      { id: "view", name: "Bildirimler Ekranı" },
      { id: "details", name: "Bildirim Detayları" },
    ],
  },
  {
    id: "settings",
    name: "Ayarlar",
    desc: "Kullanıcının MASS uygulamasında kişisel ayarlarını güncelleyebilmesi için kullandığı sayfalar",
    sub: [
      { id: "view", name: "Ayarlar Ekranı" },
      { id: "user", name: "Kullanıcı Ayarları" },
      { id: "notif", name: "Bildirim Ayarları" },
    ],
  },
];

export default function PageSettings() {
  const pageSettings = PAGE_SETTINGS.map((category) => {
    return {
      ...category,
      sub: category.sub.map((sub) => {
        const key = "disabled." + category.id + "." + sub.id;

        // this is a terrible idea. todo: fix
        const [isDisabled, setIsDisabled] = useState(false);

        const { update } = useGlobalSetting(key, (data) =>
          setIsDisabled(data === "true")
        );

        return {
          ...sub,

          isDisabled,
          setIsDisabled,
          update,
        };
      }),
    };
  });

  const { open } = useModal();

  const handleSave = () => {
    open(Publish, {
      title: "Yayınla",
      description: "Sayfa ayarlarını güncellemek istediğinize emin misiniz?",
      onConfirm: async () => {
        pageSettings.forEach((category) => {
          category.sub.forEach((sub) => {
            sub.update(sub.isDisabled ? "true" : null);
          });
        });
      },
    });
  };

  return (
    <>
      <Header
        title="Sayfa yönetimi ayarı"
        sidebarData={sidebarData}
        description="Son kullanıcının görüntüleyebileceği sayfa görünürlük alanı"
      />

      <Main>
        <div className="py-6 space-y-6 w-full h-full">
          <div className="flex items-center justify-between pb-4 border-b border-gray-200">
            <div>
              <h3 className="font-medium">Sayfa görünürlük alanı</h3>
              <p className="text-sm text-gray-500">
                Son kullanıcının görüntüleyebileceği sayfa görünürlük alanı
              </p>
            </div>
            <Button onClick={handleSave}>Yayınla</Button>
          </div>
          {pageSettings.map((category, index, self) => (
            <React.Fragment key={category.id}>
              <div className="w-full py-4 flex items-start gap-2">
                <div className="font-normal text-sm flex-1">
                  <div className="font-medium">{category.name}</div>
                  <div className="">{category.desc}</div>
                </div>
                <div className="flex gap-2 flex-1 flex-col text-sm">
                  {category.sub.map((sub) => (
                    <label key={sub.id} className="flex items-center gap-2">
                      <Switch
                        checked={!sub.isDisabled}
                        onCheckedChange={(checked) =>
                          sub.setIsDisabled(!checked)
                        }
                      />{" "}
                      {sub.name}
                    </label>
                  ))}
                </div>
              </div>
              {index < self.length - 1 ? (
                <Separator key={`sep-${category.id}`} />
              ) : null}
            </React.Fragment>
          ))}
        </div>
      </Main>
    </>
  );
}
