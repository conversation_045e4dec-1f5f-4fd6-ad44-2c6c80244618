import { sidebarData } from "@/constants/sidebar-data";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { Header } from "@mass/shared/components/organisms/layout/header";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mass/shared/components/ui/select";

import { MultiSelect } from "@mass/shared/components/ui/multi-select";
import { Label } from "@mass/shared/components/ui/label";
import { Button } from "@mass/shared/components/ui/button";
import { useState } from "react";
import { useGlobalSetting } from "@/utils/useGlobalSetting";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Publish from "../components/modals/publish";
import { Input } from "@mass/shared/components/ui/input";

export default function AccountSettings() {
  const [yearMax, setYearMax] = useState("2");
  const [monthMax, setMonthMax] = useState("24");
  const [dayMax, setDayMax] = useState((2 * 365).toString());

  const [monthPrev, setMonthPrev] = useState("6");
  const [dayPrev, setDayPrev] = useState("10");

  const [allowedFileSize, setAllowedFileSize] = useState("********");
  const [mimetypesSelectState, setMimetypesSelectState] = useState({
    selectedValues: ["application/pdf"],
    isPopoverOpen: false,
    isAnimating: false,
    width: 0,
    search: "",
  });
  const [allowedFileCount, setAllowedFileCount] = useState("5");

  const { open } = useModal();

  // todo: any
  const { update: updateQueryDateSettings } = useGlobalSetting<any>(
    "subscriptions.usage.date-limits",
    (data) => {
      if (data?.yearMax) setYearMax(data.yearMax);
      if (data?.monthMax) setMonthMax(data.monthMax);
      if (data?.dayMax) setDayMax(data.dayMax);
      if (data?.monthPrev) setMonthPrev(data.monthPrev);
      if (data?.dayPrev) setDayPrev(data.dayPrev);
    }
  );

  const { update: updateExcessConsumptionSettings } = useGlobalSetting<any>(
    "subscriptions.usage.excess-consumption",
    (data) => {
      if (data?.defaultExcessConsumption)
        setDefaultExcessConsumption(data.defaultExcessConsumption);
    }
  );

  const { update: updateFileSizeSettings } = useGlobalSetting<any>(
    "documents.upload.limits",
    (data) => {
      console.log("data", data);
      if (data.size) setAllowedFileSize(data.size);
      if (data.mimetypes)
        setMimetypesSelectState((old) => ({
          ...old,
          selectedValues: data.mimetypes,
        }));
      if (data.count) setAllowedFileCount(data.count);
    }
  );

  const [defaultExcessConsumption, setDefaultExcessConsumption] =
    useState("0.5");

  const yearlyOptions = Array.from({ length: 5 }, (_, i) => ({
    value: (i + 1).toString(),
    label: `${i + 1} Yıl`,
  }));
  const monthlyOptions = Array.from({ length: 48 }, (_, i) => ({
    value: (i + 1).toString(),
    label: `${i + 1} Ay`,
  }));
  const yearlyDayOptions = Array.from({ length: 5 }, (_, i) => ({
    value: (365 * (i + 1)).toString(),
    label: `${i + 1} Yıl`,
  }));
  const dailyOptions = Array.from({ length: 30 }, (_, i) => ({
    value: (i + 1).toString(),
    label: `${i + 1} Gün`,
  }));

  const excessConsumptionOptions = [
    { value: "0.25", label: "%25" },
    { value: "0.5", label: "%50" },
    { value: "0.75", label: "%75" },
    { value: "1", label: "%100" },
  ];

  const mimeTypes = [
    // Document & Text formats
    "text/plain", // .txt
    "text/csv", // .csv
    "text/html", // .htm, .html
    "application/pdf", // .pdf
    "application/rtf", // .rtf
    "application/msword", // .doc
    "application/vnd.ms-word.document.macroEnabled.12", // .docm
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
    "application/vnd.ms-excel", // .xls
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
    "application/vnd.ms-powerpoint", // .ppt
    "application/vnd.openxmlformats-officedocument.presentationml.presentation", // .pptx
    "application/vnd.oasis.opendocument.text", // .odt
    "application/vnd.oasis.opendocument.spreadsheet", // .ods
    "application/vnd.oasis.opendocument.presentation", // .odp

    // Microsoft‐specific extras
    "application/vnd.ms-word.template.macroEnabled.12", // .dotm
    "application/vnd.openxmlformats-officedocument.wordprocessingml.template", // .dotx
    "application/vnd.ms-excel.sheet.macroEnabled.12", // .xlsm
    "application/vnd.openxmlformats-officedocument.spreadsheetml.template", // .xltx
    "application/vnd.ms-excel.addin.macroEnabled.12", // .xlam
    "application/vnd.ms-excel.sheet.binary.macroEnabled.12", // .xlsb
    "application/vnd.ms-powerpoint.presentation.macroEnabled.12", // .pptm
    "application/vnd.openxmlformats-officedocument.presentationml.slideshow", // .ppsx
    "application/vnd.ms-powerpoint.slideshow.macroEnabled.12", // .ppsm
    "application/vnd.openxmlformats-officedocument.presentationml.template", // .potx
    "application/vnd.ms-powerpoint.template.macroEnabled.12", // .potm
    "application/vnd.openxmlformats-officedocument.presentationml.slide", // .sldx
    "application/vnd.ms-powerpoint.slide.macroEnabled.12", // .sldm
    "application/onenote", // .one, .onepkg, etc.
    "application/x-mspublisher", // .pub
    "application/vnd.ms-access", // .mdb
    "application/vnd.ms-project", // .mpp
    "application/vnd.ms-visio", // .vsd
    "application/vnd.ms-outlook", // .msg

    // Image formats
    "image/jpeg", // .jpg, .jpeg, .jfif, .pjpeg, .pjp
    "image/png", // .png
    "image/gif", // .gif
    "image/webp", // .webp
    "image/svg+xml", // .svg
    "image/apng", // .apng
    "image/avif", // .avif
    "image/bmp", // .bmp
    "image/tiff", // .tif, .tiff
    "image/x-icon", // .ico, .cur
    "image/vnd.microsoft.icon", // .ico
    "image/heif", // .heif, .heic
    "image/avif", // .avif

    // Video formats
    "video/mp4", // .mp4
    "video/webm", // .webm
    "video/ogg", // .ogv
    "application/ogg", // .ogx
    "video/mpeg", // .mpeg, .mpg
    "video/quicktime", // .mov
    "video/x-msvideo", // .avi
    "video/x-ms-wmv", // .wmv
    "video/x-ms-asf", // .asf, .asx
    "video/x-flv", // .flv
    "video/3gpp", // .3gp
    "video/3gpp2", // .3g2
    "video/x-matroska", // .mkv
    "video/mp2t", // .ts

    // Audio formats
    "audio/mpeg", // .mp3
    "audio/wav", // .wav
    "audio/wave", // .wav
    "audio/x-wav", // .wav
    "audio/webm", // .weba
    "audio/ogg", // .oga
    "audio/aac", // .aac
    "audio/flac", // .flac
    "audio/x-flac", // .flac
    "audio/3gpp", // .3gp
    "audio/3gpp2", // .3g2
    "audio/mp4", // .m4a
    "audio/wma", // .wma
    "audio/vnd.wave", // .wav
  ];

  return (
    <>
      <Header
        title="Sorgu tarihi parametresi ayarı"
        sidebarData={sidebarData}
        description="Son kullanıcının seçebileceği tarihlerin parametrik kontrol alanı"
      />

      <Main>
        <div className="py-6 space-y-6 w-full h-full">
          <div className="flex items-center justify-between pb-4 border-b border-gray-200">
            <div>
              <h3 className="font-medium">Sorgu tarihi parametresi ayarı</h3>
              <p className="text-sm text-gray-500">
                Son kullanıcının seçebileceği tarihlerin parametrik kontrol
                alanı.
              </p>
            </div>

            <Button
              onClick={() => {
                open(Publish, {
                  title: "Sorgu tarihi parametresi ayarını yayınla",
                  description:
                    "Sorgu tarihi parametresi ayarını yayınlamak istediğinize emin misiniz?",
                  onConfirm: () => {
                    updateQueryDateSettings({
                      yearMax,
                      monthMax,
                      dayMax,
                      monthPrev,
                      dayPrev,
                    });
                  },
                });
              }}
            >
              Yayınla
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <Label className="block mb-1 font-medium">
                Yıllık Maksimum Geriye Dönük Veri
              </Label>
              <Select value={yearMax} onValueChange={setYearMax}>
                <SelectTrigger>
                  <SelectValue placeholder="Seçim yapın" />
                </SelectTrigger>
                <SelectContent>
                  {yearlyOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-4">
              <div className="">
                <Label className="block mb-1 font-medium">
                  Aylık Maksimum Geriye Dönük Veri
                </Label>
                <Select value={monthMax} onValueChange={setMonthMax}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seçim yapın" />
                  </SelectTrigger>
                  <SelectContent>
                    {monthlyOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="">
                <Label className="block mb-1 font-medium">
                  Aylık Geçmiş Veri Miktarı
                </Label>
                <Select value={monthPrev} onValueChange={setMonthPrev}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seçim yapın" />
                  </SelectTrigger>
                  <SelectContent>
                    {monthlyOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex flex-col gap-4">
              <div className="">
                <Label className="block mb-1 font-medium">
                  Günlük Maksimum Geriye Dönük Veri
                </Label>
                <Select value={dayMax} onValueChange={setDayMax}>
                  <SelectTrigger>
                    <SelectValue placeholder="Gün Seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {yearlyDayOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="">
                <Label className="block mb-1 font-medium">
                  Günlük Geçmiş Veri Miktarı
                </Label>
                <Select value={dayPrev} onValueChange={setDayPrev}>
                  <SelectTrigger>
                    <SelectValue placeholder="Gün Seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {dailyOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between pb-4 border-b border-gray-200">
            <div>
              <h3 className="font-medium">Beklenmedik aşırı tüketim</h3>
              <p className="text-sm text-gray-500">
                Beklenmedik aşırı tüketim için varsayılan eşik değeri
              </p>
            </div>

            <Button
              onClick={() => {
                open(Publish, {
                  title: "Beklenmedik aşırı tüketim ayarını yayınla",
                  description:
                    "Beklenmedik aşırı tüketim ayarını yayınlamak istediğinize emin misiniz?",
                  onConfirm: () => {
                    updateExcessConsumptionSettings({
                      defaultExcessConsumption,
                    });
                  },
                });
              }}
            >
              Yayınla
            </Button>
          </div>

          <div className="max-w-xs flex-grow">
            <Select
              value={defaultExcessConsumption}
              onValueChange={setDefaultExcessConsumption}
            >
              <SelectTrigger>
                <SelectValue placeholder="Varsayılan değeri seçin" />
              </SelectTrigger>
              <SelectContent>
                {excessConsumptionOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              Beklenmedik aşırı tüketim için varsayılan eşik değeri
            </p>
          </div>

          <div className="flex items-center justify-between pb-4 border-b border-gray-200">
            <div>
              <h3 className="font-medium">Şikayet / Talep Dosya Sınırları</h3>
              <p className="text-sm text-gray-500">
                Şikayet / Talep dosya boyut ve tip sınırları
              </p>
            </div>

            <Button
              onClick={() => {
                if (
                  !allowedFileSize ||
                  !mimetypesSelectState ||
                  (mimetypesSelectState?.selectedValues?.length ?? 0) <= 0 ||
                  !allowedFileCount
                )
                  return;
                if (isNaN(Number(allowedFileSize))) return;
                if (parseInt(allowedFileSize) <= 0) return;

                if (isNaN(Number(allowedFileCount))) return;
                if (parseInt(allowedFileCount) <= 0) return;

                open(Publish, {
                  title: "Şikayet / Talep dosya sınırları ayarını yayınla",
                  description:
                    "Şikayet / Talep dosya sınırları ayarını yayınlamak istediğinize emin misiniz?",
                  onConfirm: () => {
                    updateFileSizeSettings({
                      size: allowedFileSize,
                      mimetypes: mimetypesSelectState.selectedValues,
                      count: allowedFileCount,
                    });
                  },
                });
              }}
            >
              Yayınla
            </Button>
          </div>
          <div className="flex flex-col gap-2">
            <Label className="block mb-1 font-medium">
              Dosya Türleri (MIME)
            </Label>
            <MultiSelect
              onValueChange={() => {}}
              options={mimeTypes.map((d) => ({ label: d, value: d }))}
              placeholder="Dosya türlerini seçin"
              state={mimetypesSelectState}
              setState={setMimetypesSelectState}
            />
            <Label className="block mb-1 font-medium">
              Maksimum Dosya Boyutu
            </Label>
            <Input
              value={allowedFileSize}
              onChange={(e) => {
                setAllowedFileSize(e.target.value.replace(/\D/g, ""));
              }}
            />
            <Label>Maksimum Dosya Sayısı</Label>
            <Input
              value={allowedFileCount}
              onChange={(e) => {
                setAllowedFileCount(e.target.value.replace(/\D/g, ""));
              }}
            />
          </div>
        </div>
      </Main>
    </>
  );
}
