import { Header } from "@mass/shared/components/organisms/layout/header";
import { sidebarData } from "@/constants/sidebar-data";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { Button } from "@mass/shared/components/ui/button";
import { Pencil, Save } from "lucide-react";
import { Input } from "@mass/shared/components/ui/input";
import { useState } from "react";
import { toast } from "sonner";
import { useMe } from "@/features/auth/data/queries";
import api from "@/services/api";

export default function AccountSettings() {
  const [editSection, setEditSection] = useState<
    "personal" | "password" | null
  >(null);

  const isEditingPassword = editSection === "password";

  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const { data: me } = useMe();

  console.log("me", me);

  return (
    <>
      <Header
        title="Kullanıcı Ayarları"
        sidebarData={sidebarData}
        description="E-posta ve şifre bilgilerinizi güncelleyebileceğiniz ayarlar alanıdır."
      />

      <Main>
        <div className="w-full h-full space-y-24 py-6">
          <div className="flex flex-col gap-8">
            <div className="flex items-center justify-between pb-4 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold">Kişisel Bilgiler</h2>
                <p className="text-sm text-muted-foreground">
                  E-posta bilgilerinizi görüntüleyebilirsiniz.
                </p>
              </div>
            </div>
            <div className="space-y-4 max-w-xl">
              <div>
                <label
                  htmlFor="email"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  E-posta adresi
                </label>
                <Input
                  id="email"
                  type="email"
                  // todo: any
                  value={(me as any)?.email ?? ""}
                  disabled={true}
                />
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-8">
            <div className="flex items-center justify-between pb-4 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold">Şifre ve Güvenlik</h2>
                <p className="text-sm text-muted-foreground">
                  Şifre bilgilerinizi görüntüleyip güncelleyebileceğiniz
                  alandır.
                </p>
              </div>
              {isEditingPassword ? (
                <Button
                  size="sm"
                  onClick={() => {
                    if (!currentPassword) {
                      toast.error("Mevcut şifre alanı boş olamaz");
                      return;
                    }

                    if (!newPassword) {
                      toast.error("Yeni şifre alanı boş olamaz");
                      return;
                    }

                    if (confirmPassword !== newPassword) {
                      toast.error("Yeni şifre ve onay şifresi eşleşmiyor");
                      return;
                    }

                    if (newPassword.length < 8) {
                      toast.error("Yeni şifre en az 8 karakter olmalıdır");
                      return;
                    }

                    if (!/[a-zA-Z]/.test(newPassword)) {
                      toast.error("Yeni şifre en az bir harf içermelidir");
                      return;
                    }

                    if (!/\d/.test(newPassword)) {
                      toast.error("Yeni şifre en az bir rakam içermelidir");
                      return;
                    }

                    if (!/[!@#$%^&*(),.?":{}|<>]/.test(newPassword)) {
                      toast.error(
                        "Yeni şifre en az bir özel karakter içermelidir"
                      );
                      return;
                    }

                    toast.promise(
                      api("/auth/password", {
                        method: "PATCH",
                        body: JSON.stringify({
                          oldPassword: currentPassword,
                          newPassword,
                        }),
                      }),
                      {
                        loading: "Şifre güncelleniyor...",
                        success: () => {
                          setEditSection(null);
                          return "Şifre başarıyla güncellendi";
                        },
                        error: (err) => {
                          setEditSection(null);
                          return "Şifre güncellenirken bir hata oluştu. Eski şifreyi kontrol edin";
                        },
                      }
                    );
                  }}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Kaydet
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setEditSection("password")}
                  disabled={editSection !== null && !isEditingPassword}
                >
                  <Pencil className="w-4 h-4" />
                </Button>
              )}
            </div>
            <div className="space-y-4 max-w-xl">
              <div>
                <label
                  htmlFor="current-password"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Mevcut Şifre
                </label>
                <Input
                  id="current-password"
                  type="password"
                  disabled={!isEditingPassword}
                  placeholder="********"
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                />
              </div>
              <div>
                <label
                  htmlFor="new-password"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Yeni Şifre
                </label>
                <Input
                  id="new-password"
                  type="password"
                  disabled={!isEditingPassword}
                  placeholder="********"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                />
                <ul className="text-sm text-muted-foreground list-disc pl-5 mt-2 space-y-1">
                  <li>En az 8 karakter uzunluğunda olmalıdır.</li>
                  <li>
                    En az bir Harf, rakam ve özel karakter içermelidir. (!@#$%^&
                    vb.)
                  </li>
                </ul>
              </div>
              <div>
                <label
                  htmlFor="confirm-password"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Yeni Şifre Tekrar
                </label>
                <Input
                  id="confirm-password"
                  type="password"
                  disabled={!isEditingPassword}
                  placeholder="********"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>
      </Main>
    </>
  );
}
