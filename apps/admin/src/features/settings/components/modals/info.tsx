import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import { But<PERSON> } from "@mass/shared/components/ui/button";
import { ReactNode } from "react";

interface Props extends ModalProps {
  onHide: () => void;
  title?: string;
  description?: ReactNode;
}

export default function Info({ onHide, title, description }: Props) {
  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-lg text-gray-900 font-text-sm-regular">
        <div className="self-stretch relative leading-[28px] font-semibold">
          {title}
        </div>
        <div className="self-stretch relative text-sm leading-[20px] text-gray-600">
          {description}
        </div>
      </div>

      <div className="w-full flex gap-2 items-center">
        <Button variant="outline" onClick={onHide} className="w-full">
          Tamam
        </Button>
      </div>
    </div>
  );
}
