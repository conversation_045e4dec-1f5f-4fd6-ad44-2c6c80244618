import { But<PERSON> } from "@mass/shared/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@mass/shared/components/ui/card";
import { Input } from "@mass/shared/components/ui/input";
import { Textarea } from "@mass/shared/components/ui/textarea";
import { Pencil, Save, Trash2, X } from "lucide-react";
import Delete from "./modals/delete";
import { useModal } from "@mass/shared/components/organisms/modal/provider";

export interface FaqItem {
    id: number;
    question: string;
    answer: string;
    isEditing: boolean;
}

interface Props {
    item: FaqItem;
    index: number;
    isNew: boolean;
    onEdit: () => void;
    onDelete: () => void;
    onCancel: () => void;
    onSave: () => void;
    onChange: (field: "question" | "answer", value: string) => void;
}

export default function FaqCard({
    item,
    index,
    isNew,
    onEdit,
    onDelete,
    onCancel,
    onSave,
    onChange,
}: Props) {
    const title = isNew ? "Yeni Soru" : `Soru - Cevap ${index}`;

    const { open } = useModal();

    const handleDelete = () => {
        open(Delete, {
            name: "delete-faq-modal",
            title: "Soruyu Sil",
            description: "Bu soruyu silmek istediğinize emin misiniz? Bu işlem geri alınamaz.",
            onConfirm: () => {
                return new Promise<void>((resolve) => {
                    onDelete();
                    resolve();
                });
            }
        });
    };

    return (
        <Card>
            <CardHeader className="w-full flex flex-row justify-between items-center">
                <div className="font-medium text-gray-800">{title}</div>
                <div className="flex gap-2 items-center">
                    {item.isEditing ? (
                        <>
                            <Button variant="outline" size="sm" onClick={onCancel}>
                                <X className="h-4 w-4 mr-1" /> İptal
                            </Button>
                            <Button size="sm" onClick={onSave}>
                                <Save className="h-4 w-4 mr-1" /> Kaydet
                            </Button>
                        </>
                    ) : (
                        <>
                            <Button variant="secondary" size="icon" className="h-8" onClick={handleDelete}>
                                <Trash2 className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm" onClick={onEdit}>
                                <Pencil className="h-4 w-4 mr-1" /> Düzenle
                            </Button>
                        </>
                    )}
                </div>
            </CardHeader>
            <CardContent className="gap-4 flex flex-col">
                <div className="flex flex-col gap-1">
                    <span className="mb-2 text-gray-700">
                        Soru:
                    </span>
                    {item.isEditing ? (
                        <Input
                            placeholder="Soru başlığını giriniz"
                            value={item.question}
                            onChange={(e) => onChange("question", e.target.value)}
                            className="text-lg font-semibold"
                        />
                    ) : (
                        <div className="bg-gray-50 w-full rounded-lg p-2">
                            {item.question}
                        </div>
                    )}
                </div>

                <div className="flex flex-col gap-1">
                    <span className="mb-2 text-gray-700">
                        Cevap:
                    </span>

                    {item.isEditing ? (
                        <Textarea
                            placeholder="Cevabı giriniz"
                            value={item.answer}
                            onChange={(e) => onChange("answer", e.target.value)}
                            rows={4}
                        />
                    ) : (
                        <div className="bg-gray-50 w-full rounded-lg p-2">
                            {item.answer}
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
