import { Skeleton } from '@mass/shared/components/ui/skeleton'

export function UserManagementTableSkeleton() {
    return (
        <div className="w-full space-y-3 h-full">
            <div className="flex items-center justify-between py-4">
                <div className="flex gap-3">
                    <Skeleton className="h-10 w-64" /> {/* Search input skeleton */}
                    <Skeleton className="h-10 w-40" /> {/* Date filter skeleton */}
                </div>
                <Skeleton className="h-10 w-36" /> {/* Action button skeleton */}
            </div>

            <div className="rounded-xl border border-gray-100">
                {/* Table header */}
                <div className="border-b border-gray-100 px-4 py-2 grid grid-cols-3 items-center gap-4">
                    {/* Name/TCKN column header */}
                    <div className="flex gap-2 items-center">
                        <Skeleton className="h-5 w-5" /> {/* Checkbox */}
                        <Skeleton className="h-5 w-20" /> {/* Column name */}
                    </div>
                    
                    {/* Date column header - right aligned */}
                    <div className="flex justify-end">
                        <Skeleton className="h-5 w-24" /> {/* Date column name */}
                    </div>
                    
                    {/* Actions column - empty header */}
                    <div className="flex justify-end">
                        <div className="w-8" />
                    </div>
                </div>

                {/* Table rows */}
                {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="px-4 py-3 w-full grid grid-cols-3 items-center gap-4 border-b border-gray-100 last:border-b-0">
                        {/* Name/TCKN cell */}
                        <div className="flex gap-3 items-center">
                            <Skeleton className="h-4 w-4" /> {/* Checkbox */}
                            <div className="flex flex-col gap-1">
                                <Skeleton className="h-5 w-32" /> {/* Name */}
                                <Skeleton className="h-4 w-28" /> {/* TCKN */}
                            </div>
                        </div>
                        
                        {/* Date cell - right aligned */}
                        <div className="flex justify-end">
                            <Skeleton className="h-5 w-24" /> {/* Date */}
                        </div>
                        
                        {/* Actions cell - right aligned */}
                        <div className="flex justify-end">
                            <Skeleton className="h-8 w-8 rounded-md" /> {/* Action button */}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )
}
