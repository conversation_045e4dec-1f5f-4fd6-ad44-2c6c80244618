import { sidebarData } from "@/constants/sidebar-data";
import { analyticsService } from "@/services/api/dashboard";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { DataTable } from "@mass/shared/components/organisms/table/index";
import { cn } from "@mass/shared/lib/utils";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { Loader } from "lucide-react";
import { useEffect, useRef } from "react";
import { useIsVisible } from "react-is-visible";

export default function AdminLogs() {
  const finalElementRef = useRef<HTMLDivElement>(null);

  const {
    data = { pages: [] },
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    initialPageParam: 1,
    queryKey: ["logs"],
    queryFn: async ({ pageParam = 1 }) => {
      const data = await analyticsService.logs(pageParam, 50);

      return data;
    },
    getNextPageParam: (lastPage, _, lastPageParam) => {
      return lastPage.length === 0 ? undefined : lastPageParam + 1;
    },
    staleTime: 60 * 1000,
    refetchInterval: 60 * 1000,
  });

  const allData = data.pages.flat();

  const isVisible = useIsVisible(finalElementRef);

  useEffect(() => {
    if (isVisible && finalElementRef.current) {
      fetchNextPage();
    }
  }, [isVisible]);

  return (
    <>
      <Header
        sidebarData={sidebarData}
        title="Admin Logları"
        description="Admin logları, sistemdeki tüm önemli olayları ve değişiklikleri takip etmenizi sağlar."
      />
      <Main className="justify-start">
        <DataTable
          className="w-full"
          columns={[
            {
              header: "Tarih / Saat",
              accessorFn: (row) => row.date.toLocaleString("tr-TR"),
            },
            {
              header: "IP",
              accessorKey: "ip",
            },
            {
              header: "Kullanıcı",
              accessorKey: "user_email",
            },
            {
              header: "İşlem",
              accessorKey: "key",
            },
            {
              header: "Değer",
              accessorKey: "value",
            },
          ]}
          data={allData}
          tableOptions={{
            manualPagination: true,
          }}
          isPaginated={false}
        />
        <div className="w-full py-4" ref={finalElementRef}>
          <Loader
            className={cn("w-8 h-8 animate-spin text-gray-200 mx-auto", {
              "!text-white": !isFetchingNextPage,
            })}
          />
        </div>
      </Main>
    </>
  );
}
