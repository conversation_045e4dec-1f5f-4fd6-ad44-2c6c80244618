import { useState } from "react";
import { useGlobalSetting } from "@/utils/useGlobalSetting";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { sidebarData } from "@/constants/sidebar-data";
import { Bell, ChevronRight, Edit, Key, Lock, Send, Trash } from "lucide-react";
import { cn } from "@mass/shared/lib/utils";
import { Button } from "@mass/shared/components/ui/button";
import { Separator } from "@mass/shared/components/ui/separator";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Add from "./components/modals/add";
import Delete from "./components/modals/delete";
import Publish from "./components/modals/publish";
import Notification from "./components/modals/notification";
import Info from "./components/modals/info";
import api from "@/services/api";
import { useQueryClient } from "@tanstack/react-query";

interface NotificationCategory {
  label: { TR: string; EN: string };
  epias: boolean;
  edas: boolean;
  fixed: boolean;
  deleted: boolean;
  subcategories: Record<string, NotificationSubcategory>;
}

interface NotificationSubcategory {
  label: { TR: string; EN: string };
  epias: boolean;
  edas: boolean;
  fixed: boolean;
  deleted: boolean;
  unpublished?: undefined; // this is a hack. undefined serializes to nothing when sending to the server, however we can use "in" to check if it exists
  // so we can use it to check if the subcategory is unpublished
}

function NotificationSubcategory(
  props: NotificationSubcategory & {
    id: string;
    upperCategory: string;
    deleteSubcategory: () => void;
    editSubcategory: (label: { TR: string; EN: string }) => void;
  }
) {
  const { open } = useModal();
  const queryClient = useQueryClient();

  return (
    <div className="flex items-center justify-between w-full px-3 py-2 bg-slate-50 rounded-md border">
      <div className="flex items-center gap-4 text-md text-gray-600">
        <span>{props.label.TR}</span>
        {props.fixed ? (
          <span className="text-xs text-gray-500 font-semibold ml-2 flex gap-1 items-center justify-center bg-gray-200 rounded-md px-2 py-1">
            <Lock className="h-3 w-3 mr-1 " />
            Sabit
          </span>
        ) : null}
      </div>
      <div className="flex flex-1 items-center justify-end gap-4">
        {props.epias ? (
          <Button
            className="h-8"
            size="icon"
            onClick={() => {
              if ("unpublished" in props) {
                open(Info, {
                  title: "Bildirim Gönderilemedi",
                  description:
                    "Bu bildirim alt kategorisi yayınlanmadığı için bildirim gönderilemez. Bildirim kategorilerini yayınladıktan sonra bildirim gönderilebilir.",
                });
                return;
              }

              open(Notification, {
                title: '"' + props.label.TR + '" Tipinden Bildirim Gönder',
                onConfirm: async (content) => {
                  // todo: this should be a useMutation
                  queryClient.invalidateQueries({
                    queryKey: ["pastNotifications"],
                  });
                  await api("/notification/send/bulk", {
                    method: "POST",
                    body: JSON.stringify({
                      type: props.upperCategory,
                      subtype: props.id,
                      content,
                    }),
                  });
                },
              });
            }}
          >
            <Send className="h-4 w-4" />
          </Button>
        ) : null}
        <Button
          variant="secondary"
          size="icon"
          className="h-8"
          onClick={() => {
            open(Add, {
              title: "Bildirim Alt Kategorisini Düzenle",
              oldValues: {
                id: props.id,
                label: props.label,
              },
              onConfirm: ({ label }) => {
                props.editSubcategory(label);
              },
            });
          }}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="destructive"
          size="icon"
          className="h-8"
          disabled={props.fixed}
          onClick={() => {
            open(Delete, {
              title: "Bildirim Alt Kategorisini Sil",
              description:
                "Bu bildirim alt kategorisini silmek istediğinize emin misiniz? Bu işlem geri alınamaz.",
              onConfirm: () => {
                props.deleteSubcategory();
              },
            });
          }}
        >
          <Trash className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

function NotificationCategory(
  props: NotificationCategory & {
    id: string;
    addSubcategory: (a: {
      id: string;
      label: { TR: string; EN: string };
    }) => void;
    deleteCategory: () => void;
    deleteSubcategory: (id: string) => void;
    editCategory: (label: { TR: string; EN: string }) => void;
    editSubcategory: (id: string, label: { TR: string; EN: string }) => void;
    existingIds: string[];
  }
) {
  const [collapsed, setCollapsed] = useState(true);
  const { open } = useModal();

  return (
    <div className="flex flex-col w-full border px-3 py-3 rounded-md bg-white shadow-sm cursor-pointer select-none">
      <div
        className="flex items-center justify-between w-full"
        onClick={(e) => {
          if ((e.target as any).closest("button")) return;
          setCollapsed((c) => !c);
        }}
      >
        <div className="flex items-center gap-4 text-lg text-gray-900">
          <div className="p-3 bg-blue-50 text-blue-400 rounded-md">
            <ChevronRight
              size={20}
              className={cn(
                "animate-transform transition-transform duration-200",
                {
                  "rotate-90": !collapsed,
                }
              )}
            />
          </div>
          <span>{props.label.TR}</span>
          {props.fixed ? (
            <span className="text-xs text-gray-500 font-semibold ml-2 flex gap-1 items-center justify-center bg-gray-200 rounded-md px-2 py-1">
              <Lock className="h-3 w-3 mr-1 " />
              Sabit
            </span>
          ) : null}
        </div>
        <div className="flex flex-1 items-center justify-end gap-4">
          <Button
            variant="secondary"
            size="icon"
            className="h-8"
            onClick={() => {
              open(Add, {
                title: "Bildirim Kategorisini Düzenle",
                oldValues: {
                  id: props.id,
                  label: props.label,
                },
                onConfirm: ({ label }) => {
                  props.editCategory(label);
                },
              });
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="destructive"
            size="icon"
            className="h-8"
            disabled={props.fixed}
            onClick={() => {
              open(Delete, {
                title: "Bildirim Kategorisini Sil",
                description:
                  "Bu bildirim kategorisini silmek istediğinize emin misiniz? Bu işlem geri alınamaz.",
                onConfirm: () => {
                  props.deleteCategory();
                },
              });
            }}
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {collapsed ? null : (
        <>
          <div className="py-6 flex flex-col gap-2 items-center justify-center">
            {Object.entries(props.subcategories).length > 0 ? (
              Object.entries(props.subcategories)
                .filter(([key, subcategory]) => !subcategory.deleted)
                .sort((a, b) => a[0].localeCompare(b[0]))
                .map(([key, subcategory]) => (
                  <NotificationSubcategory
                    key={key}
                    id={key}
                    upperCategory={props.id}
                    {...subcategory}
                    deleteSubcategory={() => props.deleteSubcategory(key)}
                    editSubcategory={(label) =>
                      props.editSubcategory(key, label)
                    }
                  />
                ))
            ) : (
              <>Alt kategori yok</>
            )}
          </div>
          <div className="flex items-center justify-center gap-4">
            <Button
              variant="outline"
              onClick={() => {
                open(Add, {
                  title: "Yeni Bildirim Alt Kategorisi Ekle",
                  currentIds: props.existingIds,
                  onConfirm: ({ id, label }) => {
                    props.addSubcategory({
                      id,
                      label: { TR: label.TR, EN: label.EN },
                    });
                  },
                });
              }}
            >
              Alt Kategori Ekle
            </Button>
          </div>
        </>
      )}
    </div>
  );
}

export default function NotificationManagementSettings() {
  const [notificationCategories, setNotificationCategories] = useState<
    Record<string, NotificationCategory>
  >({});

  const { isLoading, update, data } = useGlobalSetting<
    Record<
      string,
      {
        epias: string;
        edas: string;
        fixed: string;
        deleted?: string;
        label: { TR: string; EN: string };
        subcategories: Record<
          string,
          {
            epias: string;
            edas: string;
            fixed: string;
            deleted?: string;
            label: { TR: string; EN: string };
          }
        >;
      }
    >
  >("notifications.categories", (data) => {
    const mapped = Object.fromEntries(
      Object.entries(data ?? {}).map(([key, value]) => {
        const subcategories = Object.fromEntries(
          Object.entries(value.subcategories ?? {}).map(
            ([subKey, subValue]) => [
              subKey,
              {
                ...subValue,
                epias: subValue.epias !== "false",
                edas: subValue.edas !== "false",
                fixed: subValue.fixed !== "false",
                deleted: subValue.deleted === "true",
              },
            ]
          )
        );

        return [
          key,
          {
            ...value,
            epias: value.epias !== "false",
            edas: value.edas !== "false",
            fixed: value.fixed !== "false",
            deleted: value.deleted === "true",
            subcategories,
          },
        ];
      })
    );

    setNotificationCategories(mapped);
  });

  const { open } = useModal();

  return (
    <>
      <Header
        sidebarData={sidebarData}
        title="Bildirim Ayarları"
        description="MASS platform kullanıcılarına yönelik bildirim tipi ayarları"
        className="flex flex-col"
      ></Header>
      <Main className="justify-start">
        {isLoading ? (
          "Yükleniyor..."
        ) : (
          <div className="w-full flex flex-col gap-2">
            <div className="relative w-full">
              <Separator className="my-8 w-full" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-background px-2 flex gap-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      open(Add, {
                        title: "Yeni Bildirim Tipi Ekle",
                        currentIds: Object.entries(notificationCategories)
                          .filter(([key, category]) => !category.deleted)
                          .map(([key]) => key),
                        onConfirm: ({ id, label }) => {
                          setNotificationCategories((old) => ({
                            ...old,
                            [id]: {
                              label: { TR: label.TR, EN: label.EN },
                              unpublished: undefined,
                              subcategories: {},
                              ...((old?.[id] as any) ?? {}),
                              epias: true,
                              edas: false,
                              fixed: false,
                              deleted: false,
                            },
                          }));
                        },
                      });
                    }}
                  >
                    Yeni Tip Ekle
                  </Button>
                  <Button
                    onClick={() => {
                      if (
                        Object.entries(notificationCategories).filter(
                          ([key, category]) => !category.deleted
                        ).length === 0
                      ) {
                        return open(Info, {
                          title: "Yayınlama Hatası",
                          description:
                            "Henüz hiç kategori eklenmediği için yayınlama işlemi yapılamaz.",
                        });
                      }

                      open(Publish, {
                        name: "publish-notifs-modal",
                        title: "Yayınla",
                        description:
                          "Aşağıdaki bildirim kategorilerini yayınlamak istediğinize emin misiniz?",
                        onConfirm: async () => {
                          await new Promise<void>(async (resolve) => {
                            await update(notificationCategories as any); // todo
                            resolve();
                          });

                          window.location.reload();
                        },
                      });
                    }}
                  >
                    Yayınla
                  </Button>
                </div>
              </div>
            </div>
            {Object.entries(notificationCategories)
              .filter(([key, category]) => !category.deleted)
              .sort((a, b) => a[0].localeCompare(b[0]))
              .map(([key, category]) => (
                <NotificationCategory
                  key={key}
                  {...category}
                  id={key}
                  existingIds={Object.entries(notificationCategories).flatMap(
                    ([key, category]) =>
                      category.deleted
                        ? []
                        : Object.entries(category.subcategories)
                            .filter(
                              ([key, subcategory]) => !subcategory.deleted
                            )
                            .map(([key]) => key)
                  )}
                  addSubcategory={({ id, label }) =>
                    setNotificationCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        subcategories: {
                          ...old[key].subcategories,
                          [id]: {
                            label,
                            epias: true,
                            edas: false,
                            fixed: false,
                            deleted: false,
                            unpublished: undefined,
                          },
                        },
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    })
                  }
                  deleteCategory={() => {
                    setNotificationCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        deleted: true,
                        unpublished: undefined,
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    });
                  }}
                  deleteSubcategory={(id) => {
                    setNotificationCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        subcategories: {
                          ...old[key].subcategories,
                          [id]: {
                            ...old[key].subcategories[id],
                            deleted: true,
                            unpublished: undefined,
                          },
                        },
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    });
                  }}
                  editCategory={(label) => {
                    setNotificationCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        label,
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    });
                  }}
                  editSubcategory={(id, label) => {
                    setNotificationCategories((old) => {
                      const newCategory = {
                        ...old[key],
                        subcategories: {
                          ...old[key].subcategories,
                          [id]: {
                            ...old[key].subcategories[id],
                            label,
                          },
                        },
                      };
                      return {
                        ...old,
                        [key]: newCategory,
                      };
                    });
                  }}
                />
              ))}
          </div>
        )}
      </Main>
    </>
  );
}
