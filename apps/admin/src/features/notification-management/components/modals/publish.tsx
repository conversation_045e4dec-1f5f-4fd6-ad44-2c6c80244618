import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import Pattern from "@mass/shared/assets/pattern_decorative.svg";
import FeaturedIcon from "@mass/shared/components/atoms/featured-icon";
import { Button } from "@mass/shared/components/ui/button";
import { useTranslation } from "react-i18next";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm?: () => void | Promise<void>;
  ids?: string[];
  title?: string;
  description?: string;
}

export default function Publish({
  onHide,
  onConfirm,
  ids,
  title,
  description,
}: Props) {
  const handlePublish = async () => {
    if (onConfirm) await onConfirm();
    onHide();
  };

  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-lg text-gray-900 font-text-sm-regular">
        <div className="self-stretch relative leading-[28px] font-semibold">
          {title}
        </div>
        <div className="self-stretch relative text-sm leading-[20px] text-gray-600">
          {description}
        </div>
      </div>

      <div className="w-full flex gap-2 items-center">
        <Button variant="outline" onClick={onHide} className="w-full">
          İptal
        </Button>
        <Button className="w-full" onClick={handlePublish}>
          Yayınla
        </Button>
      </div>
    </div>
  );
}
