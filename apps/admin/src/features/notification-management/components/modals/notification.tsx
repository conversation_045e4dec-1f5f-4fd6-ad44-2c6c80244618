import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { Label } from "@mass/shared/components/ui/label";
import { Textarea } from "@mass/shared/components/ui/textarea";
import { useState } from "react";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm: (data: { TR: string; EN: string }) => void | Promise<void>;
}

export default function Notification({
  title,
  description,
  onHide,
  onConfirm,
}: Props & { title?: string; description?: string }) {
  const [descriptionTr, setDescriptionTr] = useState("");
  const [descriptionEn, setDescriptionEn] = useState("");
  const [error, setError] = useState("");

  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <div className="-mt-12 w-full relative flex flex-col items-start justify-start gap-2 text-left text-gray-900 font-text-sm-regular">
        <div className="self-stretch relative leading-[28px] font-semibold">
          {title}
        </div>
        <div className="self-stretch relative text-sm leading-[20px] text-gray-600">
          {description}
        </div>
      </div>

      <div className="w-full flex flex-col gap-4">
        <div className="text-red-500 text-xs">{error}</div>

        <div className="grid w-full gap-1.5">
          <Label htmlFor="message" className="text-sm font-medium">
            Bildirim Açıklaması (TR)
          </Label>
          <Textarea
            id="message"
            placeholder="Bildirim açıklama metni giriniz..."
            className="resize-none"
            rows={4}
            value={descriptionTr}
            onChange={(e) => setDescriptionTr(e.target.value)}
          />
        </div>
        <div className="grid w-full gap-1.5">
          <Label htmlFor="messageEnDesc" className="text-sm font-medium">
            Bildirim Açıklaması (EN)
          </Label>
          <Textarea
            id="messageEnDesc"
            placeholder="Bildirim açıklama metni giriniz..."
            className="resize-none"
            rows={4}
            value={descriptionEn}
            onChange={(e) => setDescriptionEn(e.target.value)}
          />
        </div>
        <div className="flex justify-end w-full">
          <Button
            type="button"
            className="w-full"
            onClick={() => {
              if (!descriptionTr || !descriptionEn) {
                setError("Lütfen tüm alanları doldurun.");
                return;
              }

              onConfirm({ TR: descriptionTr, EN: descriptionEn });
              onHide();
            }}
          >
            Oluştur
          </Button>
        </div>
      </div>
    </div>
  );
}
