import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { Input } from "@mass/shared/components/ui/input";
import { Label } from "@mass/shared/components/ui/label";
import { Textarea } from "@mass/shared/components/ui/textarea";
import { useState } from "react";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm?: (v: {
    id: string;
    label: { TR: string; EN: string };
  }) => void | Promise<void>;
  activeTab: "subscriptions" | "facilities";
  currentIds: string[];
  oldValues?: {
    id: string;
    label: { TR: string; EN: string };
  };
}

export default function Add({
  title,
  description,
  onHide,
  onConfirm,
  currentIds,
  oldValues,
}: Props & { title?: string; description?: string }) {
  const [id, setId] = useState<string>(oldValues?.id ?? "");
  const [type, setType] = useState<string>(oldValues?.label?.TR ?? "");
  const [typeEn, setTypeEn] = useState<string>(oldValues?.label?.EN ?? "");
  const [error, setError] = useState<string>("");

  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <div className="-mt-12 w-full relative flex flex-col items-start justify-start gap-2 text-left text-gray-900 font-text-sm-regular">
        <div className="self-stretch relative leading-[28px] font-semibold">
          {title}
        </div>
        <div className="self-stretch relative text-sm leading-[20px] text-gray-600">
          {description}
        </div>
      </div>

      <div className="w-full flex flex-col gap-4">
        <div className="text-red-500 text-xs">{error}</div>
        <div className="grid w-full gap-1.5">
          <Label htmlFor="message" className="text-sm font-medium">
            Bildirim ID
          </Label>
          <Input
            placeholder="Bildirim ID giriniz..."
            className="resize-none"
            value={id}
            onChange={(e) => setId(e.target.value)}
            disabled={!!oldValues}
          />
        </div>
        <div className="grid w-full gap-1.5">
          <Label htmlFor="message" className="text-sm font-medium">
            Bildirim Tipi (TR)
          </Label>
          <Input
            placeholder="Bildirim tipi giriniz..."
            className="resize-none"
            value={type}
            onChange={(e) => setType(e.target.value)}
          />
        </div>
        <div className="grid w-full gap-1.5">
          <Label htmlFor="messageEnTitle" className="text-sm font-medium">
            Bildirim tipi (EN)
          </Label>
          <Input
            placeholder="Bildirim tipi giriniz..."
            className="resize-none"
            value={typeEn}
            onChange={(e) => setTypeEn(e.target.value)}
          />
        </div>
        <div className="flex justify-end w-full">
          <Button
            type="button"
            className="w-full"
            onClick={() => {
              if (!id.match(/^[a-zA-Z0-9\-]+$/)) {
                setError(
                  "ID sadece sayılardan, latin alfabesindeki harflerden ve tireden oluşmalıdır."
                );
                return;
              }
              if (id.length < 3) {
                setError("ID en az 3 karakter olmalıdır.");
                return;
              }
              if (type.length < 3) {
                setError("Tür en az 3 karakter olmalıdır.");
                return;
              }
              if (typeEn.length < 3) {
                setError("Tür (EN) en az 3 karakter olmalıdır.");
                return;
              }
              if (!oldValues && currentIds.includes(id)) {
                setError("Bu ID zaten mevcut.");
                return;
              }

              onConfirm &&
                onConfirm({
                  id,
                  label: {
                    TR: type,
                    EN: typeEn,
                  },
                });
              onHide();
            }}
          >
            Oluştur
          </Button>
        </div>
      </div>
    </div>
  );
}
