import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import * as Locale from "date-fns/locale";
import { useTranslation } from "react-i18next";
``;
export interface AppNotification {
  id: string;
  createdAt: string;
  textContent: {
    TR: string;
    EN: string;
  };
  title: {
    TR: string;
    EN: string;
  };
  read: boolean;
  regionId: string;
  subscriptionId: string;
  type: string;
  subtype:
    | "notification"
    | "unexpected-usage-warning"
    | "closed-term-warning"
    | "user-limit-warning"
    | "complaint-update"
    | "planned-outage"
    | "unplanned-outage";
  status: "UNREAD" | "READ" | "ARCHIVED";
}

const truncate = (text: string, length = 90) => {
  return text.length > length - 1 ? text.slice(0, length - 1) + "…" : text;
};
export const columns: ColumnDef<AppNotification, unknown>[] = [
  {
    id: "id",
    accessorKey: "notificationInfo",
    header: () => {
      const { t } = useTranslation("notifications");
      return (
        <div className="flex items-center gap-3 pl-2">
          {t("column_notification_info")}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex items-start gap-3 pl-2">
          <div className="flex gap-1">
            <span className={`text-gray-700 flex items-center`}>
              {(row.original as any).title.TR}
            </span>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "type",
    header: ({ table }) => {
      return "Bildirim Tipi";
    },
    cell: ({ row }) => {
      return (
        <span
          className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${row.original.type === "warning" ? "bg-red-50 border border-red-200 text-red-800" : "border"}`}
        >
          {(row.original as any).category.TR}
        </span>
      );
    },
  },
  {
    accessorKey: "title",
    header: ({ table }) => {
      return "Bildirim Başlığı";
    },
    cell: ({ row }) => {
      return <span className="text-sm">{(row.original as any).title.TR}</span>;
    },
  },
  {
    accessorKey: "textContent",
    header: () => "Bildirim İçeriği",
    cell: ({ row }) => {
      return (
        <div className="text-sm">
          {truncate(row.original.textContent.TR, 90)}
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column, table }) => {
      return "Gönderim Tarihi";
    },
    cell: ({ row }) => {
      const dateValue = row.getValue("createdAt") as string;
      if (!dateValue) return null;

      return (
        <div className="text-sm">
          {format(new Date(dateValue), "PPP", { locale: Locale.tr })}
        </div>
      );
    },
  },
];
