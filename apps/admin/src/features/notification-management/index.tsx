import { sidebarData } from "@/constants/sidebar-data";
import { usePastNotifications } from "./queries";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { useState } from "react";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { AnimatedTransition } from "@mass/shared/components/molecules/table-transition";
import { DataTable } from "@mass/shared/components/organisms/table/index";
import Empty from "@mass/shared/components/atoms/empty";
import { columns } from "./components/columns";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Info from "./components/modals/info";
import { format } from "date-fns";
import * as Locale from "date-fns/locale";

export default function NotificationManagement() {
  const [page, setPage] = useState(1);
  const { data, isLoading } = usePastNotifications(page);
  const { totalPages = 0, content = [] } = data ?? {};

  const { open } = useModal();

  const handleRowClick = (row: any) => {
    console.log("row", row);
    open(Info, {
      title: "Bildirim <PERSON>ayı",
      description: (
        <div className="">
          <div className="mb-4">
            <strong>Başlık (TR):</strong> {row.title.TR}
          </div>
          <div className="mb-4">
            <strong>Başlık (EN):</strong> {row.title.EN}
          </div>
          <div className="mb-4">
            <strong>İçerik (TR):</strong> {row.textContent.TR}
          </div>
          <div className="mb-4">
            <strong>İçerik (EN):</strong> {row.textContent.EN}
          </div>
          <div className="mb-4">
            <strong>Tarih:</strong>{" "}
            {format(row.createdAt, "PPP", { locale: Locale.tr })}
          </div>
        </div>
      ),
    });
  };
  const handlePageChange = (page: number) => {
    setPage(Math.max(page, 1));
  };

  return (
    <>
      <Header
        sidebarData={sidebarData}
        title="Bildirim Geçmişi"
        description="MASS platform kullanıcılarına EPİAŞ tarafından gönderilen bildirimler"
        className="flex flex-col"
      />
      <Main>
        <AnimatedTransition loading={isLoading} skeleton={"Yükleniyor..."}>
          <DataTable
            columns={columns}
            data={content}
            onRowClick={handleRowClick}
            tableOptions={{
              getRowId: (row) => row.id,
              enableRowSelection: false,
              state: {
                pagination: {
                  pageIndex: Math.max(page - 1, 0),
                  pageSize: 10,
                },
              },
              manualPagination: true,
              manualSorting: true,
              pageCount: totalPages,
            }}
            empty={
              <Empty
                title={"Bildirim bulunamadı"}
                description={"Henüz hiç bildirim gönderilmedi."}
              ></Empty>
            }
            className="w-full h-full"
            onPageChange={handlePageChange}
          />
        </AnimatedTransition>
      </Main>
    </>
  );
}
