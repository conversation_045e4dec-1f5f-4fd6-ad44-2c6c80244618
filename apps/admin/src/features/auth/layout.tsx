import logo from "@mass/shared/assets/logo-mark.svg";
import DecorativeBackground from "@mass/shared/assets/pattern_decorative1.svg";
interface Props {
  children: React.ReactNode;
}

export default function AuthLayout({ children }: Props) {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row max-w-7xl mx-auto">
      <div className="flex-1 flex items-center justify-center p-4 sm:p-8">
        <img
          className="w-full pointer-events-none absolute !m-[0] top-[-264px] max-w-full overflow-hidden h-[768px]"
          src={DecorativeBackground}
          alt="background"
        />

        <div className="w-full max-w-md space-y-6">
          <div className="mb-4 flex items-center justify-center">
            <img src={logo} alt="MASS Logo" className="h-20 mr-4" />
          </div>

          <div className="flex flex-col gap-2 pb-4">
            <span className="w-full relative text-3xl leading-[38px] font-semibold text-gray-900 text-center inline-block">
              Admin paneline giriş yap
            </span>

            <span className="text-center text-gray-500">
              E-posta ve şifre ile admin paneline güvenli bir şekilde giriş
              yapabilirsiniz.
            </span>
          </div>

          {children}
        </div>
      </div>
    </div>
  );
}
