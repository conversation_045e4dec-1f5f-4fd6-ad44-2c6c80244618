import type { ForgotPasswordFormType } from '@mass/shared/lib/schemas/auth'
import AuthLayout from '@/features/auth/layout'
import { useNavigate } from '@tanstack/react-router'
import { useState } from 'react'
import { ForgotPasswordForm } from './components/forgot-password-form'
import { ForgotPasswordSuccess } from './components/forgot-password-success'
import { useForgotPasswordMutation } from '../../data/queries'

export default function ForgotPassword() {
    const navigate = useNavigate()
    const [error, setError] = useState<string | null>(null)
    const [sentEmail, setSentEmail] = useState<string | null>(null)
    
    const forgotPasswordMutation = useForgotPasswordMutation()
    const isLoading = forgotPasswordMutation.isPending
    const isResending = forgotPasswordMutation.isPending

    const handleSubmit = async (data: ForgotPasswordFormType) => {
        setError(null)
        
        try {
            await forgotPasswordMutation.mutateAsync(data.email)
            setSentEmail(data.email)
        }
        catch (err) {
            setError('<PERSON><PERSON>re sıfırlama işlemi başarısız oldu')
            console.error('Password reset error:', err)
        }
    }

    const handleResend = async () => {
        if (!sentEmail) return
        
        try {
            await forgotPasswordMutation.mutateAsync(sentEmail)
        }
        catch (err) {
            setError('Kod gönderimi başarısız oldu')
            console.error('Code resend error:', err)
        }
    }

    const handleBack = () => {
        navigate({ to: '/login' })
    }

    return (
        <AuthLayout>
            {sentEmail
                ? (
                        <ForgotPasswordSuccess
                            email={sentEmail}
                            onResend={handleResend}
                            onBack={handleBack}
                            isResending={isResending}
                        />
                    )
                : (
                        <ForgotPasswordForm
                            onSubmit={handleSubmit}
                            onBack={handleBack}
                            isLoading={isLoading}
                            error={error}
                        />
                    )}
        </AuthLayout>
    )
}
