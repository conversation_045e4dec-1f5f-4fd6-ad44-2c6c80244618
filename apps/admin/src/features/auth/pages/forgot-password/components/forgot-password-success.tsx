import { Button } from '@mass/shared/components/ui/button'
import { Loader2 } from 'lucide-react'

interface ForgotPasswordSuccessProps {
    email: string
    onResend: () => Promise<void>
    onBack: () => void
    isResending: boolean
}

export function ForgotPasswordSuccess({
    email,
    onResend,
    onBack,
    isResending,
}: ForgotPasswordSuccessProps) {
    return (
        <div className="w-full max-w-md space-y-8">
            {/* Success message */}
            <div className="text-center text-gray-600">
                <p>
                    <span className="font-medium">{email}</span>
                    {' '}
                    adresine şifre
                    <br />
                    sıfırlama bağlantısı gönderdik
                </p>
            </div>

            {/* Actions */}
            <div className="space-y-6">
                <Button
                    className="w-full"
                    onClick={() => window.open(`mailto:${email}`)}
                >
                    Mail adresine git
                </Button>

                <div className="text-center text-sm">
                    <span className="text-gray-600">Bana mail gelmedi? </span>
                    <Button
                        variant="link"
                        className="p-0 h-auto text-[#df4f16]"
                        onClick={onResend}
                        disabled={isResending}
                    >
                        {isResending
                            ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Gönderiliyor...
                                    </>
                                )
                            : (
                                    'Tekrar gönder'
                                )}
                    </Button>
                </div>

                <div className="text-center">
                    <Button
                        variant="link"
                        className="text-gray-600"
                        onClick={onBack}
                        disabled={isResending}
                    >
                        ← Giriş ekranına geri dön
                    </Button>
                </div>
            </div>
        </div>
    )
}
