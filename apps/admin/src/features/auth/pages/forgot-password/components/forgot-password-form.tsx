import type {
    ForgotPasswordFormType,
} from '@mass/shared/lib/schemas/auth'
import { AuthAlert } from '@mass/shared/components/organisms/auth/alert'
import { Button } from '@mass/shared/components/ui/button'
import { Input } from '@mass/shared/components/ui/input'
import {
    forgotPasswordSchema,
} from '@mass/shared/lib/schemas/auth'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

interface ForgotPasswordFormProps {
    onSubmit: (data: ForgotPasswordFormType) => Promise<void>
    onBack: () => void
    isLoading: boolean
    error: string | null
}

export function ForgotPasswordForm({
    onSubmit,
    onBack,
    isLoading,
    error,
}: ForgotPasswordFormProps) {
    const { t } = useTranslation('common')

    const form = useForm<ForgotPasswordFormType>({
        resolver: zodResolver(forgotPasswordSchema),
        defaultValues: {
            email: '',
        },
    })

    return (
        <div className="w-full max-w-md space-y-8">
            {/* Description */}
            <div className="text-center text-gray-600 italic">
                <p>
                    {t('auth.forgotPassword.description')}
                </p>
            </div>

            {error && <AuthAlert title={t('common.error')} description={error} />}

            {/* Form */}
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                    <label className="text-sm font-medium">{t('auth.forgotPassword.emailLabel')}</label>
                    <Input
                        type="email"
                        placeholder={t('auth.forgotPassword.emailPlaceholder')}
                        {...form.register('email')}
                    />
                    {form.formState.errors.email && (
                        <p className="text-sm text-destructive">
                            {form.formState.errors.email.message}
                        </p>
                    )}
                </div>

                <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading
                        ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    {t('auth.forgotPassword.resetLoadingButton')}
                                </>
                            )
                        : (
                                t('auth.forgotPassword.resetButton')
                            )}
                </Button>
            </form>

            <div className="text-center">
                <Button
                    variant="link"
                    className="text-gray-600"
                    onClick={onBack}
                    disabled={isLoading}
                >
                    {t('auth.common.backToLogin')}
                </Button>
            </div>
        </div>
    )
}
