import type { FieldConfig } from "@mass/shared/components/ui/auto-form/types";
import type { LoginFormType } from "@mass/shared/lib/schemas/auth";
import { AuthAlert } from "@mass/shared/components/organisms/auth/alert";
import AutoForm from "@mass/shared/components/ui/auto-form/index";
import { But<PERSON> } from "@mass/shared/components/ui/button";
import { loginSchema } from "@mass/shared/lib/schemas/auth";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";

interface LoginFormProps {
  onSubmit: (data: LoginFormType) => Promise<void>;
  error?: string;
  isLoading: boolean;
}

export function LoginForm({ onSubmit, error, isLoading }: LoginFormProps) {
  const { t } = useTranslation("common");

  const fieldConfig: FieldConfig<LoginFormType> = {
    email: {
      label: t("auth.login.email"),
      inputProps: {
        type: "email",
        placeholder: t("auth.login.emailPlaceholder"),
      },
    },
    password: {
      label: t("auth.login.password"),
      inputProps: {
        type: "password",
        placeholder: t("auth.login.passwordPlaceholder"),
      },
    },
  };

  return (
    <div className="space-y-6 mt-4">
      {error ? (
        <AuthAlert title={t("auth.login.errorTitle")} description={error} />
      ) : null}

      <AutoForm
        formSchema={loginSchema}
        fieldConfig={fieldConfig as any}
        onSubmit={onSubmit}
        values={{
          email: "",
          password: "",
        }}
      >
        <Button type="submit" className="w-full mt-4" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("auth.login.loginLoading")}
            </>
          ) : (
            t("auth.login.loginButton")
          )}
        </Button>
      </AutoForm>
    </div>
  );
}
