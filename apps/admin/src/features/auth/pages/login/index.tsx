import type { LoginFormType } from '@mass/shared/lib/schemas/auth'
import { useNavigate } from '@tanstack/react-router'
import { useState, useMemo } from 'react'
import { useLoginMutation } from '../../data/queries'
import AuthLayout from '../../layout'
import { LoginForm } from './components/login-form'

const MAX_LOGIN_ATTEMPTS = 3
const BLOCK_DURATION = 15 * 60 * 1000

export default function Auth() {
    const [error, setError] = useState<string>('')
    const [loginAttempts, setLoginAttempts] = useState(0)
    const loginMutation = useLoginMutation()
    const navigate = useNavigate()

    const handleLogin = async (data: LoginFormType) => {
        setError('')
        
        try {
            const response = await loginMutation.mutateAsync({
                email: data.email,
                password: data.password,
            })

            if (response.session) {
                setLoginAttempts(0)
                navigate({ to: '/' })
            } else {
                setError(response.message || 'Geçersiz e-posta veya şifre')
            }
        } catch (err) {
            setError('<PERSON><PERSON><PERSON> ya<PERSON>ılırken bir hata oluştu')
            console.error('Login error:', err)
        }
    }

    return (
        <AuthLayout>
            <LoginForm
                onSubmit={handleLogin}
                error={error}
                isLoading={loginMutation.isPending}
            />
        </AuthLayout>
    )
}
