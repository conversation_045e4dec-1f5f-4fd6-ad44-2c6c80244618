import type { HTMLAttributes } from 'react'
import { PinInput, PinInputField } from '@mass/shared/components/atoms/pin-input'
import { AuthAlert } from '@mass/shared/components/organisms/auth/alert'
import { Button } from '@mass/shared/components/ui/button'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormMessage,
} from '@mass/shared/components/ui/form'
import { Input } from '@mass/shared/components/ui/input'
import { cn } from '@mass/shared/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import { useNavigate } from '@tanstack/react-router'
import { Loader2 } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

type OtpFormProps = HTMLAttributes<HTMLDivElement> & {
    email?: string
    onVerify?: (code: string) => Promise<void>
    onResend?: () => Promise<void>
    onBack?: () => void
    isResending?: boolean
}

const MAX_VERIFY_ATTEMPTS = 3

const formSchema = z.object({
    otp: z
        .string()
        .min(1, { message: 'auth:otp_required' }),
})

export function OtpForm({
    className,
    email,
    onVerify,
    onResend,
    onBack,
    isResending = false,
    ...props
}: OtpFormProps) {
    const { t } = useTranslation('common')
    const navigate = useNavigate()
    const [isLoading, setIsLoading] = useState(false)
    const [disabledBtn, setDisabledBtn] = useState(true)
    const [timeLeft, setTimeLeft] = useState(300)
    const [verifyAttempts, setVerifyAttempts] = useState(0)
    const [error, setError] = useState<string | null>(null)

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: { otp: '' },
    })

    useEffect(() => {
        if (timeLeft <= 0)
            return

        const timer = setInterval(() => {
            setTimeLeft(prev => prev - 1)
        }, 1000)

        return () => clearInterval(timer)
    }, [timeLeft])

    const formatTime = (seconds: number) => {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${String(minutes).padStart(2, '0')}:${String(
            remainingSeconds,
        ).padStart(2, '0')}`
    }

    async function onSubmit(data: z.infer<typeof formSchema>) {
        if (verifyAttempts >= MAX_VERIFY_ATTEMPTS) {
            setError(
                t('auth.otp.tooManyAttempts')
            )
            return
        }

        setIsLoading(true)
        try {
            if (onVerify) {
                await onVerify(data.otp)
            }
            else {
                await new Promise(resolve => setTimeout(resolve, 1000))
                navigate({ to: '/' })
            }
        }
        catch {
            const newAttempts = verifyAttempts + 1
            setVerifyAttempts(newAttempts)

            if (newAttempts >= MAX_VERIFY_ATTEMPTS) {
                setError(
                    t('auth.otp.tooManyAttempts')
                )
            }
            else {
                setError(
                    t('auth.otp.invalidCode', { attempts: MAX_VERIFY_ATTEMPTS - newAttempts })
                )
            }
        }
        finally {
            setIsLoading(false)
        }
    }

    const handleResend = async () => {
        setError(null)
        setVerifyAttempts(0)
        if (onResend) {
            await onResend()
        }
        setTimeLeft(300)
    }

    return (
        <div className={cn('space-y-6', className)} {...props}>
            <div className="text-center text-gray-600">
                <p>
                    <span className="font-medium">{email}</span>
                    {' '}
                    {t('auth.otp.sentCode')}
                </p>
            </div>

            <div className="flex justify-center py-2">
                <div className="h-7 px-3 py-1 bg-[#fef6ee] rounded-2xl border border-[#f8dbaf] items-center inline-flex">
                    <div className="text-center">
                        <span className="text-[#df4f16] text-sm font-medium leading-tight">
                            {t('auth.otp.timeRemaining')}
                            {' '}
                        </span>
                        <span className="text-[#df4f16] text-sm font-bold leading-tight">
                            {formatTime(timeLeft)}
                        </span>
                    </div>
                </div>
            </div>

            {error && <AuthAlert title={t('common.error')} description={error} />}

            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                    <div className="grid gap-2">
                        <FormField
                            control={form.control}
                            name="otp"
                            render={({ field }) => (
                                <FormItem className="space-y-1">
                                    <FormControl>
                                        <PinInput
                                            {...field}
                                            className="flex h-10 justify-center gap-2"
                                            onComplete={() => setDisabledBtn(false)}
                                            onIncomplete={() => setDisabledBtn(true)}
                                        >
                                            {Array.from({ length: 6 }, (_, i) => (
                                                <PinInputField
                                                    key={i}
                                                    component={Input}
                                                    className={cn(
                                                        'w-12 h-12 text-center text-lg',
                                                        form.getFieldState('otp').invalid
                                                            ? 'border-red-500'
                                                            : '',
                                                    )}
                                                />
                                            ))}
                                        </PinInput>
                                    </FormControl>
                                    <FormMessage className="text-center" />
                                </FormItem>
                            )}
                        />
                        <Button
                            className="mt-8 w-full font-medium"
                            disabled={
                                disabledBtn
                                || isLoading
                                || verifyAttempts >= MAX_VERIFY_ATTEMPTS
                            }
                        >
                            {isLoading
                                ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            {t('auth.otp.verifying')}
                                        </>
                                    )
                                : (
                                        t('auth.otp.verify')
                                    )}
                        </Button>
                    </div>
                </form>
            </Form>

            <div className="text-center text-sm">
                <span className="text-gray-600">
                    {t('auth.otp.noCodeReceived')}
                    {' '}
                </span>
                <Button
                    variant="link"
                    className="p-0 h-auto"
                    onClick={handleResend}
                    disabled={timeLeft > 0 || isResending}
                >
                    {isResending
                        ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    {t('auth.otp.sendingAgain')}
                                </>
                            )
                        : (
                                t('auth.otp.sendAgain')
                            )}
                </Button>
            </div>

            <div className="text-center">
                <Button
                    variant="link"
                    className="text-gray-600"
                    onClick={() => navigate({ to: '/login' })}
                    disabled={isLoading || isResending}
                >
                    {t('auth.common.backToLogin')}
                </Button>
            </div>
        </div>
    )
}
