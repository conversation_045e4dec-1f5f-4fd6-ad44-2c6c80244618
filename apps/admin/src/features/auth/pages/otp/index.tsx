import { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'
import AuthLayout from '../../layout'
import { OtpForm } from './components/otp-form'
import { useVerifyOtpMutation } from '../../data/queries'

export default function Otp() {
    const navigate = useNavigate()
    const [email] = useState<string>(() => {
        const urlParams = new URLSearchParams(window.location.search)
        return urlParams.get('email') || sessionStorage.getItem('otp_email') || ''
    })
    
    const verifyOtpMutation = useVerifyOtpMutation()
    const isResending = false
    
    const handleVerify = async (code: string) => {
        if (!email) {
            throw new Error('Email is missing')
        }
        
        const response = await verifyOtpMutation.mutateAsync({ email, code })
        
        if (response.success) {
            navigate({ to: '/' })
        } else {
            throw new Error(response.message || 'Verification failed')
        }
    }
    
    return (
        <AuthLayout>
            <OtpForm 
                email={email}
                onVerify={handleVerify}
                isResending={isResending}
            />
        </AuthLayout>
    )
}
