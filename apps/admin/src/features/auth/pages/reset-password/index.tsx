import type { ResetPasswordFormType } from '@mass/shared/lib/schemas/auth'
import { useNavigate } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import AuthLayout from '../../layout'
import { ResetPasswordForm } from './components/reset-password-form'
import { ResetPasswordSuccess } from './components/reset-password-success'
import { useResetPasswordMutation } from '../../data/queries'

function ResetPasswordPage() {
    const navigate = useNavigate()
    const [error, setError] = useState<string | null>(null)
    const [isSuccess, setIsSuccess] = useState(false)
    const [token, setToken] = useState<string>('')
    
    const resetPasswordMutation = useResetPasswordMutation()
    const isLoading = resetPasswordMutation.isPending

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search)
        const tokenFromUrl = urlParams.get('token')
        
        if (tokenFromUrl) {
            setToken(tokenFromUrl)
        } else {
            navigate({ to: '/login' })
        }
    }, [navigate])

    const handleSubmit = async (data: ResetPasswordFormType) => {
        setError(null)
        
        try {
            await resetPasswordMutation.mutateAsync({
                token,
                password: data.password
            })
            setIsSuccess(true)
        }
        catch (err) {
            setError('Şifre sıfırlama işlemi başarısız oldu')
            console.error('Password reset error:', err)
        }
    }

    const handleBack = () => {
        navigate({ to: '/login' })
    }

    const handleLogin = () => {
        navigate({ to: '/login' })
    }

    if (isSuccess) {
        return (
            <AuthLayout>
                <ResetPasswordSuccess onLogin={handleLogin} />
            </AuthLayout>
        )
    }

    return (
        <AuthLayout>
            <ResetPasswordForm
                onSubmit={handleSubmit}
                onBack={handleBack}
                isLoading={isLoading}
                error={error}
            />
        </AuthLayout>
    )
}

export default ResetPasswordPage
