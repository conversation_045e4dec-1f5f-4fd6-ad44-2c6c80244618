import type { ResetPasswordFormType } from '@mass/shared/lib/schemas/auth'
import { AuthAlert } from '@mass/shared/components/organisms/auth/alert'
import { Button } from '@mass/shared/components/ui/button'
import { Input } from '@mass/shared/components/ui/input'
import { resetPasswordSchema } from '@mass/shared/lib/schemas/auth'
import { calculatePasswordStrength } from '@mass/shared/utils/calculate-password-strength'
import { zodResolver } from '@hookform/resolvers/zod'
import { Check, Loader2, X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

interface ResetPasswordFormProps {
    onSubmit: (data: ResetPasswordFormType) => Promise<void>
    onBack: () => void
    isLoading: boolean
    error: string | null
}

function getStrengthColor(strength: number): string {
    if (strength <= 25)
        return 'bg-red-500'
    if (strength <= 50)
        return 'bg-orange-500'
    if (strength <= 75)
        return 'bg-yellow-500'
    return 'bg-green-500'
}

export function ResetPasswordForm({
    onSubmit,
    onBack,
    isLoading,
    error,
}: ResetPasswordFormProps) {
    const { t } = useTranslation('common')
    const [passwordStrength, setPasswordStrength] = useState(0)

    const form = useForm<ResetPasswordFormType>({
        resolver: zodResolver(resetPasswordSchema),
        defaultValues: {
            password: '',
            confirmPassword: '',
        },
    })

    useEffect(() => {
        const subscription = form.watch((value, { name }) => {
            if (name === 'password') {
                setPasswordStrength(calculatePasswordStrength(value.password || ''))
            }
        })
        return () => subscription.unsubscribe()
    }, [form.watch])

    const password = form.watch('password')
    const hasStartedTyping = password.length > 0

    return (
        <div className="w-full max-w-md space-y-8">
            {/* Description */}
            <div className="text-center text-gray-600">
                <p>
                    {t('auth.resetPassword.description')}
                </p>
            </div>

            {error && <AuthAlert title={t('common.error')} description={error} />}

            {/* Form */}
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                    <label className="text-sm font-medium">{t('auth.resetPassword.newPassword')}</label>
                    <Input type="password" {...form.register('password')} />
                    {form.formState.errors.password && (
                        <p className="text-sm text-destructive">
                            {form.formState.errors.password.message}
                        </p>
                    )}
                </div>

                <div className="space-y-2">
                    <label className="text-sm font-medium">{t('auth.resetPassword.confirmPassword')}</label>
                    <Input type="password" {...form.register('confirmPassword')} />
                    {form.formState.errors.confirmPassword && (
                        <p className="text-sm text-destructive">
                            {form.formState.errors.confirmPassword.message}
                        </p>
                    )}
                </div>

                <div className="h-2 w-full bg-[#E6E6E6] rounded-full overflow-hidden">
                    <div
                        className={`h-full transition-all duration-300 ${getStrengthColor(
                            passwordStrength,
                        )}`}
                        style={{ width: `${passwordStrength}%` }}
                    />
                </div>

                <div className="space-y-3 text-[15px] text-[#6B7280]">
                    <div className="flex items-center gap-3">
                        <div
                            className={`w-6 h-6 rounded-full flex items-center justify-center ${
                                !hasStartedTyping
                                    ? 'bg-gray-400'
                                    : /^.{8,}$/.test(password)
                                        ? 'bg-green-500'
                                        : 'bg-red-500'
                            }`}
                        >
                            {!hasStartedTyping && <Check className="w-4 h-4 text-white" />}
                            {hasStartedTyping
                                && (/^.{8,}$/.test(password)
                                    ? (
                                            <Check className="w-4 h-4 text-white" />
                                        )
                                    : (
                                            <X className="w-4 h-4 text-white" />
                                        ))}
                        </div>
                        <span>{t('auth.resetPassword.minLength')}</span>
                    </div>
                    <div className="flex items-center gap-3">
                        <div
                            className={`w-6 h-6 rounded-full flex items-center justify-center ${
                                !hasStartedTyping
                                    ? 'bg-gray-400'
                                    : /(?=.*[a-z])(?=.*[A-Z!@#$%^&*(),.?":{}|<>])(?=.*\d)/.test(
                                        password,
                                    )
                                        ? 'bg-green-500'
                                        : 'bg-red-500'
                            }`}
                        >
                            {!hasStartedTyping && <Check className="w-4 h-4 text-white" />}
                            {hasStartedTyping
                                && (/(?=.*[a-z])(?=.*[A-Z!@#$%^&*(),.?":{}|<>])(?=.*\d)/.test(
                                    password,
                                )
                                    ? (
                                            <Check className="w-4 h-4 text-white" />
                                        )
                                    : (
                                            <X className="w-4 h-4 text-white" />
                                        ))}
                        </div>
                        <span>
                            {t('auth.resetPassword.specialCharacters')}
                        </span>
                    </div>
                </div>

                <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading
                        ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    {t('auth.resetPassword.savingButton')}
                                </>
                            )
                        : (
                                t('auth.resetPassword.saveButton')
                            )}
                </Button>
            </form>

            <div className="text-center">
                <Button
                    variant="link"
                    className="text-gray-600"
                    onClick={onBack}
                    disabled={isLoading}
                >
                    {t('auth.common.backToLogin')}
                </Button>
            </div>
        </div>
    )
}
