import { Button } from '@mass/shared/components/ui/button'
import { Check } from 'lucide-react'
import { useTranslation } from 'react-i18next'

interface ResetPasswordSuccessProps {
    onLogin: () => void
}

export function ResetPasswordSuccess({
    onLogin,
}: ResetPasswordSuccessProps) {
    const { t } = useTranslation('common')

    return (
        <div className="w-full max-w-md space-y-8">
            {/* Success Icon */}
            <div className="flex justify-center">
                <div className="w-16 h-16 rounded-full bg-green-500/10 flex items-center justify-center">
                    <Check className="w-8 h-8 text-green-500" />
                </div>
            </div>

            {/* Title and Description */}
            <div className="text-center space-y-6">
                <h2 className="text-3xl font-semibold">{t('auth.resetPassword.success.title')}</h2>
                <p className="text-gray-600">
                    {t('auth.resetPassword.success.description')}
                </p>
            </div>

            {/* Action Button */}
            <Button onClick={onLogin} className="w-full">
                {t('auth.login')}
            </Button>
        </div>
    )
}
