import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { authService } from "../../../services/api/auth";

export const authKeys = {
  all: ["auth"] as const,
  session: () => [...authKeys.all, "session"] as const,
  user: () => [...authKeys.all, "user"] as const,
};

const logQueryError = (error: unknown) => {
  if (axios.isAxiosError(error)) {
    const errorMessage = error.response?.data?.message || error.message;
    console.error(`Auth API Error: ${errorMessage}`);
    return errorMessage;
  }
  console.error("Unexpected auth error:", error);
  return "An unexpected error occurred";
};

export const useMe = () => {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: () => authService.me(),
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
};

export const useLoginMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) => {
      queryClient.invalidateQueries();
      
      return authService.login(email, password);
    },
    onError: (error) => logQueryError(error),
  });
};

export const useRegisterMutation = () => {
  return useMutation({
    mutationFn: ({
      email,
      password,
      phone,
      tckn,
    }: {
      email: string;
      password: string;
      phone?: string;
      tckn?: string;
    }) => authService.register({ email, password, phone, tckn }),
    onError: (error) => logQueryError(error),
  });
};

export const useForgotPasswordMutation = () => {
  return useMutation({
    mutationFn: (email: string) => authService.forgotPassword(email),
    onError: (error) => logQueryError(error),
  });
};

export const useResetPasswordMutation = () => {
  return useMutation({
    mutationFn: ({ token, password }: { token: string; password: string }) =>
      authService.resetPassword(token, password),
    onError: (error) => logQueryError(error),
  });
};

export const useVerifyOtpMutation = () => {
  return useMutation({
    mutationFn: ({ email, code }: { email: string; code: string }) =>
      authService.verifyOtp(code, email),
    onError: (error) => logQueryError(error),
    onSuccess: (data) => {
      return data;
    }
  });
};

export const useLogoutMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: () => authService.logout(),
    onError: (error) => logQueryError(error),
    onSuccess: () => {
      queryClient.clear();
    }
  });
};
