import api from "@/services/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";

export const useGlobalSetting = <T>(
  settingKey: string,
  onLoad?: (data: T) => void
) => {
  const fetches = useRef(0);
  const queryClient = useQueryClient();
  const { data, isLoading } = useQuery<T>({
    queryFn: async () => {
      const response = await api(
        "/setting/global/" + encodeURIComponent(settingKey)
      );

      return response.value;
    },
    queryKey: ["globalSetting", settingKey],
  });

  useEffect(() => {
    if (isLoading || !data) return;
    if (fetches.current === 0) {
      if (onLoad) onLoad(data);
    }
    fetches.current++;
  }, [isLoading, data]);

  const { mutateAsync } = useMutation({
    mutationFn: async (data: T) => {
      return await api("/setting/global/" + encodeURIComponent(settingKey), {
        method: "PATCH",
        body: JSON.stringify({ value: data }),
      });
    },
    mutationKey: ["globalSetting", settingKey],
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["globalSetting", settingKey],
      });
    },
  });

  return {
    data,
    isLoading,
    update: mutateAsync,
  };
};
