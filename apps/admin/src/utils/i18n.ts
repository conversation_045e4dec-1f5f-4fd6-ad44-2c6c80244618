import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import resources from 'virtual:i18next-loader'

i18n
    .use(initReactI18next)
    .init({
        resources,
        lng: 'tr',
        fallbackLng: 'tr',

        interpolation: {
            escapeValue: false,
        },

        react: {
            useSuspense: false,
        },

        debug: import.meta.env.DEV,
    })

export default i18n
