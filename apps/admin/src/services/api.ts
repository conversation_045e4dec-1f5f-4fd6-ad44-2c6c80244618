import { toast } from "sonner";

const baseURL = "/api";

async function api(endpoint: string, options: RequestInit = {}) {
  const url = baseURL + endpoint;


  const defaultOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    credentials: "include",
  };

  const mergedOptions: RequestInit = {
    ...defaultOptions,
    ...options,
    headers: {
      ...(defaultOptions.headers as Record<string, string>),
      ...(options.headers || {}),
    },
  };

  if (import.meta.env.DEV) {
    console.log(
      `📤 [API Request] ${(mergedOptions.method || "GET").toUpperCase()} ${url}`,
      mergedOptions
    );
  }

  try {
    const response = await fetch(url, mergedOptions);

    if (import.meta.env.DEV) {
      console.log(
        `📥 [API Response] ${(mergedOptions.method || "GET").toUpperCase()} ${url}`,
        {
          status: response.status,
          statusText: response.statusText,
        }
      );
    }

    if (!response.ok) {
      if (response.status === 401 || response.status === 403) {
        // Use typeof window check to satisfy TypeScript
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
        return null;
      }

      let errorData = {} as { message?: string };

      try {
        const jsonResponse = await response.json();
        if (typeof jsonResponse === "object" && jsonResponse !== null) {
          errorData = jsonResponse as { message?: string };
        }
      } catch (e) {
        errorData = {};
      }

      const errorMessage =
        errorData.message || `${response.status}: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      return await response.json();
    }

    return await response.text();
  } catch (error) {
    if (error instanceof Error) {
      if (!error.message.startsWith("4") && !error.message.startsWith("5")) {
        toast.error(
          "Bağlantı hatası. Lütfen internet bağlantınızı kontrol edin."
        );
      }
    }
    throw error;
  }
}

export default api;
