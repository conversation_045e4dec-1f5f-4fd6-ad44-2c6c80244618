export interface Permission {
  scope: string;
}
export interface MeResponse {
  user?: {
    id: string;
    email: string;
    name: string;
  };
  error?: string;
}

export interface LoginResponse {
  session?: string;
  user?: {
    id: string;
    email: string;
    name: string;
  };
  message?: string;
  success: boolean;
}

export interface RegisterResponse {
  session?: string;
  user?: {
    id: string;
    email: string;
    name: string;
  };
  message?: string;
  success: boolean;
}

export const authService = {
  async me(): Promise<MeResponse> {
    const response = await fetch("/api/auth/me");
    if (!response.ok) {
      throw new Error(`Failed to fetch user: ${response.statusText}`);
    }
    const result = await response.json();
    return result as MeResponse;
  },

  async login(email: string, password: string): Promise<LoginResponse> {
    const response = await fetch("/api/auth/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.statusText}`);
    }

    const result = await response.json();

    return result as LoginResponse;
  },

  async register(data): Promise<RegisterResponse> {
    const response = await fetch("/api/auth/register", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Registration failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result as RegisterResponse;
  },

  async verifyOtp(
    code: string,
    token: string
  ): Promise<{ success: boolean; message?: string }> {
    const response = await fetch("/api/auth/verify-otp", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ code, token }),
    });

    if (!response.ok) {
      throw new Error(`OTP verification failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result as { success: boolean; message?: string };
  },

  async forgotPassword(
    email: string
  ): Promise<{ success: boolean; message?: string }> {
    const response = await fetch("/api/auth/forgot-password", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      throw new Error(`Forgot password request failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result as { success: boolean; message?: string };
  },

  async resetPassword(
    token: string,
    password: string
  ): Promise<{ success: boolean; message?: string }> {
    const response = await fetch("/api/auth/reset-password", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token, password }),
    });

    if (!response.ok) {
      throw new Error(`Reset password failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result as { success: boolean; message?: string };
  },

  async logout(): Promise<void> {
    await fetch("/api/auth/logout", { method: "DELETE" });

    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
  },
};
