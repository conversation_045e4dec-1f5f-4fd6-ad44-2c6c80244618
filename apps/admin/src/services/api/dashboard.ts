import { addDays, addMonths, startOfMonth, subYears } from "date-fns";
import api from "../api";

export const analyticsService = {
  async data() {
    const response = (await api("/analytics/data")) as {
      active_users: { curr: number; prev: number }[];
      total_users_per_device: { device: string; count: number }[];
      median_duration_prev: { median_duration_seconds: number }[];
      median_duration_curr: { median_duration_seconds: number }[];
      users_per_month: { count: number; date: string }[];
      total_users: { curr: number; prev: number };
      analytics_events: {
        type: string;
        subtype: string;
        count: number;
        prev_count: number;
      }[];
      live_users: { curr: number; prev: number }[];
      survival: { max_days: number; count: number }[];
    };

    let totalUsers = 0;
    const actualSurvival = response.survival
      .sort((a, b) => b.max_days - a.max_days)
      .map((item) => {
        totalUsers += item.count;
        return {
          days: item.max_days,
          amount: totalUsers,
          ratio: 0,
        };
      })
      .reverse()
      .map((item, index, self) => ({
        ...item,
        ratio: self[0].amount === 0 ? 0 : item.amount / self[0].amount,
      }));

    const zeroFilledPerMonth = [] as { count: number; date: Date }[];

    let workingDate = addDays(subYears(startOfMonth(new Date()), 1), 3);
    for (let i = 0; i < 12; i++) {
      workingDate = addMonths(workingDate, 1);
      const workingDateIso = workingDate.toISOString().split("T")[0];
      const workingDateParts = workingDateIso.split("-");
      const yearMonth = `${workingDateParts[0]}${workingDateParts[1]}`;
      zeroFilledPerMonth.push({
        count:
          response.users_per_month.find((item) => item.date === yearMonth)
            ?.count ?? 0,
        date: new Date(workingDate),
      });
    }

    const perDeviceTotal = response.total_users_per_device.reduce(
      (acc, item) => acc + item.count,
      0
    );

    return {
      active: response.active_users?.[0] ?? { curr: 0, prev: 0 },
      perDevice: (response.total_users_per_device ?? []).map((d) => ({
        name: d.device,
        count: d.count,
        ratio: perDeviceTotal === 0 ? 0 : d.count / perDeviceTotal,
      })),
      duration: {
        prev: response.median_duration_prev?.[0]?.median_duration_seconds ?? 0,
        curr: response.median_duration_curr?.[0]?.median_duration_seconds ?? 0,
      },
      perMonth: zeroFilledPerMonth,
      total: response.total_users ?? { curr: 0, prev: 0 },
      events: (response.analytics_events ?? [])
        .sort((a, b) => b.count - a.count)
        .map((d) => ({
          type: d.type,
          subtype: d.subtype,
          count: d.count,
          prev: d.prev_count,
        })),
      live: response.live_users?.[0] ?? { curr: 0, prev: 0 },
      survival: actualSurvival,
    };
  },

  async logs(page: number, size = 10) {
    const data = (await api("/analytics/admin/log?pageSize=" + size + "&pageNumber=" + page)) as {
      id: string;
      ip: string;
      date: string;
      key: string;
      value: string;
      user_id: string;
      user_email: string;
    }[];

    return data.map((item) => ({
      ...item,
      date: new Date(item.date),
    }));
  },
};
