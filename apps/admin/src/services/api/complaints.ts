import api from '../api'

export interface ComplaintPayload {
    body: string
    category: string
    subcategory: string
    subscriptionId: string
    files: string[]
}

export async function fetchComplaints(page: number, pageSize: number) {
    return api(`/complaint?pageNumber=${page}&pageSize=${pageSize}`)
}

export async function fetchComplaintById(id: string) {
    try {
        const response = await api(`/complaint/${id}`)
        console.log('Fetched complaint by ID:', id, response)
        if (!response) {
            throw new Error('No response from server')
        }
        return response
    } catch (error) {
        console.error(`Error fetching complaint by ID (${id}):`, error)
        throw error
    }
}

export async function createComplaint(payload: ComplaintPayload) {
    return api('/complaint', {
        method: 'POST',
        body: JSON.stringify(payload),
    })
}

export async function uploadDocument(file: File) {
    const payload = {
        name: file.name,
        size: file.size,
        mimeType: file.type,
    }

    return api('/document', {
        method: 'POST',
        body: JSON.stringify(payload),
        headers: {
            'Content-Type': 'application/json',
        },
    })
}

export async function fetchDocument(fileId: string) {
    return api(`/document/${fileId}`)
}

export async function markDocumentAsDone(fileId: string) {
    return api(`/document/${fileId}/done`, {
        method: 'PATCH',
    })
}
