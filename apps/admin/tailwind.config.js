import tailwindCssAnimate from 'tailwindcss-animate'
import { theme } from '@mass/shared/config/tailwind'
const fonts = ["inter", "manrope", "system"];

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: ['class'],
    content: [
        './index.html',
        './src/**/*.{ts,tsx,js,jsx}', 
        '../../shared/**/*.{ts,tsx,js,jsx}',
        '!../../shared/**/node_modules/**/*'
    ],
    safelist: fonts.map(font => `font-${font}`),
    theme,
    plugins: [
        tailwindCssAnimate,
        require('@tailwindcss/typography'),
    ],
}
