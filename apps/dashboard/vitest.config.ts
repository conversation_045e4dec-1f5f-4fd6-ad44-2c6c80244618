import { defineConfig } from 'vitest/config';
import { fileURLToPath } from 'node:url';
import path from 'node:path';

export default defineConfig({
    test: {
      environment: 'jsdom',
      globals: true,
      setupFiles: ['./src/test/setup.ts'],
      coverage: {
        provider: 'istanbul',
        reporter: ['text', 'json', 'html', 'lcov'],
        reportsDirectory: '../../coverage/dashboard',
        include: ['src/**/*.{ts,tsx}'],
        exclude: [
          'src/**/*.test.{ts,tsx}',
          'src/**/*.spec.{ts,tsx}',
          'src/test/**',
          'src/**/*.d.ts',
          'src/main.tsx',
          'src/env.d.ts',
          'src/routeTree.gen.ts'
        ],
        all: true,
        clean: true
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
});
