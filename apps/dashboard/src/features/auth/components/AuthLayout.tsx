import { useState, useEffect } from "react";
import slider1 from "@mass/shared/assets/auth/slider-1.png";
import slider2 from "@mass/shared/assets/auth/slider-2.png";
import slider3 from "@mass/shared/assets/auth/slider-3.png";

import slider1Card from "@mass/shared/assets/auth/slider-1-card.png";
import slider1Card1 from "@mass/shared/assets/auth/slider-1-card-1.png";
import slider2Card from "@mass/shared/assets/auth/slider-2-card.png";
import slider2Card1 from "@mass/shared/assets/auth/slider-2-card-1.png";
import slider3Card from "@mass/shared/assets/auth/slider-3-card.png";
import slider3Card1 from "@mass/shared/assets/auth/slider-3-card-1.png";
import slider3Card2 from "@mass/shared/assets/auth/slider-3-card-2.png";

import slider1Mobile from "@mass/shared/assets/auth/slider-1-mobile.png";
import slider2Mobile from "@mass/shared/assets/auth/slider-2-mobile.png";
import slider3Mobile from "@mass/shared/assets/auth/slider-3-mobile.png";

interface Props {
  children: React.ReactNode;
}

const slideBackgroundColors = ["bg-[#FDB022]", "bg-[#2E90FA]", "bg-[#EE46BC]"];
const ANIMATION_DURATION = 600;
const SLIDE_INTERVAL = 5000;   
const MOBILE_BREAK = "xl"

export default function AuthLayout({ children }: Props) {
  const [currentSlide, setCurrentSlide] = useState(1);
  const [prevSlide, setPrevSlide] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const desktopSliderImages = [slider1, slider2, slider3];
  const mobileSliderImages = [slider1Mobile, slider2Mobile, slider3Mobile];

  const slideOneCards = [slider1Card, slider1Card1];
  const slideTwoCards = [slider2Card, slider2Card1];
  const slideThreeCards = [slider3Card, slider3Card1, slider3Card2];

  useEffect(() => {
    const timer = setInterval(() => {
      setPrevSlide(currentSlide);
      setIsAnimating(true);
      setCurrentSlide((prev) => (prev + 1) % desktopSliderImages.length);

      const animTimeout = setTimeout(() => {
        setIsAnimating(false);
      }, ANIMATION_DURATION);

      return () => clearTimeout(animTimeout);
    }, SLIDE_INTERVAL);

    return () => clearInterval(timer);
  }, [currentSlide]);

  const floatingAnimations = [
    'animate-float-slow',
    'animate-float-medium',
    'animate-float-fast',
  ];

  const handleSlideChange = (index: number) => {
    if (currentSlide !== index) {
      setPrevSlide(currentSlide);
      setIsAnimating(true);
      setCurrentSlide(index);

      setTimeout(() => {
        setIsAnimating(false);
      }, ANIMATION_DURATION);
    }
  };

  const renderCards = (slideIndex: number, cards: string[], isMobile = false) => {
    if (slideIndex !== currentSlide && slideIndex !== prevSlide) {
      return null;
    }

    const isCurrentSlide = slideIndex === currentSlide;
    const animClass = isCurrentSlide ? 'card-enter' : 'card-exit';

    let positions: { [key: number]: string }[] = [];
    let cardPrefix = '';

    if (isMobile) {
      cardPrefix = `slide${slideIndex + 1}-card-`;
      if (slideIndex === 0) {
        positions = [{ 0: "top-[20%] right-4" }, { 1: "bottom-[25%] left-10" }];
      } else if (slideIndex === 1) {
        positions = [{ 0: "bottom-[30%] left-4" }, { 1: "top-[15%] left-6" }];
      } else {
        positions = [{ 0: "top-[35%] left-[30%]" }, { 1: "bottom-[20%] right-4" }];
      }
    } else {
      cardPrefix = `slide${slideIndex + 1}-desktop-card-`;
      if (slideIndex === 0) {
        positions = [{ 0: "bottom-[20%] right-[5%]" }, { 1: "top-[25%] left-[10%]" }];
      } else if (slideIndex === 1) {
        positions = [{ 0: "bottom-[30%] -left-[5%]" }, { 1: "top-[25%] right-[10%]" }];
      } else {
        positions = [
          { 0: "bottom-[25%] -left-[5%]" },
          { 1: "top-[30%] -left-[2%]" },
          { 2: "top-[20%] right-[10%]" }
        ];
      }
    }

    const cardsToRender = isMobile && slideIndex === 2 ? cards.slice(0, 2) : cards;

    return cardsToRender.map((card, index) => {
      const position = positions[index] ? positions[index][index] : "";
      const baseClasses = `hidden ${MOBILE_BREAK}:block select-none w-auto h-auto transition-all duration-1000 ${floatingAnimations[(index + slideIndex) % floatingAnimations.length]
        } ${animClass} ${isAnimating ? '' : 'animation-delay-' + index * 100}`;

      if (isMobile) {
        return (
          <img
            key={`${cardPrefix}${index}`}
            src={card}
            className={`absolute max-w-[45%] ${baseClasses} ${position}`}
            alt={`Card ${index + 1}`}
            draggable="false"
            style={{
              animationDelay: `${index * 100}ms`,
              zIndex: isCurrentSlide ? 5 : 1
            }}
          />
        );
      } else {
        return (
          <img
            key={`${cardPrefix}${index}`}
            src={card}
            className={`hidden ${MOBILE_BREAK}:block absolute max-w-[35%] ${baseClasses} ${position}`}
            alt={`Card ${index + 1}`}
            draggable="false"
            style={{
              animationDelay: `${index * 100}ms`,
              zIndex: isCurrentSlide ? 5 : 1
            }}
          />
        );
      }
    });
  };

  return (
    <div className={`min-h-screen flex flex-col-reverse ${MOBILE_BREAK}:flex-row mx-auto h-full w-full`}>
      <div className={`flex-1 ${MOBILE_BREAK}:hidden p-5 flex items-center justify-center max-h-[600px]`}>
        <div className="flex relative flex-col items-center justify-center w-full max-w-md mx-auto rounded-2xl overflow-hidden" style={{ aspectRatio: '9/10' }}>
          {mobileSliderImages.map((image, index) => (
            <img
              key={index}
              src={image}
              draggable="false"
              className={`select-none absolute top-0 left-0 w-full h-full object-cover rounded-2xl transition-opacity duration-1000 ${index === currentSlide ? "opacity-100" : "opacity-0"
                }`}
            />
          ))}

          {renderCards(0, slideOneCards, true)}
          {renderCards(1, slideTwoCards, true)}
          {renderCards(2, slideThreeCards, true)}

          <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-10">
            {mobileSliderImages.map((_, index) => (
              <button
                key={index}
                onClick={() => handleSlideChange(index)}
                className={`w-2 h-2 rounded-full transition-all ${index === currentSlide ? "bg-white scale-125" : "bg-white/50"
                  }`}
              />
            ))}
          </div>
        </div>
      </div>

      <div className={`flex flex-col items-center justify-center p-0 ${MOBILE_BREAK}:p-8`}>
        <div className="w-full max-w-md space-y-6">{children}</div>
      </div>

      <div className={`flex-1 hidden ${MOBILE_BREAK}:flex ${MOBILE_BREAK}:items-center ${MOBILE_BREAK}:justify-center p-8`}>
        <div className={`flex relative flex-col items-center justify-center h-full w-full rounded-2xl transition-all duration-${ANIMATION_DURATION} ${slideBackgroundColors[currentSlide]}`}>
          {desktopSliderImages.map((image, index) => (
            <img
              key={index}
              src={image}
              draggable="false"
              className={`select-none absolute top-0 left-0 w-full h-full object-contain rounded-2xl transition-opacity duration-${ANIMATION_DURATION} ${index === currentSlide ? "opacity-100" : "opacity-0"
                }`}
              alt={`Slide ${index + 1}`}
            />
          ))}
          {renderCards(0, slideOneCards)}
          {renderCards(1, slideTwoCards)}
          {renderCards(2, slideThreeCards)}

          <div className="absolute bottom-6 left-0 right-0 flex justify-center gap-2 z-10">
            {desktopSliderImages.map((_, index) => (
              <button
                key={index}
                onClick={() => handleSlideChange(index)}
                className={`w-2 h-2 rounded-full transition-all ${index === currentSlide ? "bg-white scale-125" : "bg-white/50"
                  }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
