import Iconify from "@mass/shared/components/atoms/Iconify";
import { AuthAlert } from "@mass/shared/components/organisms/auth/alert";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { useState } from "react";
import { useTranslation, Trans } from "react-i18next";
import AgreementModal from "./Agreement";
import { LoginFormType } from "../types";
import LogoMark from "@mass/shared/assets/logo-mark.svg";

import {
  globalSettingsKeys,
  useGlobalSettings,
} from "@mass/shared/hooks/use-global-settings";
import api from "@/services/api";

interface LoginFormProps {
  onSubmit: (data: LoginFormType) => Promise<void>;
  error?: string;
  isLoading: boolean;
}

export function LoginForm({ error, isLoading }: LoginFormProps) {
  const { t } = useTranslation("common");
  const { open } = useModal();
  const [kvkkApproved, setKvkkApproved] = useState(false);
  const [sozlesmeApproved, setSozlesmeApproved] = useState(false);

  const { data } = useGlobalSettings(globalSettingsKeys.documents.pdf(), api);

  const handleOpenModal = (type: "kvkk" | "userAgreement") => {
    const url =
      type === "kvkk" ? data?.value?.kvkk?.url : data?.value?.agreement?.url;
    open(AgreementModal, {
      name: `pdf-modal-${type}`,
      size: "large",
      header: {
        title:
          type === "kvkk"
            ? t("auth.register.kvkkTitle", {
                defaultValue: "KVKK Aydınlatma Metni",
              })
            : t("auth.register.userAgreementTitle", {
                defaultValue: "Kullanıcı Sözleşmesi",
              }),
        description:
          type === "kvkk"
            ? t("auth.register.kvkkDescription", {
                defaultValue: "KVKK metnini okuduğunuzu onaylayın.",
              })
            : t("auth.register.userAgreementDescription", {
                defaultValue:
                  "Kullanıcı Sözleşmesi metnini okuduğunuzu onaylayın.",
              }),
      },
      url,
      disableClose: true,
      onApprove: () => {
        if (type === "kvkk") setKvkkApproved(true);
        if (type === "userAgreement") setSozlesmeApproved(true);
      },
    });
  };

  const handleEdevletLogin = async () => {
    window.open("/api/auth/edevlet", "_self");
  };

  return (
    <div className="space-y-6 mt-4 max-w-md mx-auto">
      <img src={LogoMark} alt="logo" className="mb-24" />
      <div className="text-left mb-4">
        <Iconify
          name="mass:login"
          className="size-10 text-primary mx-auto mb-2"
        />
        <h2 className="text-2xl font-bold">{t("auth.login.title")}</h2>
        <p className="text-muted-foreground text-sm">
          {t("auth.login.subtitle")}
        </p>
      </div>

      {error ? (
        <AuthAlert title={t("auth.login.errorTitle")} description={error} />
      ) : null}

      <Button
        type="button"
        variant="destructive"
        className="w-full mt-2 flex items-center gap-2 transition"
        disabled={isLoading}
        onClick={handleEdevletLogin}
      >
        <Iconify name="mass:edevlet" className="size-5" />
        {t("auth.login.eDevletLogin")}
      </Button>

      <div className="text-xs text-muted-foreground text-center mt-4">
        <Trans
          i18nKey="auth.register.readAndApprove"
          ns="common"
          components={{
            kvkk: (
              <button
                className="underline text-primary"
                type="button"
                onClick={() => handleOpenModal("kvkk")}
              />
            ),
            userAgreement: (
              <button
                className="underline text-primary"
                type="button"
                onClick={() => handleOpenModal("userAgreement")}
              />
            ),
          }}
        />
      </div>
      
      <div className="text-xs text-muted-foreground text-center pt-24">
        MASS © Tüm hakları saklıdır. • Uygulama hakkında
      </div>
    </div>
  );
}
