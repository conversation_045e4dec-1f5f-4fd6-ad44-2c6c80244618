import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { authService } from "@/services/api/auth";
import axios from "axios";

export const authKeys = {
  all: ["auth"] as const,
  session: () => [...authKeys.all, "session"] as const,
  user: () => [...authKeys.all, "user"] as const,
};

const logQueryError = (error: unknown) => {
  if (axios.isAxiosError(error)) {
    const errorMessage = error.response?.data?.message || error.message;
    console.error(`Auth API Error: ${errorMessage}`);
    return errorMessage;
  }
  console.error("Unexpected auth error:", error);
  return "An unexpected error occurred";
};

export const useMe = () => {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: () => authService.me(),
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
};

export const useLoginMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) => {
      queryClient.resetQueries();
      return authService.login({ email, password });
    },
    onError: (error) => logQueryError(error),
  });
};

export const useLogoutMutation = () => {
  return useMutation({
    mutationFn: authService.logout,
    onError: (error) => logQueryError(error),
  });
};