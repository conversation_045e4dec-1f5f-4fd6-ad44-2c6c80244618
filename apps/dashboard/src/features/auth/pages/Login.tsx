import type { LoginFormType } from "../types";
import { useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import AuthLayout from "../components/AuthLayout";
import { LoginForm } from "../components/LoginForm";
import { useLoginMutation } from "../hooks/use-auth";

export default function Login() {
  const { t } = useTranslation("common");
  const [error, setError] = useState<string>("");

  const loginMutation = useLoginMutation();
  const navigate = useNavigate();

  const isLoading = loginMutation.isPending;

  const handleLogin = async (data: LoginFormType) => {
    setError("");
    try {
      const response = await loginMutation.mutateAsync({
        email: data.email,
        password: data.password,
      });

      if (response.session) {
        navigate({ to: "/" });
      } else {
        setError(
          response.message ||
            t("auth.login.invalidCredentials", "Invalid email or password")
        );
      }
    } catch (err) {
      setError(
        t(
          "auth.login.errorWhileLoggingIn",
          "An error occurred while logging in"
        )
      );
      console.error("Login error:", err);
    }
  };

  return (
    <AuthLayout>
      <LoginForm onSubmit={handleLogin} error={error} isLoading={isLoading} />
    </AuthLayout>
  );
}
