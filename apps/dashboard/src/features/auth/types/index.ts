export interface LoginFormType {
  email: string;
  password: string;
}

export interface User {
  id: string;
  permissions: Array<{ scope: string }>;
  email: string | null;
  phone: string | null;
  tckn: string | null;
  firstName: string | null;
  lastName: string | null;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (credentials: LoginFormType) => Promise<void>;
  logout: () => Promise<void>;
}