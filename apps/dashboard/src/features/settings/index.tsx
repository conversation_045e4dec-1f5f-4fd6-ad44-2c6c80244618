import { sidebarData } from "@/constants/sidebar-data";
import { useAuthStore } from "@/stores/auth";
import { changeLanguage } from "@/utils/i18n";
import { useDisabled, useIsDisabled } from "@/utils/use-disabled";
import { LanguageSwitcher } from "@mass/shared/components/molecules/language-switcher";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { Input } from "@mass/shared/components/ui/input";
import { Separator } from "@mass/shared/components/ui/separator";
import { Switch } from "@mass/shared/components/ui/switch";
import { Tabs, TabsList, TabsTrigger } from "@mass/shared/components/ui/tabs";
import { useNavigate, useRouter } from "@tanstack/react-router";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { DeleteAccountModal } from "./components/delete-account-modal";
import {
  useDeleteAccount,
  useNotificationSettings,
  useSetNotificationSettings,
} from "./data/queries";
import { globalSettingsKeys } from "@mass/shared/hooks/use-global-settings";

interface DocumentData {
    value: {
        agreement?: { url: string };
        about?: { url: string };
        kvkk?: { url: string };
        manual?: { url: string };
        [key: string]: { url: string } | undefined;
    }
}

export default function Settings() {
  useDisabled(["disabled.settings.view"], "/help-center/faq");
  const { t } = useTranslation("settings");
  const { t: commonT } = useTranslation("common");
  const navigate = useNavigate();
  const router = useRouter();

  const isUserSettingsDisabled = useIsDisabled([
    "disabled.settings.view",
    "disabled.settings.user",
  ]);

  const isNotifSettingsDisabled = useIsDisabled([
    "disabled.settings.view",
    "disabled.settings.notif",
  ]);

  if (isUserSettingsDisabled && isNotifSettingsDisabled) {
    navigate({
      to: "/help-center/$page",
      params: { page: "faq" },
      replace: true
    });
    return null;
  }

  const currentPath = router.state.location.pathname;
  let activeTab = "overview";

  if (currentPath.includes("/notification")) {
    activeTab = "billing";
  }

  if (activeTab === "overview" && isUserSettingsDisabled) {
    navigate({
      to: "/settings/notification",
    });
  } else if (activeTab === "billing" && isNotifSettingsDisabled) {
    navigate({
      to: "/settings",
    });
  }

  const handleTabChange = (value: string) => {
    if (value === "overview") {
      navigate({
        to: "/settings",
        replace: true,
      });
    } else if (value === "billing") {
      navigate({
        to: "/settings/notification",
        replace: true,
      });
    }
  };

  const user = useAuthStore((state) => state.auth.user);

  const { open } = useModal();
  const { mutate: deleteAccount, isPending } = useDeleteAccount();

  const notifSettingsLoaded = useRef(false);
  const { data: oldNotificationSettings, isLoading: isNotifSettingsLoading } =
    useNotificationSettings();
  const { mutate: setNotifSettingsMutation } = useSetNotificationSettings();

  const [notifSettings, setNotifSettings] = useState({
    outage: {
      planned: true,
      unplanned: true,
    },
    warning: {
      unexpectedUsage: true,
      closedTerm: true,
      userLimit: true,
    },
  });

  useEffect(() => {
    if (notifSettingsLoaded.current) return;
    if (isNotifSettingsLoading) return;

    const constructedNotifSettings = notifSettings;

    if (
      oldNotificationSettings?.outage?.planned &&
      oldNotificationSettings?.outage?.planned === "false"
    )
      constructedNotifSettings.outage.planned = false;
    if (
      oldNotificationSettings?.outage?.unplanned &&
      oldNotificationSettings?.outage?.unplanned === "false"
    )
      constructedNotifSettings.outage.unplanned = false;
    if (
      oldNotificationSettings?.warning?.unexpectedUsage &&
      oldNotificationSettings?.warning?.unexpectedUsage === "false"
    )
      constructedNotifSettings.warning.unexpectedUsage = false;
    if (
      oldNotificationSettings?.warning?.closedTerm &&
      oldNotificationSettings?.warning?.closedTerm === "false"
    )
      constructedNotifSettings.warning.closedTerm = false;
    if (
      oldNotificationSettings?.warning?.userLimit &&
      oldNotificationSettings?.warning?.userLimit === "false"
    )
      constructedNotifSettings.warning.userLimit = false;

    setNotifSettings(constructedNotifSettings);

    notifSettingsLoaded.current = true;
  }, [isNotifSettingsLoading]);

  const handleDeleteAccountClick = () => {
    open(DeleteAccountModal, {
      onConfirm: () => deleteAccount(),
    });
  };

  const [documents, setDocuments] = useState<DocumentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
      const fetchDocuments = async () => {
          try {
              setIsLoading(true);

              const endpoint = `/api/setting/global/${globalSettingsKeys.documents.pdf()}`;
              const response = await fetch(endpoint);

              if (!response.ok) {
                  console.warn(`API returned ${response.status}: ${response.statusText}`);
                  throw new Error(`API returned ${response.status}: ${response.statusText}`);
              }

              const contentType = response.headers.get('content-type');
              if (!contentType || !contentType.includes('application/json')) {
                  console.warn('Expected JSON response but got:', contentType);
                  throw new Error(`Expected JSON response but got ${contentType}`);
              }

              const data = await response.json();
              setDocuments(data.value);
          } finally {
              setIsLoading(false);
          }
      };

      fetchDocuments();
  }, []);

  if (!documents) {
    return null;
  }

  const handleOpenPdf = (url: string) => {
      window.open(url, "_blank");
  };

  return (
    <>
      <Header
        title={t("settings")}
        sidebarData={sidebarData}
        description="Manage and customize your application settings to suit your preferences."
        className="mb-2 md:mb-0 w-full"
        subContent={
          <Tabs
            value={activeTab}
            className="mt-2 w-full"
            onValueChange={handleTabChange}
          >
            <TabsList className="border-b border-border/50 !bg-transparent w-full overflow-x-auto">
              {isUserSettingsDisabled ? null : (
                <TabsTrigger
                  value="overview"
                  className="rounded-none border-b pb-3 border-border/50 !shadow-none !bg-transparent data-[state=active]:border-primary data-[state=active]:border-b-2 data-[state=active]:text-primary flex-1 sm:flex-initial whitespace-nowrap"
                >
                  {t("settings")}
                </TabsTrigger>
              )}
              {isNotifSettingsDisabled ? null : (
                <TabsTrigger
                  value="billing"
                  className="rounded-none border-b pb-3 border-border/50 !shadow-none !bg-transparent data-[state=active]:border-primary data-[state=active]:border-b-2 data-[state=active]:text-primary flex-1 sm:flex-initial whitespace-nowrap"
                >
                  {t("notifications_tab")}
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>
        }
      />

      <Main fixed className="justify-start px-4 sm:px-6 mx-auto">
        {activeTab === "overview" ? (
          <>
            <div className="w-full py-4">
              <h2 className="text-lg md:text-xl font-medium">{t("personal_info")}</h2>
            </div>
            <Separator />
            <div className="w-full py-4 flex flex-col md:flex-row items-start md:items-center">
              <div className="font-normal text-sm flex-1 mb-2 md:mb-0 min-w-[100px]">{t("name")}</div>
              <div className="flex gap-2 flex-1 w-full flex-col sm:flex-row">
                <Input value={user?.firstName ?? "-"} disabled className="w-full text-sm" />
                <Input value={user?.lastName ?? "-"} disabled className="w-full text-sm" />
              </div>
            </div>
            <Separator />
            <div className="w-full py-4 flex flex-col md:flex-row items-start md:items-center">
              <div className="font-normal text-sm flex-1 mb-2 md:mb-0 min-w-[100px]">{t("TCKN")}</div>
              <div className="flex gap-2 flex-1 w-full">
                <Input value={user?.tckn ?? "-"} disabled className="w-full text-sm" />
              </div>
            </div>
            <Separator />
            <div className="w-full py-4 flex flex-col md:flex-row items-start md:items-center">
              <div className="font-normal text-sm flex-1 mb-2 md:mb-0 min-w-[100px]">{t("language")}</div>
              <div className="flex gap-2 flex-1 w-full">
                <LanguageSwitcher changeLanguage={changeLanguage} />
              </div>
            </div>
            <Separator />
            <div className="w-full py-4 flex flex-col md:flex-row items-start md:items-center">
              <div className="font-normal text-sm flex-1 mb-2 md:mb-0 min-w-[100px]">{t("documents")}</div>
              <div className="flex gap-2 flex-1 w-full">
                <Button variant="outline" className="mt-4" onClick={() => handleOpenPdf((documents as any)?.kvkk?.url ?? "")}>
                  {commonT("sidebar.items.documents.kvkk")}
                </Button>

                <Button variant="outline" className="mt-4" onClick={() => handleOpenPdf((documents as any)?.agreement?.url ?? "")}>
                  {commonT("sidebar.items.documents.agreement")}
                </Button>
              </div>
            </div>
            <Separator />
            
            <div className="w-full rounded-lg mt-8 md:mt-12 p-4 sm:p-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-red-50 border border-red-200 shadow-sm">
              <div className="flex flex-col gap-1">
                <h2 className="font-medium md:text-lg">{t("delete_account")}</h2>
                <p className="text-sm text-gray-600 mt-1 max-w-md">
                  {t("delete_account_desc")}
                </p>
              </div>
              <Button
                variant="destructive"
                className="sm:mt-4 w-full sm:w-auto text-sm transition-all hover:shadow-md"
                onClick={handleDeleteAccountClick}
                disabled={isPending}
              >
                {t("delete_account")}
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="w-full py-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
              <h2 className="text-lg md:text-xl font-medium">
                {t("notification_options")}
              </h2>
              <Button
                onClick={() => {
                  setNotifSettings({
                    outage: {
                      planned: true,
                      unplanned: true,
                    },
                    warning: {
                      unexpectedUsage: true,
                      closedTerm: true,
                      userLimit: true,
                    },
                  });
                }}
                className="w-full sm:w-auto text-sm transition-colors"
              >
                {t("activate_all")}
              </Button>
            </div>
            <Separator />
            <div className="w-full py-4 flex flex-col lg:flex-row items-start gap-4 lg:gap-6">
              <div className="font-normal text-sm flex-1 w-full lg:w-auto lg:max-w-[40%]">
                <div className="font-medium text-base md:text-lg">{t("outage_notifs_title")}</div>
                <div className="mt-1 text-gray-600">{t("outage_notifs_desc")}</div>
              </div>
              <div className="flex gap-3 flex-1 flex-col text-sm w-full lg:w-auto bg-white/50 p-3 rounded-lg border border-gray-100 shadow-sm">
                <label className="flex items-center gap-3 hover:bg-gray-50 p-2 rounded-md -ml-2 transition-colors">
                  <Switch
                    checked={notifSettings.outage.planned}
                    onCheckedChange={(value) =>
                      setNotifSettings((old) => ({
                        ...old,
                        outage: { ...old.outage, planned: value },
                      }))
                    }
                  />{" "}
                  {t("planned_outage_notifs")}
                </label>
                <label className="flex items-center gap-3 hover:bg-gray-50 p-2 rounded-md -ml-2 transition-colors">
                  <Switch
                    checked={notifSettings.outage.unplanned}
                    onCheckedChange={(value) =>
                      setNotifSettings((old) => ({
                        ...old,
                        outage: { ...old.outage, unplanned: value },
                      }))
                    }
                  />{" "}
                  {t("unplanned_outage_notifs")}
                </label>
              </div>
            </div>
            <Separator />
            <div className="w-full py-4 flex flex-col lg:flex-row items-start gap-4 lg:gap-6">
              <div className="font-normal text-sm flex-1 w-full lg:w-auto lg:max-w-[40%]">
                <div className="font-medium text-base md:text-lg">{t("warnings_title")}</div>
                <div className="mt-1 text-gray-600">{t("warnings_desc")}</div>
              </div>
              <div className="flex gap-3 flex-1 flex-col text-sm w-full lg:w-auto bg-white/50 p-3 rounded-lg border border-gray-100 shadow-sm">
                <label className="flex items-center gap-3 hover:bg-gray-50 p-2 rounded-md -ml-2 transition-colors">
                  <Switch
                    checked={notifSettings.warning.unexpectedUsage}
                    onCheckedChange={(value) =>
                      setNotifSettings((old) => ({
                        ...old,
                        warning: { ...old.warning, unexpectedUsage: value },
                      }))
                    }
                  />{" "}
                  {t("unexpected_usage_warning")}
                </label>
                <label className="flex items-center gap-3 hover:bg-gray-50 p-2 rounded-md -ml-2 transition-colors">
                  <Switch
                    checked={notifSettings.warning.closedTerm}
                    onCheckedChange={(value) =>
                      setNotifSettings((old) => ({
                        ...old,
                        warning: { ...old.warning, closedTerm: value },
                      }))
                    }
                  />{" "}
                  {t("closed_term_warning")}
                </label>
                <label className="flex items-center gap-3 hover:bg-gray-50 p-2 rounded-md -ml-2 transition-colors">
                  <Switch
                    checked={notifSettings.warning.userLimit}
                    onCheckedChange={(value) =>
                      setNotifSettings((old) => ({
                        ...old,
                        warning: { ...old.warning, userLimit: value },
                      }))
                    }
                  />{" "}
                  {t("user_limit_warning")}
                </label>
              </div>
            </div>
            <div className="w-full flex justify-end mt-6 md:mt-8">
              <Button
                onClick={() => {
                  setNotifSettingsMutation(notifSettings);
                }}
                className="w-full sm:w-auto text-sm transition-all hover:shadow-md"
              >
                {t("save_changes")}
              </Button>
            </div>
          </>
        )}
      </Main>
    </>
  );
}
