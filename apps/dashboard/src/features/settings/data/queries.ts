import { settingsService } from "@/services/api/settings";
import { authService } from "@/services/api/auth";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "@/stores/auth";

export const settingsKeys = {
  notification: () => ["settings", "notification"],
};

export const useNotificationSettings = () => {
  return useQuery({
    queryKey: settingsKeys.notification(),
    queryFn: () => settingsService.getNotificationSettings(),
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
};

export const useSetNotificationSettings = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation("settings");

  return useMutation({
    mutationFn: (data: any) => settingsService.setNotificationSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: settingsKeys.notification() });
      toast.success(t("notification_settings_saved"));
    },
    onError: (error) => {
      console.error("Error saving notification settings:", error);
      toast.error(t("notification_settings_error"));
    }
  });
};

export const useDeleteAccount = () => {
  const { t } = useTranslation();
  const reset = useAuthStore((state) => state.auth.reset);
  
  return useMutation({
    mutationFn: () => authService.deleteAccount(),
    onSuccess: () => {
      toast.success(t("settings:delete_account_success"));
      reset();

      window.location.href = "/login";
    },
    onError: (error) => {
      console.error("Error deleting account:", error);
      toast.error(t("settings:delete_account_error"));
    }
  });
};
