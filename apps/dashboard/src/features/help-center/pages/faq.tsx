import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@mass/shared/components/ui/accordion";
import {
  useGlobalSettings,
  globalSettingsKeys,
} from "@mass/shared/hooks/use-global-settings";
import { useTranslation } from "react-i18next";
import { Skeleton } from "@mass/shared/components/ui/skeleton";
import api from "@/services/api";

export default function Faq() {
  const { i18n } = useTranslation("complaints");
  const { data, isLoading } = useGlobalSettings(
    globalSettingsKeys.documents.faq(),
    api
  );

  console.log("data", data);

  if (isLoading) {
    return (
      <article className="prose prose-xs text-left w-full space-y-4">
        {Array.from({ length: 3 }).map((_, idx) => (
          <div key={idx} className="space-y-2">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-5/6" />
          </div>
        ))}
      </article>
    );
  }

  const faqItems = data?.value || [];

  return (
    <article className="prose prose-xs text-left w-full -mt-6">
      <Accordion type="single" collapsible className="w-full">
        {faqItems
          .slice()
          .reverse()
          .map((item, index) => (
            <AccordionItem key={`item-${index}`} value={`item-${index}`}>
              <AccordionTrigger className="text-lg">
                {
                  item.question[
                    i18n.language.toUpperCase() as keyof typeof item.question
                  ]
                }
              </AccordionTrigger>
              <AccordionContent>
                {
                  item.answer[
                    i18n.language.toUpperCase() as keyof typeof item.answer
                  ]
                }
              </AccordionContent>
            </AccordionItem>
          ))}
      </Accordion>
    </article>
  );
}
