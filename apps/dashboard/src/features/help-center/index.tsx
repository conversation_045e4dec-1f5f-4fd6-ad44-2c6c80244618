import { sidebarData } from '@/constants/sidebar-data'
import { Header } from '@mass/shared/components/organisms/layout/header'
import { Main } from '@mass/shared/components/organisms/layout/main'
import { useMatches, useNavigate } from '@tanstack/react-router'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

type Tab = 'use-cases' | 'faq' | 'about'

export default function HelpCenterLayout({ children }: { children?: React.ReactNode }) {
    const navigate = useNavigate()
    const matches = useMatches()
    const currentPath = matches[matches.length - 1]?.pathname || ''
    const pathSegments = currentPath.split('/')
    const activeTab = (pathSegments[pathSegments.length - 1] || 'use-cases') as Tab
    const { t } = useTranslation('common')

    useEffect(() => {
        if (!['use-cases', 'faq', 'about'].includes(activeTab)) {
            navigate({
                to: '/help-center/$page',
                params: { page: 'use-cases' }
            })
        }
    }, [activeTab, navigate])

    return (
        <>
            <Header sidebarData={sidebarData} title={t('help_center.' + activeTab)} description="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Maecenas convallis posuere volutpat" />
            <Main className="justify-start">
                <div className="flex flex-col items-start mr-auto gap-5 w-full">
                    {children}
                </div>
            </Main>
        </>
    )
}
