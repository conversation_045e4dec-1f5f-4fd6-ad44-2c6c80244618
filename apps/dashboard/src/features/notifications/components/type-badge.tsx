import React from "react";
import { useTranslation } from "react-i18next";

interface TypeBadgeProps {
  type: string;
  subType: string;
}

const TypeBadge: React.FC<TypeBadgeProps> = ({ type, subType }) => {
  const { t } = useTranslation("common");
  const baseClasses = "px-2 py-1 text-xs font-medium rounded-full capitalize";
  let typeClasses = "border";

  if (type.includes("warning")) {
    typeClasses = "bg-red-50 border border-red-200 text-red-800";
  }

  const translatedType = t(`notifications.types.${subType}.type`);

  return (
    <span className={`${baseClasses} ${typeClasses}`}>{translatedType}</span>
  );
};

export default TypeBadge;
