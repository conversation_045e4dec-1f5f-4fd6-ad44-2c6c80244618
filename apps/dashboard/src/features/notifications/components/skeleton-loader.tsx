import { Skeleton } from '@mass/shared/components/ui/skeleton'

export function NotificationsTableSkeleton() {
    return (
        <div className="w-full space-y-3 h-full">
            <div className="flex items-center justify-between py-4">
                <Skeleton className="h-8 w-64" />
                <div className="flex gap-2">
                    <Skeleton className="h-10 w-40" />
                    <Skeleton className="h-10 w-24" />
                </div>
            </div>

            <div className="rounded-xl border border-gray-100">
                <div className="border-b border-gray-100 px-4 py-2 grid grid-cols-6 items-center gap-4">
                    <div className="flex gap-2 w-full col-span-2">
                        <Skeleton className="h-5 w-5" />
                        <Skeleton className="h-5 w-32" />
                    </div>
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-5 w-24" />
                </div>

                {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="px-4 py-3 w-full grid grid-cols-6 items-center gap-4 border-b border-gray-100 last:border-b-0">
                        <div className="flex gap-2 w-full col-span-2">
                            <Skeleton className="h-4 w-4" />
                            <div className="flex flex-col gap-1">
                                <Skeleton className="h-5 w-32" />
                                <Skeleton className="h-4 w-48" />
                            </div>
                        </div>
                        <div>
                            <Skeleton className="h-5 w-24 rounded-full" />
                        </div>
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-8 w-8 rounded-md ml-auto" />
                    </div>
                ))}
            </div>
        </div>
    )
}
