import { useNotificationCategories } from "../data/queries";

export const useNotificationTypes = () => {
  const { data: categories = {} } = useNotificationCategories();

  const allSubCategories: any[] = [];

  Object.entries(categories).forEach(([categoryKey, categoryValue]: [string, any]) => {
    if (categoryValue.subcategories && categoryValue.deleted !== "true") {
      Object.entries(categoryValue.subcategories).forEach(([subKey, subValue]: [string, any]) => {
        if(!allSubCategories.includes(subKey) && subValue.deleted !== "true") {
          allSubCategories.push({
            id: subKey,
            label: subValue.label,
          });
        }
      });
    }
  });

  return (allSubCategories || []) as { id: string; label: { TR: string; EN: string } }[];
};
