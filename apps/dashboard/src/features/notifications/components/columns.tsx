import { useMarkNotificationAsRead } from "@/features/notifications/data/queries";
import { DetailSubscription } from "@/features/subscriptions/pages/detail";
import Iconify from "@mass/shared/components/atoms/Iconify";
import Action from "@mass/shared/components/organisms/table/action";
import type { ColumnMeta } from "@mass/shared/components/organisms/table/index";
import { SortHeader } from "@mass/shared/components/organisms/table/SortHeader";
import { DropdownMenuItem } from "@mass/shared/components/ui/dropdown-menu";
import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import * as Locale from "date-fns/locale";
import { useTranslation } from "react-i18next";
``
export interface AppNotification {
  id: string;
  createdAt: string;
  textContent: {
    TR: string;
    EN: string;
  };
  title: {
    TR: string;
    EN: string;
  };
  read: boolean;
  regionId: string;
  subscriptionId: string;
  subscription: DetailSubscription;
  type: string;
  subtype: 'notification' | 'unexpected-usage-warning' | 'closed-term-warning' | 'user-limit-warning' | 'complaint-update' | 'planned-outage' | 'unplanned-outage';
  status: "UNREAD" | "READ" | "ARCHIVED";
}

const truncate = (text: string, length = 90) => {
  return text.length > length - 1 ? text.slice(0, length - 1) + "…" : text;
};
export const columns: ColumnDef<AppNotification, unknown>[] = [
  {
    id: "id",
    accessorKey: "notificationInfo",
    header: () => {
      const { t } = useTranslation("notifications");
      return (
        <div className="flex items-center gap-3 pl-2">
          {t("column_notification_info")}
        </div>
      );
    },
    cell: ({ row }) => {
      const { i18n } = useTranslation("notifications");
      const currentLang = i18n.language.toUpperCase() as "EN" | "TR";
      const isUnread = row.original.status === "UNREAD";
      const lang = row.original.textContent[currentLang] ? currentLang : "EN";

      return (
        <div className="flex items-start gap-3 pl-2">
          <div className="flex gap-1">
            <span className={`text-gray-700 flex items-center ${isUnread ? "font-bold" : "font-medium"}`}>
              {isUnread ? (
                <span className="w-2 h-2 inline-block bg-primary rounded-full mr-1.5 animate-pulse" />
              ) : null}
              {(row.original as any).title[lang]}
            </span>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "subscriptionId",
    enableSorting: true,
    header: () => {
      const { t } = useTranslation("subscriptions");
      return t("subscription");
    },
    cell: ({ row }) => {
      return <span>{row.original.subscription?.name ?? "-"}</span>;
    },
  },
  {
    accessorKey: "type",
    enableSorting: true,
    header: ({ table }) => {
      const { t } = table.options.meta as ColumnMeta;
      return t("column_notification_type");
    },
    cell: ({ row }) => {
      const { i18n } = useTranslation("notifications");
      const currentLang = i18n.language.toUpperCase() as "EN" | "TR";
      const lang = row.original.textContent[currentLang] ? currentLang : "EN";

      return (
        <span
          className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${row.original.type === "warning" ? "bg-red-50 border border-red-200 text-red-800" : "border"}`}
        >
          {(row.original as any).category[lang]}
        </span>
      );
    },
  },
  {
    accessorKey: "createdAt",
    enableSorting: true,
    header: ({ column, table }) => {
      const { t } = table.options.meta as ColumnMeta;
      return <SortHeader column={column} title={t("column_send_date")} />;
    },
    cell: ({ row }) => {
      const { i18n } = useTranslation();
      const locale = Locale[i18n.language as "tr"];

      const dateValue = row.getValue("createdAt") as string;
      if (!dateValue) return null;

      return (
        <div className="text-sm">
          {format(new Date(dateValue), "PPP", { locale })}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row, table }) => {
      const { t } = table.options.meta as ColumnMeta;
      const { mutate: markAsRead } = useMarkNotificationAsRead();

      const handleMarkAsRead = (e: React.MouseEvent) => {
        e.stopPropagation();
        markAsRead({ id: row.id, tag: "READ" });
      };

      const handleArchive = (e: React.MouseEvent) => {
        e.stopPropagation();
        markAsRead({ id: row.id, tag: "ARCHIVED" });
      };

      // const handleMarkAsUnread = (e: React.MouseEvent) => {
      //   e.stopPropagation();
      //   markAsRead({ id: row.id, tag: "UNREAD" });
      // };

      return (
        <div className="flex w-full justify-end items-center">
          {row.original.status !== "ARCHIVED" && <Action
            action={
              <div
                className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 cursor-pointer"
                onClick={(e) => e.stopPropagation()}
              >
                <Iconify name="untitled:dots-vertical" className="size-4" />
              </div>
            }
          >
            {/* {row.original.status === "ARCHIVED" ? null : (
              <DropdownMenuItem
                className="flex text-xs font-medium items-center justify-between"
                onClick={
                  row.original.status === "UNREAD"
                    ? handleMarkAsRead
                    : handleMarkAsUnread
                }
              >
                {t(
                  row.original.status === "UNREAD"
                    ? "mark_as_read"
                    : "mark_as_unread"
                )}
                <Iconify
                  name={
                    row.original.status === "UNREAD"
                      ? "untitled:check"
                      : "untitled:flip-backward"
                  }
                  className="size-2"
                />
              </DropdownMenuItem>
            )} */}
            <DropdownMenuItem
              className="flex text-xs font-medium items-center justify-between"
              onClick={handleArchive}
            >
              {t("mark_as_archived")}
              <Iconify name="untitled:archive" className="size-2" />
            </DropdownMenuItem>
          </Action>}
        </div>
      );
    },
  },
];
