import { useSidebar } from "@mass/shared/components/ui/sidebar";
import { useNotificationCount } from "../data/queries";
import { Badge } from "@mass/shared/components/ui/badge";

export const SidebarNumber = () => {
  const notificationCount = useNotificationCount(false, false);
  const { state } = useSidebar();
  const count = notificationCount.data;
  
  if (count === 0 || count === undefined) return null;

  if (state === "collapsed") {
    return (
      <div className="absolute top-1/2 -translate-y-1/2 right-0 -mr-2">
        <Badge className="bg-primary/10 text-primary border-none text-xs h-4 w-4 p-2.5 flex items-center justify-center" variant="outline">
          {count}
        </Badge>
      </div>
    );
  }
  
  return count;
};
