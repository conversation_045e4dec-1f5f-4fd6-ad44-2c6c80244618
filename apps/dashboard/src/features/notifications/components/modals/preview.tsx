import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { useTranslation } from "react-i18next";

interface Props extends ModalProps {
  onHide: () => void;
  activeTab: "subscriptions" | "facilities";
}

export default function Preview({
  onHide,
  title,
  description,
}: Props & { title?: string; description?: string }) {
  const { t } = useTranslation("notifications");

  return (
    <div className="pb-6 px-6 flex flex-col gap-5">
      <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-gray-900 font-text-sm-regular">
        <div className="self-stretch relative leading-[28px] font-semibold">
          {title}
        </div>
        <div className="self-stretch relative text-sm leading-[20px] text-gray-600">
          {description}
        </div>
      </div>
      <div className="w-full flex gap-2 items-center">
        <Button variant="outline" onClick={onHide} className="w-full">
          {t("close")}
        </Button>
      </div>
    </div>
  );
}
