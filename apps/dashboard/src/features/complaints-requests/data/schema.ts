import { z } from "zod";

export enum ApplicationType {
  COMPLAINT = "COMPLAINT",
  REQUEST = "REQUEST",
}

export enum ApplicationCategory {
  ElectricityFault = "ElectricityFault",
  MeterIssues = "MeterIssues",
  DisconnectionNotice = "DisconnectionNotice",
  IllegalUsage = "IllegalUsage",
  MASSDataIssues = "MASSDataIssues",
}

export const newApplicationSchema = z.object({
  subscriptionId: z.string().min(1, "complaints:errors.subscription_id_required"),
  applicationType: z.string().min(1, "complaints:errors.application_type_required"),
  applicationCategory: z.string().min(1, "complaints:errors.application_category_required"),
  applicationDescription: z
    .string()
    .min(1, "complaints:errors.application_description_required")
    .max(2000, "complaints:errors.application_description_too_long"),
  file: z.any().optional(),
});

export const applicationViewModeSchema = newApplicationSchema
  .omit({ file: true })
  .extend({
    response: z.string().optional(),
    distributionCompany: z.string().optional(),
  })
  .readonly();

export type ComplaintsSchema = z.infer<typeof newApplicationSchema> & {
  file?: string | string[] | File | File[];
  response?: string;
  distributionCompany?: string;
};

export type ComplaintsViewModeSchema = z.infer<
  typeof applicationViewModeSchema
>;
