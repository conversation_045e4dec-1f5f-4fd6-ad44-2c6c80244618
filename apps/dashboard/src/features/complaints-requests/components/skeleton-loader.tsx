import { <PERSON>, CardContent, CardHeader } from '@mass/shared/components/ui/card'
import { Skeleton } from '@mass/shared/components/ui/skeleton'

export function ComplaintsTableSkeleton() {
    return (
        <div className="w-full space-y-3 h-full">
            <div className="flex items-center justify-between py-4">
                <div className="flex gap-2">
                    <Skeleton className="h-8 w-40" />
                    <Skeleton className="h-8 w-24" />
                </div>

                <div className="flex gap-2">
                    <Skeleton className="h-8 w-28" />
                    <Skeleton className="h-8 w-24" />
                    <Skeleton className="h-8 w-28" />
                </div>
            </div>

            <div className="rounded-xl border border-gray-100">
                <div className="border-b border-gray-100 px-4 py-2 grid grid-cols-6 items-center gap-4">
                    <div className="flex gap-2 w-full col-span-2">
                        <Skeleton className="h-5 w-5" />
                        <Skeleton className="h-5 w-32" />
                    </div>
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-5 w-24" />
                </div>

                {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="px-4 py-3 w-full grid grid-cols-6 items-center gap-4 border-b border-gray-100 last:border-b-0">
                        <div className="flex gap-2 w-full col-span-2">
                            <Skeleton className="h-4 w-4" />
                            <div className="flex flex-col gap-1">
                                <Skeleton className="h-5 w-32" />
                                <Skeleton className="h-4 w-48" />
                            </div>
                        </div>
                        <div>
                            <Skeleton className="h-5 w-24 rounded-full" />
                        </div>
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-8 w-8 rounded-md ml-auto" />
                    </div>
                ))}
            </div>
        </div>
    )
}

export function ComplaintFormSkeleton() {
    return (
        <div className="space-y-3">
            {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />

                    <Skeleton className="h-10 w-full" />
                </div>
            ))}

            <Skeleton className="h-10 w-full mt-6" />
        </div>
    )
}

export function ComplaintsDetailSkeleton() {
    return (
        <Card className="!px-0 rounded-none shadow-none w-full">
            <CardHeader className="flex flex-row items-center space-y-0 justify-end gap-3">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-8 w-14" />
                <Skeleton className="h-8 w-20" />
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Invoice/Contract Information */}
                <div className="w-full flex flex-col sm:flex-row sm:justify-between gap-2 border-b border-gray-200 pb-4">
                    <div className="w-1/2 flex flex-col gap-2">
                        <div className="flex gap-2 items-center">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-4 w-28" />
                        </div>
                        <div className="flex gap-2 items-center">
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-4 w-20" />
                        </div>
                    </div>
                    <div className="w-1/2 flex flex-col gap-2">
                        <div className="flex gap-2 items-center">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-4 w-28" />
                        </div>
                        <div className="flex gap-2 items-center">
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-4 w-20" />
                        </div>
                    </div>
                </div>

                <div>
                    <Skeleton className="h-6 w-40 mb-2" />
                    <div className="grid gap-2 md:grid-cols-2">
                        {Array.from({ length: 5 }).map((_, i) => (
                            <div key={i} className="flex flex-col gap-2">
                                <Skeleton className="h-4 w-28" />
                                <Skeleton className="h-4 w-40" />
                            </div>
                        ))}
                    </div>
                </div>

                <div>
                    <Skeleton className="h-6 w-40 mb-2" />
                    <div className="w-full">
                        <div className="flex gap-4 py-3 border-b border-gray-100">
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-4 w-28" />
                        </div>
                        <div className="flex gap-4 py-4">
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-4 w-28" />
                            <Skeleton className="h-4 w-28" />
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    )
}
