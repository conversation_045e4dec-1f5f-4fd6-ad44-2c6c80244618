import Alert from "@mass/shared/components/molecules/alert";
import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import AutoForm from "@mass/shared/components/ui/auto-form/index";
import { Button } from "@mass/shared/components/ui/button";
import { Separator } from "@mass/shared/components/ui/separator";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { extensions as mimeExtensions } from "mime-types";
import { toast } from "sonner";
import { subscriptionService } from "../../../../services/api/subscriptions";
import {
  useRegions,
  useSubscriptions,
} from "../../../subscriptions/data/queries";
import {
  useComplaintDetails,
  useComplaintCategories,
  useCreateComplaint,
  useDocuments,
  useMarkDocumentAsDone,
  useUploadDocument,
} from "../../data/queries";
import type { ComplaintsSchema } from "../../data/schema";
import {
  applicationViewModeSchema,
  newApplicationSchema,
} from "../../data/schema";
import { DocIcon } from "../document";
import { ComplaintFormSkeleton } from "../skeleton-loader";
import { useGlobalSettings } from "@mass/shared/hooks/use-global-settings";
import api from "@/services/api";

type ExtendedComplaintsSchema = ComplaintsSchema & {
  status?: string;
  distributionCompany?: string;
  subscriptionName?: string;
  response?: string;
};

interface CategoryLabel {
  [key: string]: string;
}

interface CategorySubcategory {
  label: CategoryLabel;
}

interface Category {
  label: CategoryLabel;
  subcategories?: Record<string, CategorySubcategory>;
}

interface Props extends ModalProps {
  onHide: () => void;
  viewMode?: boolean;
  data?: { id: string; status?: string };
}

export default function ComplaintApplicationModal({
  onHide,
  viewMode,
  data: modalData,
  name,
}: Props) {
  const { t } = useTranslation("subscriptions");
  const { t: tComplaints } = useTranslation("complaints");
  const [isValueChange, setIsValueChange] = useState(false);
  const [formValues, setFormValues] = useState<Partial<ComplaintsSchema>>({});
  const [uploadedFileIds, setUploadedFileIds] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [complaintDetails, setComplaintDetails] =
    useState<ExtendedComplaintsSchema | null>(null);
  const { data: categories } = useComplaintCategories(false);
  const { data: subscriptions, isLoading: subscriptionsLoading } =
    useSubscriptions({ pageSize: 1 }); // just to get the count
  const { patch } = useModal();

  const { data: regions } = useRegions();

  const {
    data: complaintDetailsData,
    isLoading: isLoadingDetails,
    error: detailsError,
  } = useComplaintDetails(viewMode && modalData?.id ? modalData.id : "");

  const uploadDocument = useUploadDocument();
  const createComplaint = useCreateComplaint();
  const markDocumentAsDone = useMarkDocumentAsDone();

  const { i18n } = useTranslation();

  const { data: documentUploadLimits } = useGlobalSettings(
    "documents.upload.limits",
    api,
  ) as any;

  const allowedMimeTypes =
    documentUploadLimits &&
    typeof documentUploadLimits === "object" &&
    "value" in documentUploadLimits &&
    documentUploadLimits.value &&
    typeof documentUploadLimits.value === "object" &&
    "mimetypes" in documentUploadLimits.value &&
    Array.isArray(
      (documentUploadLimits.value as { mimetypes?: unknown }).mimetypes,
    )
      ? (documentUploadLimits.value as { mimetypes: string[] }).mimetypes
      : [];

  const acceptString = [
    ...new Set(
      allowedMimeTypes
        .flatMap(
          (type: string) =>
            mimeExtensions[type as keyof typeof mimeExtensions] ?? [],
        )
        .filter(Boolean),
    ),
  ]
    .map((e) => `.${e}`)
    .join(",");

  useEffect(() => {
    if (!viewMode) {
      patch({
        name,
        confirmClose: true,
        hasUnsavedChanges: true,
        confirmationProps: {
          title: t("confirmation.modal_close.title", {
            ns: "common",
          }),
          description: t("confirmation.modal_close.description", {
            ns: "common",
          }),
          confirmText: t("confirmation.modal_close.confirm", {
            ns: "common",
          }),
          cancelText: t("confirmation.modal_close.cancel", {
            ns: "common",
          }),
          iconName: "untitled:alert-triangle",
        },
      });
    }
  }, []);

  useEffect(() => {
    if (viewMode && complaintDetailsData && !complaintDetails) {
      const fetchSubscriptionDetails = async () => {
        try {
          const foundSub = await subscriptionService.getById(
            complaintDetailsData.subscriptionId,
          );

          const regionName =
            (regions ?? []).find((region) => region.id === foundSub?.regionId)
              ?.name || tComplaints("unknown");

          const subscriptionName =
            foundSub?.name || tComplaints("subscription_not_found");

          const transformedDetails: ExtendedComplaintsSchema = {
            applicationDescription: complaintDetailsData.body || "",
            applicationCategory: complaintDetailsData.subtype || "",
            applicationType: complaintDetailsData.type || "",
            subscriptionId: complaintDetailsData.subscriptionId || "",
            file: complaintDetailsData.documents || [],
            response: complaintDetailsData.response || "",
            subscriptionName,
            status: modalData?.status,
            distributionCompany: regionName,
          };

          setComplaintDetails(transformedDetails);
        } catch (error) {
          toast.error(tComplaints("error.load_complaint_failed"));
        }
      };

      fetchSubscriptionDetails();
    }
  }, [
    complaintDetailsData,
    viewMode,
    modalData,
    regions,
    tComplaints,
    complaintDetails,
  ]);

  useEffect(() => {
    if (detailsError) {
      const error = detailsError as Error & { status?: number };
      if (error.status === 404) {
        toast.error(tComplaints("error.complaint_not_found"));
      } else {
        toast.error(tComplaints("error.load_complaint_failed"));
      }
    }
  }, [detailsError, tComplaints]);

  const handleFileUpload = async (file: File) => {
    if (!file) return null;

    if (!allowedMimeTypes.includes(file.type)) {
      toast.error(
        tComplaints("error.unsupported_file_type", {
          filename: file.name,
        }),
      );
      return null;
    }

    try {
      setIsUploading(true);
      const result = await uploadDocument.mutateAsync(file);
      const uploadUrl = result.url;
      const fileId = result.id || (result.data && result.data.id);

      if (!uploadUrl || !fileId) {
        toast.error(tComplaints("error.upload_missing_data"));
        setIsUploading(false);
        return null;
      }

      const loadingToast = toast.loading(
        tComplaints("toast.uploading", { filename: file.name }),
      );

      const uploadResponse = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error("Failed to upload file binary to the provided URL");
      }

      toast.dismiss(loadingToast);
      toast.success(
        tComplaints("toast.upload_success", { filename: file.name }),
      );

      setUploadedFileIds((prev) => [...prev, fileId]);
      setIsUploading(false);
      return fileId;
    } catch (error) {
      toast.error(tComplaints("toast.upload_failed"));
      setIsUploading(false);
      return null;
    }
  };

  const totalSubscriptions =
    subscriptions?.pages.reduce((_total, page) => page.totalElements, 0) || 0;

  const initialValues: Partial<ComplaintsSchema> = viewMode
    ? (complaintDetails ?? {})
    : totalSubscriptions === 1
      ? { subscriptionId: subscriptions?.pages[0].content[0].id }
      : {};

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (values: ComplaintsSchema) => {
    if (createComplaint.isPending || isSubmitting) return;
    try {
      setIsSubmitting(true);
      let submissionValues = { ...values };
      let fileIds: string[] = [];

      if (Array.isArray(submissionValues.file)) {
        if (
          submissionValues.file.length > 0 &&
          typeof submissionValues.file[0] === "string"
        ) {
          fileIds = submissionValues.file as string[];
        } else {
          fileIds = uploadedFileIds;
        }
      } else if (typeof submissionValues.file === "string") {
        fileIds = [submissionValues.file];
      } else if (uploadedFileIds.length > 0) {
        fileIds = uploadedFileIds;
      }

      patch({
        name,
        hasUnsavedChanges: false,
        hasData: false,
        confirmClose: false,
        forceClose: true,
        confirmationProps: undefined,
      });

      await new Promise((resolve) => setTimeout(resolve, 1));

      await Promise.all(
        fileIds.map(async (fileId) => {
          await markDocumentAsDone.mutateAsync(fileId);
        }),
      );

      submissionValues.file = fileIds;

      const result = await createComplaint.mutateAsync(submissionValues);

      let distributionCompany = "";
      try {
        const subscription = await subscriptionService.getById(
          submissionValues.subscriptionId,
        );
        if (subscription?.regionId && regions) {
          const region = regions.find((r) => r.id === subscription.regionId);
          distributionCompany = region?.name || "";
        }
      } catch {}

      const complaintId = result?.complaintId || "";

      toast.success(
        tComplaints("toast.submission_success_full", {
          id: complaintId,
          distributionCompany:
            distributionCompany || t("unknown", { ns: "subscriptions" }),
        }),
      );

      onHide();
    } catch (error) {
      toast.error(tComplaints("toast.submission_failed"));
      setIsSubmitting(false);
    }
  };

  const documents = useDocuments(complaintDetails?.file || []);

  const formSchema = viewMode
    ? applicationViewModeSchema
    : newApplicationSchema;

  const handleFieldChange = (formState: any) => {
    setFormValues(formState || {});
  };

  return (
    <div className="p-6 pt-4 md:pt-0 space-y-4">
      {viewMode &&
        modalData &&
        modalData.status &&
        modalData.status.toLowerCase() !== "resolved" && (
          <Alert title={tComplaints("alert.pending_title")} iconless>
            {tComplaints("alert.pending_description")}
          </Alert>
        )}

      {isLoadingDetails || documents.isLoading ? (
        <ComplaintFormSkeleton />
      ) : (
        <AutoForm
          key={complaintDetails ? JSON.stringify(complaintDetails) : "empty"}
          formSchema={formSchema as any} // todo: any
          fieldConfig={
            ((values: ExtendedComplaintsSchema) => {
              return {
                subscriptionId: {
                  label: t("subscription", { ns: "subscriptions" }),
                  fieldType: "selectSearch",
                  options: async (page: number, search: string) => {
                    const response = await subscriptionService.getAll({
                      "filter:ct": {
                        name: search,
                      },
                      pageSize: 10,
                      pageNumber: page,
                    });
                    return {
                      items: response.content.map((sub) => ({
                        label: sub.name,
                        value: sub.id,
                      })),
                      nextPage:
                        page + 1 > response.totalPages ? null : page + 1,
                    };
                  },
                  selectedOptionLabel: async (value: string) => {
                    if (!value) return "";
                    const sub = await subscriptionService.getById(value);
                    return sub.name;
                  },
                  inputProps: {
                    placeholder: t("select_subscription", {
                      ns: "subscriptions",
                    }),
                    disabled: subscriptionsLoading || viewMode,
                    defaultValue: viewMode
                      ? complaintDetails?.subscriptionId || ""
                      : totalSubscriptions === 1
                        ? subscriptions?.pages[0].content[0].id
                        : "",
                    emptyText: t("no_subscriptions_found", {
                      ns: "subscriptions",
                    }),
                    searchText: t("search_subscriptions", {
                      ns: "subscriptions",
                    }),
                  },
                  namespace: "complaints",
                },

                distributionCompany: {
                  label: t("distribution_company", { ns: "subscriptions" }),
                  fieldType: "fallback",
                  inputProps: {
                    disabled: true,
                    defaultValue: complaintDetails?.distributionCompany || "",
                  },
                  namespace: "complaints",
                  onlyIf: () => !!viewMode,
                },

                applicationCategory: {
                  label: t("application_subcategory", {
                    ns: "complaints",
                  }),
                  fieldType: "select",
                  options: Object.entries(
                    categories?.[values.applicationType]?.subcategories ?? {},
                  ).map(([key, value]) => ({
                    label: (value as CategorySubcategory).label[
                      i18n.language.toUpperCase()
                    ] as string,
                    value: key,
                  })),

                  inputProps: {
                    placeholder: t("application_subcategory", {
                      ns: "complaints",
                    }),
                    disabled: viewMode || !values.applicationType,
                    defaultValue: complaintDetails?.applicationCategory || "",
                  },
                  namespace: "complaints",
                },
                applicationDescription: {
                  label: t("application_description"),
                  description: t("application_description_hint"),
                  fieldType: "textarea",
                  inputProps: {
                    placeholder: t("application_description"),
                    disabled: viewMode,
                    defaultValue:
                      complaintDetails?.applicationDescription || "",
                  },
                  namespace: "complaints",
                },
                applicationType: {
                  label: t("application_type", { ns: "complaints" }),
                  fieldType: "select",
                  options: Object.entries(categories ?? {}).map(
                    ([key, value]) => ({
                      label: (value as Category).label[
                        i18n.language.toUpperCase()
                      ] as string,
                      value: key,
                    }),
                  ),
                  inputProps: {
                    placeholder: t("application_type", { ns: "complaints" }),
                    disabled: viewMode,
                    defaultValue: complaintDetails?.applicationType || "",
                  },
                  namespace: "complaints",
                },
                response: {
                  label: t("response", { ns: "complaints" }),
                  fieldType: "textarea",
                  inputProps: {
                    placeholder: t("response", { ns: "complaints" }),
                    disabled: true,
                    defaultValue: complaintDetails?.response || "",
                  },
                  namespace: "complaints",
                  onlyIf: (values: ExtendedComplaintsSchema) =>
                    (values.status || "").toLowerCase() === "resolved",
                },
                file: {
                  fieldType: "customFile",
                  onChange: handleFileUpload,
                  inputProps: {
                    accept: acceptString,
                    multiple: true,
                    defaultValue: complaintDetails?.file || [],
                    maxSize:
                      parseInt(documentUploadLimits?.value?.size) ??
                      10 * 1024 * 1024,
                    maxCount: parseInt(documentUploadLimits?.value?.count) ?? 5,
                  },
                  namespace: "complaints",
                },
              };
            }) as any
          } // todo: any
          namespace="complaints"
          values={initialValues}
          onSubmit={(values) => handleSubmit(values as ComplaintsSchema)}
          onValidChange={(isValid) => setIsValueChange(isValid)}
          onValuesChange={handleFieldChange}
        >
          {viewMode && (complaintDetails?.file ?? []).length > 0 ? (
            <>
              <Separator />
              <div className="font-bold">
                {t("files", { ns: "complaints" })}
              </div>
              {(documents.data ?? []).map((document) => (
                <a
                  className="flex gap-1 items-center align-center text-sm p-4 border rounded-md hover:bg-gray-50 transition-all"
                  href={document.url}
                  download
                  target="_blank"
                  key={document.id}
                >
                  <DocIcon
                    type={document.mimeType}
                    size={24}
                    filename={document.name}
                  />{" "}
                  {document.name}
                </a>
              ))}
            </>
          ) : null}
          {!viewMode && (
            <Button
              className="w-full"
              type="submit"
              disabled={
                !isValueChange ||
                createComplaint.isPending ||
                isUploading ||
                isSubmitting
              }
            >
              {t(
                isSubmitting || createComplaint.isPending
                  ? "submitting"
                  : isUploading
                    ? "uploading_file"
                    : "submit_application",
                { ns: "complaints" },
              )}
            </Button>
          )}
        </AutoForm>
      )}
    </div>
  );
}
