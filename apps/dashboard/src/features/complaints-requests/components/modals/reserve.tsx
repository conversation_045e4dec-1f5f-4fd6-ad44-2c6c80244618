import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import Pattern from "@mass/shared/assets/pattern_decorative.svg";
import FeaturedIcon from "@mass/shared/components/atoms/featured-icon";
import { Button } from "@mass/shared/components/ui/button";
import { useTranslation } from "react-i18next";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm?: () => void | Promise<void>;
  activeTab: "subscriptions" | "facilities";
}

export default function Delete({
  onHide,
  onConfirm,
}: Props & { title?: string; description?: string }) {
  const { t } = useTranslation("complaints");

  const handleDelete = () => {
    if (onConfirm) onConfirm(); // todo: await
    onHide();
  };

  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <img
        src={Pattern}
        alt="pattern"
        className="w-full absolute top-[-118px] left-[-194px] md:top-[-126px] md:left-[-160px] max-w-full overflow-hidden h-[336px] shrink-0 -z-10 pointer-events-none"
      />

      <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-lg text-gray-900 font-text-sm-regular">
        <FeaturedIcon name="untitled:trash-01" className="-mt-16" />
        <div className="self-stretch relative leading-[28px] font-semibold">
          {t("reserve.title", {
            ns: "common",
          })}
        </div>
        <div className="self-stretch relative text-sm leading-[20px] text-gray-600">
          {t("reserve.description", {
            ns: "common",
          })}
        </div>
      </div>

      <div className="w-full flex gap-2 items-center">
        <Button variant="outline" onClick={onHide} className="w-full">
          {t("reserve.cancel", {
            ns: "common",
          })}
        </Button>
        <Button
          variant="destructive"
          className="w-full"
          onClick={handleDelete}
          // disabled={deleteMutation.isPending}
        >
          {t("reserve.confirm", {
            ns: "common",
          })}
        </Button>
      </div>
    </div>
  );
}
