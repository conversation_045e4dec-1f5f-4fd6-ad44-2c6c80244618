import FeaturedIcon from "@mass/shared/components/atoms/featured-icon";
import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { DatePicker } from "@mass/shared/components/ui/date-picker";
import { Label } from "@mass/shared/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mass/shared/components/ui/select";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useComplaintCategories } from "../../data/queries";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm?: () => void | Promise<void>;
}

export default function Filter({
  onHide,
  onConfirm,
  currentFilters,
  clearFilters
  // todo: any
}: Props & { title?: string; description?: string; currentFilters?: any, clearFilters: () => void }) {
  const { t, i18n } = useTranslation("complaints");

  const [type, setType] = useState(currentFilters?.type ?? "all");
  const [subtype, setSubtype] = useState(currentFilters?.subtype ?? "all");

  const { data: categories } = useComplaintCategories(false);

  const [startDate, setStartDate] = useState<Date | undefined>(
    currentFilters?.startDate ?? undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    currentFilters?.endDate ?? undefined
  );

  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-lg text-gray-900 font-text-sm-regular">
        <FeaturedIcon name="untitled:filter-lines" className="-mt-16" />
        <div className="self-stretch relative leading-[28px] font-semibold">
          {t("filter")}
        </div>
      </div>
      <div className="">
        <Label>{t("filter_start_date")}</Label>
        <DatePicker date={startDate} setDate={setStartDate} />
      </div>
      <div className="">
        <Label>{t("filter_end_date")}</Label>
        <DatePicker date={endDate} setDate={setEndDate} />
      </div>
      <div className="">
        <Label>{t("application_type")}</Label>
        <Select
          value={type}
          onValueChange={(newType) => {
            if (type !== newType) setSubtype("all");
            setType(newType);
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder={t("application_type")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("all")}</SelectItem>
            {Object.entries(categories ?? {}).map(([key, value]) => (
              <SelectItem key={key} value={key}>
                {(value as any).label[i18n.language.toUpperCase()] as string}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {/* <div className="">
        <Label>{t("application_category")}</Label>
        <Select
          value={subtype}
          onValueChange={setSubtype}
          disabled={type === "all"}
        >
          <SelectTrigger>
            <SelectValue placeholder={t("application_category")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("all")}</SelectItem>
            {Object.entries(
              // todo: any
              categories?.[type as any]?.subcategories ?? {}
            ).map(([key, value]) => (
              <SelectItem key={key} value={key}>
                {(value as any).label[i18n.language.toUpperCase()] as string}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div> */}
      <div className="w-full flex gap-2 items-center">
        <Button
          variant="outline"
          className="w-full"
          onClick={() => {
            clearFilters()
            onHide();
          }}
        >
          {t("clear_filters")}
        </Button>
        <Button
          className="w-full"
          onClick={() => {
            let _startDate = startDate;
            let _endDate = endDate;

            if (!_startDate && endDate) {
              _startDate = new Date(0);
            } else if (!_endDate && startDate) {
              _endDate = new Date();
            }

            if (_startDate && _endDate) {
              if (_startDate.valueOf() > _endDate.valueOf()) {
                const tmp = _startDate;
                _startDate = _endDate;
                _endDate = tmp;
              }
            }

            (onConfirm as any)?.({
              type,
              subtype,
              startDate: _startDate,
              endDate: _endDate,
            }); // todo: any
            onHide();
          }}
        >
          {t("filter")}
        </Button>
      </div>
    </div>
  );
}
