export function DocIcon(props: {
  filename: string;
  type: string;
  size?: string | number;
}) {
  const randomId = Math.random().toString(36).slice(2);

  const typesMap: Record<string, { color: string; extension: string }> = {
    "application/pdf": { color: "#E74C3C", extension: "pdf" }, // red - Adobe PDF style
    "application/msword": { color: "#2E86C1", extension: "doc" }, // blue - Word
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
      color: "#2E86C1",
      extension: "docx",
    },
    "application/vnd.ms-powerpoint": { color: "#D35400", extension: "ppt" }, // orange - PowerPoint
    "application/vnd.openxmlformats-officedocument.presentationml.presentation":
      { color: "#D35400", extension: "pptx" },
    "application/vnd.ms-excel": { color: "#27AE60", extension: "xls" }, // green - Excel
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
      color: "#27AE60",
      extension: "xlsx",
    },
    "text/markdown": { color: "#607D8B", extension: "md" }, // grey-blue - dev-friendly
    "text/plain": { color: "#95A5A6", extension: "txt" }, // light grey
    "image/jpeg": { color: "#F1C40F", extension: "jpg" }, // yellow - bright and common
    "image/png": { color: "#F39C12", extension: "png" }, // orange-yellow
    "image/gif": { color: "#9B59B6", extension: "gif" }, // purple - retro feel
    "image/svg+xml": { color: "#16A085", extension: "svg" }, // teal - vector graphics
    "image/webp": { color: "#1ABC9C", extension: "webp" }, // turquoise - modern image
    "image/tiff": { color: "#8E44AD", extension: "tiff" }, // dark purple - niche
    "image/bmp": { color: "#34495E", extension: "bmp" }, // dark grey - old format
    "image/x-icon": { color: "#7D3C98", extension: "ico" }, // violet - icons
    "image/vnd.microsoft.icon": { color: "#7D3C98", extension: "ico" },
    "video/mp4": { color: "#C0392B", extension: "mp4" }, // red - YouTube style
    "video/mpeg": { color: "#E67E22", extension: "mpeg" }, // warm orange
    "video/ogg": { color: "#A569BD", extension: "ogg" }, // lavender
    "video/webm": { color: "#3498DB", extension: "webm" }, // bluish - web standard
    "video/3gpp": { color: "#5D6D7E", extension: "3gp" }, // grey-blue
    "video/3gpp2": { color: "#5D6D7E", extension: "3g2" },
    "video/avi": { color: "#1F618D", extension: "avi" }, // deep blue - classic
    "video/quicktime": { color: "#2ECC71", extension: "mov" }, // green - Apple-ish
  };

  const proposedExtension = props.filename.split(".").pop();
  const extension = (typesMap[props.type]?.extension ?? proposedExtension ?? "")
    .substring(0, 4)
    .toLowerCase();
  const color = typesMap[props.type]?.color ?? "#34495E"; // dark grey

  const size = props.size ?? "20";

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="feather feather-file"
      style={{
        color,
      }}
    >
      <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z" />
      <polyline points="13 2 13 9 20 9" />

      {extension ? (
        <>
          <path
            d="M20 12 L8 12 L8 18 L20 18 L20 12 Z"
            fill="currentColor"
            mask={`url(#hole-${randomId})`}
          />

          <mask id={`hole-${randomId}`}>
            <path
              d="M20 12 L8 12 L8 18 L20 18 L20 12 Z"
              fill="white"
              stroke="white"
            />
            <text
              x="8.5"
              y="16.5"
              stroke="none"
              fill="black"
              style={{
                font: "bold 4.7px sans-serif",
              }}
            >
              {extension.toUpperCase()}
            </text>
          </mask>
        </>
      ) : null}
    </svg>
  );
}
