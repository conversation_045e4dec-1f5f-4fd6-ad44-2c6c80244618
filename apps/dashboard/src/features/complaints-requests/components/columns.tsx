import Iconify from "@mass/shared/components/atoms/Iconify";
import Action from "@mass/shared/components/organisms/table/action";
import { SortHeader } from "@mass/shared/components/organisms/table/SortHeader";
import { Badge } from "@mass/shared/components/ui/badge";
import { DropdownMenuItem } from "@mass/shared/components/ui/dropdown-menu";
import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import * as Locale from "date-fns/locale";
import { useTranslation } from "react-i18next";
import { useCancelComplaint, useComplaintCategories } from "../data/queries";
import StatusBadge from "./status-badge";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { useState } from "react";
import Reserve from "./modals/reserve";

export interface ComplaintRequestType {
  id: string;
  createdAt: string;
  userId: string;
  subscriptionId: string;
  type: string;
  subtype: string;
  status: string;
  body: string;
  updatedAt: string | null;
  response: string | null;
  documents: never[];
  subscription?: {
    name?: string;
  };
}

export const columns: ColumnDef<ComplaintRequestType, unknown>[] = [
  {
    accessorKey: "body",
    enableSorting: false,
    header: () => {
      const { t } = useTranslation("complaints");
      return (
        <div className="flex items-center gap-3 pl-2">
          <span className="font-medium">{t("columns.application_id")}</span>
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex items-start gap-3 pl-2">
          <div className="flex flex-col">
            <span className="font-medium text-gray-700">{row.original.id}</span>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "subscriptionId",
    enableSorting: false,
    header: () => {
      const { t } = useTranslation("subscriptions");
      return <span className="font-medium">{t("subscription")}</span>;
    },
    cell: ({ row }) => {
      return <span>{row.original.subscription?.name}</span>;
    },
  },
  {
    accessorKey: "type",
    enableSorting: true,
    header: ({ column }) => {
      const { t } = useTranslation("complaints");
      return <SortHeader column={column} title={t("columns.request_type")} />;
    },
    cell: ({ row }) => {
      const { data: categories, isLoading } = useComplaintCategories();
      const { i18n, t } = useTranslation();
      
      const type = row.original.type;
      const subtype = row.original.subtype;
      
      if (isLoading) {
        return <Badge variant="outline" color="gray">{t("loading", { ns: "common" })}</Badge>;
      }

      const category = categories?.[type]?.subcategories?.[subtype]?.label;
      
      return (
        <Badge variant="outline" color={category ? undefined : "gray"}>
          {category 
            ? category[i18n.language.toUpperCase()] 
            : subtype.charAt(0).toUpperCase() + subtype.slice(1).toLowerCase().replace(/_/g, ' ')}
        </Badge>
      );
    },
  },
  {
    accessorKey: "status",
    enableSorting: true,
    header: ({ column }) => {
      const { t } = useTranslation("complaints");
      return <SortHeader column={column} title={t("columns.status")} />;
    },
    cell: ({ row }) => {
      const value = row.getValue<string>("status");
      return <StatusBadge status={value} />;
    },
  },
  {
    accessorKey: "createdAt",
    enableSorting: true,
    header: ({ column }) => {
      const { t } = useTranslation("complaints");
      return (
        <SortHeader column={column} title={t("columns.application_date")} />
      );
    },
    cell: ({ row }) => {
      const { i18n } = useTranslation();
      const locale = Locale[i18n.language as "tr"] || Locale.tr;

      const rawDate = row.getValue<string>("createdAt");
      let formattedDate = "-";

      try {
        if (rawDate) {
          const date = new Date(rawDate);
          if (!isNaN(date.getTime())) {
            formattedDate = format(date, "d MMM, yyyy", { locale });
          }
        }
      } catch (error) {
        console.error("Date formatting error:", error);
      }

      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "updatedAt",
    enableSorting: true,
    header: ({ column }) => {
      const { t } = useTranslation("complaints");
      return <SortHeader column={column} title={t("columns.response_date")} />;
    },
    cell: ({ row }) => {
      const { i18n } = useTranslation();
      const locale = Locale[i18n.language as "tr"] || Locale.tr;

      const rawDate = row.getValue<string>("updatedAt");
      let formattedDate = "-";

      try {
        if (rawDate) {
          const date = new Date(rawDate);
          if (!isNaN(date.getTime())) {
            formattedDate = format(date, "d MMM, yyyy", { locale });
          }
        }
      } catch (error) {
        console.error("Date formatting error:", error);
      }

      return <div>{formattedDate}</div>;
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const { t } = useTranslation("complaints");
      const { mutate } = useCancelComplaint();
      const { open } = useModal();
      const [ellipsisOpen, setEllipsisOpen] = useState(false);

      const handleEdit = (e: React.MouseEvent) => {
        e.stopPropagation();
        setEllipsisOpen(false);

        const complaintId = row.original.id;

        open(Reserve as React.ComponentType<any>, {
          name: "reserve-modal",
          onConfirm: () => {
            return new Promise<void>((resolve, reject) =>
              mutate(complaintId, {
                onSuccess: () => {

                  resolve();
                },
                onError: (error) => {
                  console.error("Error cancelling complaint:", error);
                  reject(error);
                },
              })
            );
          },
        });
      };

      return (
        <div className="flex w-full justify-end items-center">
          {row.original.status === "PENDING" ? (
            <Action
              action={
                <div
                  className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 cursor-pointer"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Iconify name="untitled:dots-vertical" className="size-4" />
                </div>
              }
              open={ellipsisOpen}
              onOpenChange={setEllipsisOpen}
            >
              <DropdownMenuItem
                className="flex text-xs font-medium items-center justify-between"
                onClick={handleEdit}
              >
                {t("restore")}
                <Iconify name="untitled:reverse-left" className="size-2" />
              </DropdownMenuItem>
            </Action>
          ) : null}
        </div>
      );
    },
  },
];
