import React from "react";
import { useTranslation } from "react-i18next";

interface StatusBadgeProps {
  status: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const { t } = useTranslation("common");
  const baseClasses = "px-2 py-1 text-xs font-medium rounded-full capitalize";
  let statusClasses = "bg-gray-50 text-gray-600 border border-gray-200";

  if (status.toLowerCase() === "pending") {
    statusClasses = "bg-yellow-50 border border-yellow-200 text-yellow-800";
  } else if (status.toLowerCase() === "resolved") {
    statusClasses = "bg-green-50 border border-green-200 text-green-800";
  } else if (status.toLowerCase() === "in progress") {
    statusClasses = "bg-blue-50 border border-blue-200 text-blue-800";
  } else if (status.toLowerCase() === "rejected") {
    statusClasses = "bg-red-50 text-red-800 border border-red-300";
  } else if (status.toLowerCase() === "canceled") {
    statusClasses = "bg-red-50 text-red-800 border border-red-300";
  } else if (status.toLowerCase() === "complaint") {
    statusClasses = "bg-purple-50 border border-purple-200 text-purple-800";
  } else if (status.toLowerCase() === "request") {
    statusClasses = "bg-teal-50 border border-teal-200 text-teal-800";
  }

  return (
    <span className={`${baseClasses} ${statusClasses}`}>
      {t(`status.${status.toLowerCase()}`)}
    </span>
  );
};

export default StatusBadge;
