import { Card, CardContent } from "@mass/shared/components/ui/card";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import { useDateLocale } from "@mass/shared/hooks/use-date-locale";
import { SubscriptionDetailSkeleton } from "./components/skeleton-loader";
import { useRegions, useSubscriptionDetail } from "./data/queries";

// Define subscription type with all required properties
interface SubscriptionDetail {
  name: string;
  startDate: string;
  type: string;
  regionId: string;
  installationId: string;
  individual: boolean;
  personIdentifier: string;
  details?: {
    address?: string;
  };
}

export const OverviewTab = (props: { subscriptionId: string }) => {
  const { t } = useTranslation("subscriptions");
  const { data: subscription, isLoading: loading } = useSubscriptionDetail(
    props.subscriptionId
  ) as {
    data: SubscriptionDetail | undefined;
    isLoading: boolean;
  };
  const dateLocale = useDateLocale();
  const { data: regions, isLoading: regionsLoading } = useRegions();

  if (!subscription) {
    return null;
  }

  return loading || regionsLoading ? (
    <SubscriptionDetailSkeleton />
  ) : (
    <Card className="!px-0 py-6 w-full">
      <CardContent className="space-y-6">
        <div>
          <div className="px-4 sm:px-0">
            <h4 className="font-semibold text-base/7 text-gray-900">
              {t("subscription_info")}
            </h4>
          </div>

          <div className="mt-4 border-t border-gray-100">
            <dl className="divide-y divide-gray-100">
              <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm/6 font-medium text-gray-900">
                  {t("subscription_name")}
                </dt>
                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {subscription.name}
                </dd>
              </div>

              <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm/6 font-medium text-gray-900">
                  {t("distribution_company")}
                </dt>
                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {(regions ?? []).find(
                    (region) => region.id === subscription.regionId
                  )?.name ?? t("unknown")}
                </dd>
              </div>

              <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm/6 font-medium text-gray-900">
                  {t("installation_no")}
                </dt>
                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {subscription.installationId}
                </dd>
              </div>

              {!subscription.type.endsWith("-consumption") && (
                <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm/6 font-medium text-gray-900">
                    {t("installation_type")}
                  </dt>
                  <dd className="mt-1 sm:col-span-2 sm:mt-0">
                    <div className="w-fit">
                      <span className="px-2 py-1 rounded-full bg-green-50 border border-green-200 text-green-800 flex items-center text-xs">
                        <span className="px-1 py-1 bg-success-600 rounded-full mr-1"></span>
                        {t("electricity_production")}
                      </span>
                    </div>
                  </dd>
                </div>
              )}

              {subscription.details?.address && (
                <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm/6 font-medium text-gray-900">
                    {t("address")}
                  </dt>
                  <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                    {subscription.details.address}
                  </dd>
                </div>
              )}
            </dl>
          </div>
        </div>

        <div>
          <div className="px-4 sm:px-0">
            <h4 className="font-semibold text-base/7 text-gray-900">
              {t("consumer_info")}
            </h4>
          </div>

          <div className="mt-4 border-t border-gray-100">
            <dl className="divide-y divide-gray-100">
              <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm/6 font-medium text-gray-900">
                  {t("subscription_type")}
                </dt>
                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {t(
                    subscription.individual
                      ? "subscription_type_individual"
                      : "subscription_type_corporate"
                  )}
                </dd>
              </div>

              <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm/6 font-medium text-gray-900">
                  {t(subscription.individual ? "tckn" : "vkn")}
                </dt>
                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {subscription.personIdentifier}
                </dd>
              </div>

              <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm/6 font-medium text-gray-900">
                  {t("subscription_start_date")}
                </dt>
                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                  {format(new Date(subscription.startDate), "PPP", {
                    locale: dateLocale,
                  })}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
