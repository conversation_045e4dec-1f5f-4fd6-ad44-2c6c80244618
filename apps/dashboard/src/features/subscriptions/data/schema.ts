import { z } from "zod";

const _createSubscriptionSchema = z.object({
  name: z.string().min(1, "subscriptions:subscription_name_required"),
  type: z
    .enum(["corporate", "individual"], {
      required_error: "subscriptions:subscription_type_required",
    })
    .default("individual"),
  distributionCompany: z.string().min(1, "subscriptions:distribution_company_required"),
  taxNumber: z.number().min(10, "subscriptions:tax_number_required").optional(),
  installationId: z.string().min(1, "subscriptions:installation_id_required"),
});

export const createSubscriptionSchema = _createSubscriptionSchema.superRefine(
  (data, ctx) => {
    if (data.type === "corporate") {
      if (
        data.taxNumber === undefined ||
        data.taxNumber.toString().length === 0
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "subscriptions:tax_number_required",
        });
        return;
      }

      if (!/^\d+$/.test(data.taxNumber.toString())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "subscriptions:tax_number_digits_only",
        });
        return;
      }
    }
  }
);

export const updateSubscriptionSchema = _createSubscriptionSchema.pick({
  name: true,
});

export type SubscriptionCreateSchema = z.infer<typeof createSubscriptionSchema>;
export type SubscriptionUpdateSchema = z.infer<typeof updateSubscriptionSchema>;
