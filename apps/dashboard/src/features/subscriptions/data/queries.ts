import type {
  SubscriptionCreateSchema,
  SubscriptionUpdateSchema,
} from "@/features/subscriptions/data/schema";
import { subscriptionService } from "@/services/api/subscriptions";
import type { ApiQueryParams, PagedResponse } from "@/services/types";
import type { Subscription as SubscriptionType } from "@/features/subscriptions/components/columns";
import type { Region } from "@/services/api/subscriptions";
import {
  useMutation,
  useInfiniteQuery,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { useTranslation } from "react-i18next";

export const subscriptionKeys = {
  all: ["subscriptions"] as const,
  lists: () => [...subscriptionKeys.all, "list"] as const,
  list: (params?: ApiQueryParams) =>
    [...subscriptionKeys.lists(), params] as const,
  details: () => [...subscriptionKeys.all, "detail"] as const,
  detail: (id: string) => [...subscriptionKeys.details(), id] as const,
  usage: () => [...subscriptionKeys.all, "usage"] as const,
  usageData: (
    id: string,
    startDate: Date | null,
    endDate: Date | null,
    granularity: string,
    compareTo: string[]
  ) =>
    [
      ...subscriptionKeys.usage(),
      id,
      startDate ? startDate.toISOString() : null,
      endDate ? endDate.toISOString() : null,
      granularity,
      compareTo,
    ] as const,
  regions: () => ["regions"] as const,
  regionById: (id: string) => [...subscriptionKeys.regions(), id] as const,
  regionsPaginated: (page: number, search?: string) =>
    [...subscriptionKeys.regions(), "paginated", page, search || ""] as const,
};

function logQueryError(error: unknown) {
  console.error("Query error:", error);
}

export function useSubscriptions(options?: Partial<ApiQueryParams>) {
  const params: ApiQueryParams = {
    pageSize: 10,
    orderBy: "createdAt:asc",
    ...options,
    pageNumber: Math.max(options?.pageNumber ?? 1, 1),
  };

  return useInfiniteQuery<PagedResponse<SubscriptionType>, unknown>({
    initialPageParam: params.pageNumber,
    queryKey: subscriptionKeys.list(params),
    queryFn: async (ctx) => {
      const page =
        typeof ctx.pageParam === "number" ? ctx.pageParam : params.pageNumber;
      return subscriptionService.getAll({ ...params, pageNumber: page });
    },
    getNextPageParam: (lastPage: PagedResponse<SubscriptionType>) =>
      lastPage.number < lastPage.totalPages ? lastPage.number + 1 : undefined,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

export function useSubscriptionDetail(id: string) {
  return useQuery<unknown, unknown, unknown>({
    queryKey: subscriptionKeys.detail(id),
    queryFn: () => subscriptionService.getById(id),
    enabled: !!id,
    staleTime: 60 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
    retry: false,
  });
}

export function useSubscriptionUsageData(id: string) {
  // this is a query, but we want to use it as a mutation
  return useMutation({
    mutationFn: (data: {
      startDate: Date | undefined;
      endDate: Date | undefined;
      granularity: "hour" | "day" | "month" | "year";
      compareTo: string[];
    }) =>
      subscriptionService.getUsageData(
        id,
        data.startDate!,
        data.endDate!,
        data.granularity,
        data.compareTo
      ),
  });
}

export function useExportSubscriptionUsageMutation(
  id: string,
  startDate: Date | undefined,
  endDate: Date | undefined,
  granularity: "hour" | "day" | "month" | "year",
  compareTo: string[]
) {
  const { i18n } = useTranslation("subscriptions");

  return useMutation({
    mutationFn: (fileType: string) =>
      subscriptionService.exportUsageData(
        id,
        startDate!,
        endDate!,
        granularity,
        compareTo,
        fileType,
        i18n.language.toLowerCase()
      ),
    onError: (error) => logQueryError(error),
  });
}

export function useExportSubscriptionOutageMutation(
  id: string,
  startDate: Date | undefined,
  endDate: Date | undefined
) {
  const { i18n } = useTranslation("subscriptions");

  return useMutation({
    mutationFn: (fileType: string) =>
      subscriptionService.exportOutageData(
        id,
        startDate!,
        endDate!,
        fileType,
        i18n.language.toLowerCase()
      ),
    onError: (error) => logQueryError(error),
  });
}

export function useRegions() {
  return useQuery<Region[], unknown>({
    queryKey: subscriptionKeys.regions(),
    queryFn: subscriptionService.getRegions,
    staleTime: 10 * 60 * 1000, // Cache for 10 minutes
    retry: 2,
  });
}

export function useRegionById(id: string) {
  return useQuery<Region | undefined, unknown>({
    queryKey: subscriptionKeys.regionById(id),
    queryFn: () => subscriptionService.getRegionById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
    retry: 1,
  });
}

export function useRegionsPaginated(page: number = 0, search?: string) {
  return useQuery({
    queryKey: subscriptionKeys.regionsPaginated(page, search),
    queryFn: () => subscriptionService.getRegionsPaginated(page, 10, search),
    staleTime: 10 * 60 * 1000,
    retry: 1,
  });
}

export function useCreateSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      data: SubscriptionCreateSchema & {
        facilityType: string;
        tckn?: string;
        vkn?: string;
      }
    ) => {
      return subscriptionService.create(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.lists() });
    },
    onError: (error) => logQueryError(error),
  });
}

export function useUpdateSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: SubscriptionUpdateSchema;
    }) => {
      return subscriptionService.update(id, data);
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.lists() });
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.detail(id) });

      queryClient.invalidateQueries({ queryKey: ["notifications"] });
      queryClient.invalidateQueries({ queryKey: ["complaints"] });

      queryClient.invalidateQueries({
        predicate: (query) => {
          return query.queryKey.some(
            (segment) => typeof segment === "string" && segment === id
          );
        },
      });
    },
    onError: (error) => logQueryError(error),
  });
}

export function useDeleteSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => {
      return subscriptionService.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.lists() });
      
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: subscriptionKeys.lists() });
      }, 0);
    },
    onError: (error) => logQueryError(error),
  });
}

export function useUpdateNotificationSettings(id: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      unexpectedUsageThreshold: number;
      userDefinedLimit: number;
    }) => subscriptionService.updateNotificationSettings(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.lists() });
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.detail(id) });

      // Invalidate notifications that may rely on these settings
      queryClient.invalidateQueries({ queryKey: ["notifications"] });

      // Invalidate any query containing this subscription ID
      queryClient.invalidateQueries({
        predicate: (query) => {
          return query.queryKey.some(
            (segment) => typeof segment === "string" && segment === id
          );
        },
      });
    },
    onError: (error) => logQueryError(error),
  });
}
