import type { IconNames } from "@mass/shared/components/atoms/Iconify";
import type { BreadcrumbItem } from "@mass/shared/components/organisms/layout/header";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import type { Subscription } from "../components/columns";

import { sidebarData } from "@/constants/sidebar-data";
import { Skeleton } from "@mass/shared/components/ui/skeleton";
import { Tabs, TabsList, TabsTrigger } from "@mass/shared/components/ui/tabs";
import React, { useMemo } from "react";
import {
  Navigate,
  useNavigate,
  useParams,
} from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { DataTab } from "../data";
import { useSubscriptionDetail } from "../data/queries";
import { NotificationTab } from "../notification";
import { OverviewTab } from "../overview";
import { OutagesTab } from "../outages";
import { useIsDisabled } from "@/utils/use-disabled";
import { Button } from "@mass/shared/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger 
} from "@mass/shared/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";

export interface DetailSubscription extends Subscription {
  distributionCompany: string;
  taxNumber: string;
  invoiceNo?: string;
  invoicePeriod?: string;
  contractNo?: string;
  installationNo?: string;
  fullName?: string;
  identityNo?: string;
  address?: string;
  consumerType?: string;
  phone?: string;
  readingStartDate?: string;
  readingEndDate?: string;
  readingDays?: number;
  readingInfo?: string;
  firstIndex?: number;
  lastIndex?: number;
  indexDifference?: number;
  unexpectedUsageThreshold: number | null;
  userDefinedLimit: number | null;
}

export default function SubscriptionDetail({
  subscriptionId: propId,
}: {
  subscriptionId?: string;
}) {
  const { t } = useTranslation("subscriptions");
  const navigate = useNavigate();

  const { subscriptionId: paramSubscriptionId } = useParams({ strict: false });
  
  // Memoize values to prevent unnecessary renders
  const subscriptionIdFromParams = useMemo(() => paramSubscriptionId || "", [paramSubscriptionId]);
  const subscriptionId = useMemo(() => propId || subscriptionIdFromParams || "", [propId, subscriptionIdFromParams]);
  
  // Only compute activeTab once based on pathname
  const activeTab = useMemo(() => {
    const pathname = window.location.pathname;
    if (pathname.includes('/data')) return "data";
    if (pathname.includes('/notifications')) return "notifications";
    if (pathname.includes('/outages')) return "outages";
    return "overview";
  }, []); // Empty dependency array as we only need this on mount

  // Memoize permission checks to prevent unnecessary rerenders
  const isDetailsDisabled = useIsDisabled([
    "disabled.subscriptions.view",
    "disabled.subscriptions.details",
  ]);

  const isNotifsDisabled = useIsDisabled([
    "disabled.subscriptions.view",
    "disabled.subscriptions.notifs",
  ]);

  const isUsageDisabled = useIsDisabled([
    "disabled.subscriptions.view",
    "disabled.subscriptions.usage",
  ]);

  const isOutagesDisabled = useIsDisabled([
    "disabled.subscriptions.view",
    "disabled.subscriptions.outages",
  ]);

  // Use cache and stable references to prevent unnecessary data fetching
  const {
    data: subscription,
    isLoading: loading,
    error,
  } = useSubscriptionDetail(subscriptionId || "") as {
    data: DetailSubscription | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Handle permissions-based navigation in a single effect with proper dependencies
  const navigationHandler = React.useCallback(() => {
    if (loading) return;

    // All tabs disabled - navigate to subscriptions list
    if (
      isDetailsDisabled &&
      isNotifsDisabled &&
      isUsageDisabled &&
      isOutagesDisabled
    ) {
      navigate({
        to: "/subscriptions",
        replace: true,
      });
      return;
    }

    // Handle tab-specific permission redirects
    if (activeTab === "overview" && isDetailsDisabled) {
      const nextAvailableTab = !isUsageDisabled 
        ? "data" 
        : !isOutagesDisabled 
          ? "outages" 
          : !isNotifsDisabled 
            ? "notifications" 
            : null;
            
      if (nextAvailableTab) {
        navigate({
          to: `/subscriptions/$subscriptionId/${nextAvailableTab}`,
          params: { subscriptionId },
          replace: true,
        });
      }
    } else if (activeTab === "data" && isUsageDisabled) {
      const nextAvailableTab = !isOutagesDisabled 
        ? "outages" 
        : !isNotifsDisabled 
          ? "notifications" 
          : !isDetailsDisabled 
            ? "overview" 
            : null;
            
      if (nextAvailableTab) {
        navigate({
          to: nextAvailableTab === "overview" 
            ? "/subscriptions/$subscriptionId" 
            : `/subscriptions/$subscriptionId/${nextAvailableTab}`,
          params: { subscriptionId },
          replace: true,
        });
      }
    } else if (activeTab === "outages" && isOutagesDisabled) {
      const nextAvailableTab = !isNotifsDisabled 
        ? "notifications" 
        : !isDetailsDisabled 
          ? "overview" 
          : !isUsageDisabled 
            ? "data" 
            : null;
            
      if (nextAvailableTab) {
        navigate({
          to: nextAvailableTab === "overview" 
            ? "/subscriptions/$subscriptionId" 
            : `/subscriptions/$subscriptionId/${nextAvailableTab}`,
          params: { subscriptionId },
          replace: true,
        });
      }
    } else if (activeTab === "notifications" && isNotifsDisabled) {
      const nextAvailableTab = !isDetailsDisabled 
        ? "overview" 
        : !isUsageDisabled 
          ? "data" 
          : !isOutagesDisabled 
            ? "outages" 
            : null;
            
      if (nextAvailableTab) {
        navigate({
          to: nextAvailableTab === "overview" 
            ? "/subscriptions/$subscriptionId" 
            : `/subscriptions/$subscriptionId/${nextAvailableTab}`,
          params: { subscriptionId },
          replace: true,
        });
      }
    }
  }, [
    loading, 
    isDetailsDisabled, 
    isNotifsDisabled, 
    isUsageDisabled, 
    isOutagesDisabled, 
    activeTab, 
    subscriptionId, 
    navigate
  ]);
  
  // Run navigation checks only once when data is available or permissions change
  React.useEffect(() => {
    navigationHandler();
  }, [navigationHandler]);

  // Memoize tab change handler to prevent recreation on each render
  const handleTabChange = React.useCallback((value: string) => {
    if (!subscriptionId) return;

    if (value === "overview") {
      navigate({
        to: "/subscriptions/$subscriptionId",
        params: { subscriptionId },
        replace: true,
      });
    } else {
      navigate({
        to: `/subscriptions/$subscriptionId/${value}`,
        params: { subscriptionId },
        replace: true,
      });
    }
  }, [subscriptionId, navigate]);

  if (error) {
    console.error("Error fetching subscription details:", error);
  }

  // Memoize breadcrumb to prevent unnecessary recreations on render
  const breadcrumb = useMemo(() => {
    return [
      {
        title: t("title"),
        icon: "untitled:building-02" as IconNames,
        path: "/subscriptions",
      },
      {
        title: loading ? (
          <Skeleton className="h-4 w-28" />
        ) : (
          subscription?.name || t("subscription_detail")
        ),
      },
    ] as BreadcrumbItem[];
  }, [loading, subscription?.name, t]);

  // If there's an error, don't show it in the UI but log it
  if (error) {
    console.error("Error fetching subscription details:", error);
  }

  // Render nothing if conditions require a redirect to subscriptions page
  if (!subscription && !loading) {
    return <Navigate to="/subscriptions" />;
  }
  
  // Memoize tab content to prevent unnecessary renders
  const tabContent = useMemo(() => {
    switch (activeTab) {
      case "overview":
        return <OverviewTab key="overview" subscriptionId={subscriptionId} />;
      case "data":
        return <DataTab key="data" subscriptionId={subscriptionId} />;
      case "outages":
        return <OutagesTab key="outages" subscriptionId={subscriptionId} />;
      case "notifications":
        return <NotificationTab key="notifications" subscriptionId={subscriptionId} />;
      default:
        return <OverviewTab key="overview" subscriptionId={subscriptionId} />;
    }
  }, [activeTab, subscriptionId]);
  
  // Memoize tab title for mobile dropdown to prevent unnecessary calculations
  const tabTitle = useMemo(() => {
    const type = subscription?.type || "";
    const isConsumption = type.endsWith("-consumption");
    
    switch(activeTab) {
      case "overview": return t("subscription_detail");
      case "data": return t("data_query");
      case "outages": return t("outages");
      case "notifications": return t("notification_settings");
      default: return t("subscription_detail");
    }
  }, [activeTab, subscription?.type, t]);
  
  // Memoize header content to prevent recreating on each render
  const mobileMenuContent = useMemo(() => (
    <div className="md:hidden">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="mt-2 w-full flex justify-between items-center">
            {tabTitle}
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-full">
          {!isDetailsDisabled && (
            <DropdownMenuItem onClick={() => handleTabChange("overview")}>
              {t("subscription_detail") }
            </DropdownMenuItem>
          )}
          {!isUsageDisabled && (
            <DropdownMenuItem onClick={() => handleTabChange("data")}>
              {t("data_query")}
            </DropdownMenuItem>
          )}
          {!isOutagesDisabled && (
            <DropdownMenuItem onClick={() => handleTabChange("outages")}>
              {t("outages")}
            </DropdownMenuItem>
          )}
          {!isNotifsDisabled && (
            <DropdownMenuItem onClick={() => handleTabChange("notifications")}>
              {t("notification_settings")}
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  ), [
    tabTitle, 
    isDetailsDisabled, 
    isUsageDisabled, 
    isOutagesDisabled, 
    isNotifsDisabled,
    t, 
    handleTabChange
  ]);
  
  // Memoize desktop tabs to prevent recreating on each render
  const desktopTabsContent = useMemo(() => (
    <div className="hidden md:block">
      <Tabs
        value={activeTab}
        className="mt-2"
        onValueChange={handleTabChange}
        defaultValue={activeTab}
      >
        <TabsList className="border-b border-border/50 !bg-transparent">
          {!isDetailsDisabled && (
            <TabsTrigger
              value="overview"
              className="rounded-none border-b pb-3 border-border/50 !shadow-none !bg-transparent data-[state=active]:border-primary data-[state=active]:border-b-2 data-[state=active]:text-primary"
            >
              {t("subscription_detail")}
            </TabsTrigger>
          )}
          {!isUsageDisabled && (
            <TabsTrigger
              value="data"
              className="rounded-none border-b pb-3 border-border/50 !shadow-none !bg-transparent data-[state=active]:border-primary data-[state=active]:border-b-2 data-[state=active]:text-primary"
            >
              {t("data_query")}
            </TabsTrigger>
          )}
          {!isOutagesDisabled && (
            <TabsTrigger
              value="outages"
              className="rounded-none border-b pb-3 border-border/50 !shadow-none !bg-transparent data-[state=active]:border-primary data-[state=active]:border-b-2 data-[state=active]:text-primary"
            >
              {t("outages")}
            </TabsTrigger>
          )}
          {!isNotifsDisabled && (
            <TabsTrigger
              value="notifications"
              className="rounded-none border-b pb-3 border-border/50 !shadow-none !bg-transparent data-[state=active]:border-primary data-[state=active]:border-b-2 data-[state=active]:text-primary"
            >
              {t("notification_settings")}
            </TabsTrigger>
          )}
        </TabsList>
      </Tabs>
    </div>
  ), [
    activeTab, 
    handleTabChange, 
    isDetailsDisabled, 
    isUsageDisabled, 
    isOutagesDisabled, 
    isNotifsDisabled,
    t
  ]);
  
  // Single consistent render with minimal non-memoized content
  return (
    <>
      <Header
        title={loading ? <Skeleton className="h-6 w-28" /> : subscription?.name || t("subscription_detail")}
        breadcrumb={breadcrumb}
        sidebarData={sidebarData}
        subContent={
          <>
            {mobileMenuContent}
            {desktopTabsContent}
          </>
        }
      />
      <Main className="pt-4 flex flex-col justify-start">
        {tabContent}
      </Main>
    </>
  );
}
