import { useMe } from "@/features/auth";
import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import AutoForm from "@mass/shared/components/ui/auto-form/index";
import type { FieldConfig } from "@mass/shared/components/ui/auto-form/types";
import { Button } from "@mass/shared/components/ui/button";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useCreateSubscription } from "../../data/queries";
import type { SubscriptionCreateSchema } from "../../data/schema";
import { createSubscriptionSchema } from "../../data/schema";
import { BillingFormSkeleton } from "../skeleton-loader";
import { subscriptionService } from "@/services/api/subscriptions";
import { unknown } from "zod";

interface Props extends ModalProps {
  onHide: () => void;
  activeTab: "subscriptions" | "facilities";
}

export default function SubscriptionBillingModal({
  onHide,
  activeTab,
}: Props & { title?: string; description?: string }) {
  const { t } = useTranslation("subscriptions");
  const [isValueChange, setIsValueChange] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const createMutation = useCreateSubscription();

  const meQuery = useMe();

  const handleSubmit = async (values: SubscriptionCreateSchema) => {
    if (createMutation.isPending) return;
    createMutation.mutate(
      {
        ...values,
        facilityType: values.type,
        ...(values.type === "individual"
          ? { tckn: meQuery.data!.tckn }
          : { vkn: values.taxNumber?.toString() }),
      },
      {
        onSuccess: () => {
          onHide();
          setError(null);
        },
        onError: (error) => setError(error.message),
      }
    );
  };

  const fieldConfig: FieldConfig<SubscriptionCreateSchema> = {
    distributionCompany: {
      order: 1,
      label: t("distribution_company"),
      fieldType: "selectSearch",
      options: async (page: number, search: string) => {
        const result = await subscriptionService.getRegionsPaginated(page, 100, search);
        
        return {
          items: result.items.map(region => ({
            label: region.name,
            value: region.id,
          })),

          nextPage: result.hasNextPage ? page + 1 : 0,
        };
      },
      selectedOptionLabel: async (value: string) => {
        if (!value) return "";

        const region = await subscriptionService.getRegionById(value);
        return region?.name || "";
      },
      inputProps: {
        placeholder: t("common.distribution_company_placeholder", { ns: "common" }),
        emptyText: t("common.no_regions_found", { ns: "common" }),
        searchText: t("common.search_regions", { ns: "common" }),
      } as any, // todo: any
    },
    installationId: {
      order: 2,
      label: t("installation_id"),
      inputProps: {
        placeholder: t("installation_id_placeholder"),
      },
    },
    type: {
      order: 3,
      label: t("subscription_type"),
      options: [
        { label: t("subscription_type_individual"), value: "individual" },
        { label: t("subscription_type_corporate"), value: "corporate" },
      ],
    },
    taxNumber: {
      order: 4,
      label: t("tax_number"),
      inputProps: {
        placeholder: t("tax_number_placeholder"),
      },
      onlyIf: (values) => values.type === "corporate",
    },
    name: {
      order: 5,
      label: t("subscription_name"),
      inputProps: {
        placeholder: t("subscription_name_placeholder"),
      },
    },
  };

  const isSubmitting = createMutation.isPending;

  return (
    <div className="p-6 py-8 md:pt-0 space-y-4">
      {isLoading ? (
        <BillingFormSkeleton />
      ) : (
        <>
        {error ? (
          <div className="text-red-500 text-sm">{t("error." + encodeURIComponent(error))}</div>
        ) : null}
        <AutoForm
          formSchema={createSubscriptionSchema}
          fieldConfig={fieldConfig as any} // todo: any
          onSubmit={handleSubmit}
          onParsedValuesChange={() => setIsValueChange(true)}
          onValidChange={setIsValid}
          namespace="subscriptions"
        >
          <Button
            className="w-full"
            type="submit"
            disabled={!isValueChange || isSubmitting || !isValid}
          >
            {t("common.save", {
              ns: "common",
            })}
          </Button>
        </AutoForm>
        </>
      )}
    </div>
  );
}
