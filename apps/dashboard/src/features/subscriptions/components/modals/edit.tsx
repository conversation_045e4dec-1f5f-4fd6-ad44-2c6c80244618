import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import AutoForm from "@mass/shared/components/ui/auto-form/index";
import type { FieldConfig } from "@mass/shared/components/ui/auto-form/types";
import { Button } from "@mass/shared/components/ui/button";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useUpdateSubscription } from "../../data/queries";
import type { SubscriptionUpdateSchema } from "../../data/schema";
import { updateSubscriptionSchema } from "../../data/schema";
import { Subscription } from "../columns";

interface Props extends ModalProps {
  onHide: () => void;
  activeTab: "subscriptions" | "facilities";
  subscriptionId: string;
}

export default function Edit({
  onHide,
  activeTab,
  subscription,
}: Props & {
  title?: string;
  description?: string;
  subscription: Subscription;
}) {
  const { t } = useTranslation("subscriptions");
  const [isValueChange, setIsValueChange] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const subscriptionId = subscription.id;

  const updateMutation = useUpdateSubscription();

  const handleSubmit = async (values: SubscriptionUpdateSchema) => {
    updateMutation.mutate(
      { id: subscriptionId, data: values },
      {
        onSuccess: () => onHide(),
        onError: (error: any) => {
          setError(error.message);
        },
      }
    );
  };

  const fieldConfig: FieldConfig<SubscriptionUpdateSchema> = {
    name: {
      label: t("subscription_name"),
      inputProps: {
        placeholder: t("subscription_name_placeholder"),
      },
    },
  };

  const isSubmitting = updateMutation.isPending;

  return (
    <div className="p-6 pt-4 md:pt-0 space-y-4">
      {error ? (
        <div className="text-red-500 text-sm">
          {t("error." + encodeURIComponent(error))}
        </div>
      ) : null}
      <AutoForm
        formSchema={updateSubscriptionSchema}
        fieldConfig={fieldConfig as any} // todo: any
        onSubmit={handleSubmit}
        onParsedValuesChange={() => setIsValueChange(true)}
      >
        <Button
          className="w-full"
          type="submit"
          disabled={!isValueChange || isSubmitting}
        >
          {t("common.save", {
            ns: "common",
          })}
        </Button>
      </AutoForm>
    </div>
  );
}
