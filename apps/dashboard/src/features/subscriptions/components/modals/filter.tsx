import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import FeaturedIcon from "@mass/shared/components/atoms/featured-icon";
import { Button } from "@mass/shared/components/ui/button";
import { useTranslation } from "react-i18next";
import { useRegions } from "../../data/queries";
import { Label } from "@mass/shared/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mass/shared/components/ui/select";
import {
  useForm,
  FormProvider,
  FieldValues,
  ControllerRenderProps,
} from "react-hook-form";
import AutoFormSearchEnum from "@mass/shared/components/ui/auto-form/fields/search-enum";
import { subscriptionService } from "@/services/api/subscriptions";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm?: () => void | Promise<void>;
  activeTab: "subscriptions" | "facilities";
}

// Form schema for filtering
const filterSchema = z.object({
  type: z.string().default("all"),
  facilityType: z.string().default("all"),
  distributionCompany: z.string().optional(),
});

type FilterFormValues = z.infer<typeof filterSchema>;

export default function Filter({
  onHide,
  onConfirm,
  currentFilters,
  clearFilters,
}: Props & {
  title?: string;
  description?: string;
  currentFilters?: any;
  clearFilters: () => void;
}) {
  const { t } = useTranslation("subscriptions");

  const initialDistributionCompany =
    Array.isArray(currentFilters?.distributionCompany) &&
    currentFilters?.distributionCompany.length > 0
      ? currentFilters?.distributionCompany[0]
      : currentFilters?.distributionCompany || "";

  const form = useForm<FilterFormValues>({
    resolver: zodResolver(filterSchema),
    defaultValues: {
      type: currentFilters?.type ?? "all",
      facilityType: currentFilters?.facilityType ?? "all",
      distributionCompany: initialDistributionCompany,
    },
  });

  const type = form.watch("type");
  const facilityType = form.watch("facilityType");
  const distributionCompany = form.watch("distributionCompany");

  const handleSubmit = () => {
    const values = form.getValues();
    const transformedDistributionCompany = values.distributionCompany
      ? !Array.isArray(values.distributionCompany)
        ? [values.distributionCompany]
        : values.distributionCompany
      : [];
    (onConfirm as any)?.({
      type: values.type,
      facilityType: values.facilityType,
      distributionCompany: transformedDistributionCompany,
    });
    onHide();
  };

  const distributionField: ControllerRenderProps<FieldValues, any> = {
    value: distributionCompany,
    onChange: (value: string) => form.setValue("distributionCompany", value),
    onBlur: () => {},
    name: "distributionCompany",
    ref: () => {},
  };

  return (
    <FormProvider {...form}>
      <div className="p-6 pt-4 flex flex-col gap-5">
        <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-lg text-gray-900 font-text-sm-regular">
          <FeaturedIcon name="untitled:filter-lines" className="-mt-16" />
          <div className="self-stretch relative leading-[28px] font-semibold">
            {t("common.filter", {
              ns: "common",
            })}
          </div>
        </div>
        <div className="">
          <Label>{t("subscription_type")}</Label>
          <Select
            value={type}
            onValueChange={(value) => form.setValue("type", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={t("subscription_type")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("all")}</SelectItem>
              <SelectItem value="individual">
                {t("subscription_type_individual")}
              </SelectItem>
              <SelectItem value="corporate">
                {t("subscription_type_corporate")}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="">
          <Label>{t("facility_type")}</Label>
          <Select
            value={facilityType}
            onValueChange={(value) => form.setValue("facilityType", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={t("facility_type")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("all")}</SelectItem>
              <SelectItem value="electricity-production">
                {t("electricity_production")}
              </SelectItem>
              <SelectItem value="electricity-consumption">
                {t("electricity_consumption")}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="">
          <AutoFormSearchEnum
            label={t("distribution_company")}
            isRequired={false}
            field={distributionField}
            fieldConfigItem={{
              options: async (page: number, search: string) => {
                const result = await subscriptionService.getRegionsPaginated(
                  page,
                  10,
                  search
                );

                return {
                  items: result.items.map((region) => ({
                    label: region.name,
                    value: region.id,
                  })),
                  // nextPage must be a number: if no more pages, return current page
                  nextPage: result.hasNextPage ? page + 1 : page,
                };
              },
              selectedOptionLabel: async (value: string): Promise<string> => {
                if (!value) return "";
                const region = await subscriptionService.getRegionById(value);
                return region?.name || "";
              },
              inputProps: {
                placeholder: t("distribution_company"),
                emptyText: t("common.no_regions_found", { ns: "common" }),
                searchText: t("common.search_regions", { ns: "common" }),
              } as any, // todo: any
            }}
            zodItem={filterSchema.shape.distributionCompany as any} // todo: any
            zodInputProps={{}}
            fieldProps={{
              placeholder: t("distribution_company"),
              emptyText: t("common.no_regions_found", { ns: "common" }),
              searchText: t("common.search_regions", { ns: "common" }),
            }}
          />
        </div>
        <div className="w-full flex gap-2 items-center">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              clearFilters();
              onHide();
            }}
          >
            {t("common.clear_filters", {
              ns: "common",
            })}
          </Button>
          <Button className="w-full" onClick={handleSubmit}>
            {t("common.filter", {
              ns: "common",
            })}
          </Button>
        </div>
      </div>
    </FormProvider>
  );
}
