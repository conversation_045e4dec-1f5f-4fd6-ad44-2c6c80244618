import type { Subscription } from "@/features/subscriptions/components/columns";
import type {
  SubscriptionCreateSchema,
  SubscriptionUpdateSchema,
} from "@/features/subscriptions/data/schema";
import type { DetailSubscription } from "@/features/subscriptions/pages/detail";
import api from "../api";
import { Api } from "../enums";
import type { ApiQueryParams, PagedResponse } from "../types";
import { buildApiQueryString } from "../types";

export interface Region {
  id: string;
  name: string;
}

let regionsCache: Region[] | null = null;
let regionsCacheTimestamp: number = 0;
const CACHE_TTL = 10 * 60 * 1000;

export const subscriptionService = {
  async getAll(params?: ApiQueryParams): Promise<PagedResponse<Subscription>> {
    try {
      const queryParams = params || {
        pageNumber: 1,
        pageSize: 10,
        orderBy: "createdAt:asc",
      };

      if (params && !params.orderBy) {
        queryParams.orderBy = "createdAt:asc";
      }

      const queryString = buildApiQueryString(queryParams);

      const response = await api(`${Api.subscriptions}?${queryString}`, {
        method: "GET",
      });
      return response;
    } catch (error) {
      console.error("Error fetching subscriptions:", error);
      throw error;
    }
  },

  async getById(id: string): Promise<DetailSubscription> {
    try {
      const response = await api(`${Api.subscriptions}/${id}`, {
        method: "GET",
      });
      return response;
    } catch (error) {
      console.error(`Error fetching subscription ${id}:`, error);
      throw error;
    }
  },

  async create(
    data: SubscriptionCreateSchema & {
      facilityType: string;
      tckn?: string;
      vkn?: string;
    }
  ): Promise<Subscription> {
    try {
      const response = await api(`${Api.subscriptions}`, {
        method: "POST",
        body: JSON.stringify({
          name: data.name,
          individual: data.type === "individual",
          personIdentifier: data.type === "individual" ? data.tckn : data.vkn,
          regionId: data.distributionCompany,
          installationId: data.installationId,
        }),
      });
      return response.data;
    } catch (error) {
      console.error("Error creating subscription:", error);
      throw error;
    }
  },

  async update(
    id: string,
    data: SubscriptionUpdateSchema
  ): Promise<Subscription> {
    try {
      const response = await api(`${Api.subscriptions}/${id}`, {
        method: "PATCH",
        body: JSON.stringify(data),
      });

      return response.data;
    } catch (error) {
      console.error(`Error updating subscription ${id}:`, error);
      throw error;
    }
  },

  async updateNotificationSettings(
    id: string,
    data: { unexpectedUsageThreshold: number; userDefinedLimit: number }
  ): Promise<void> {
    try {
      await api(`${Api.subscriptions}/${id}/notification`, {
        method: "PATCH",
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error(`Error updating subscription ${id} notifications:`, error);
      throw error;
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await api(`${Api.subscriptions}/${id}`, {
        method: "DELETE",
      });
    } catch (error) {
      console.error(`Error deleting subscription ${id}:`, error);
      throw error;
    }
  },

  async getUsageData(
    id: string,
    startDate: Date,
    endDate: Date,
    granularity: string,
    compareTo: string[]
  ): Promise<any> {
    try {
      let url = `${Api.subscriptions}/${id}/usage?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&granularity=${encodeURIComponent(granularity)}`;
      for (const compare of compareTo) {
        url += `&compareTo=${encodeURIComponent(compare)}`;
      }

      const response = await api(url, {
        method: "GET",
      });
      interface UsageDataItem {
        timeframe: string;
        [key: string]: any;
      }

      return {
        ...response,

        usageData: response.usageData.map((item: UsageDataItem) => ({
          ...item,
          timeframe: new Date(item.timeframe),
        })),
        compare: response.compare,
        requestedGranularity: granularity,
        requestedCompareTo: compareTo,
        requestedStartDate: new Date(startDate),
        requestedEndDate: new Date(endDate),
      };
    } catch (error) {
      console.error("Error fetching usage data:", error);
      throw error;
    }
  },

  async exportUsageData(
    id: string,
    startDate: Date,
    endDate: Date,
    granularity: string,
    compareTo: string[],
    fileType: string,
    language: string
  ): Promise<any> {
    try {
      let url = `${Api.subscriptions}/${id}/usage/export?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&granularity=${encodeURIComponent(granularity)}&fileType=${fileType}&language=${language}`;
      for (const compare of compareTo) {
        url += `&compareTo=${encodeURIComponent(compare)}`;
      }

      const response = await api(url, {
        method: "POST",
      });
      
      return response;
    } catch (error) {
      console.error("Error exporting usage data:", error);
      throw error;
    }
  },

  async exportOutageData(
    id: string,
    startDate: Date,
    endDate: Date,
    fileType: string,
    language: string
  ): Promise<any> {
    try {
      let url = `${Api.subscriptions}/${id}/outages/export?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&fileType=${fileType}&language=${language}`;

      const response = await api(url, {
        method: "POST",
      });
      
      return response;
    } catch (error) {
      console.error("Error exporting outage data:", error);
      throw error;
    }
  },

  async getRegions(): Promise<Region[]> {
    try {
      const now = Date.now();
      if (regionsCache && now - regionsCacheTimestamp < CACHE_TTL) {
        return regionsCache;
      }

      const response = await api(Api.regions, {
        method: "GET",
      });

      regionsCache = response;
      regionsCacheTimestamp = now;

      return response;
    } catch (error) {
      console.error("Error fetching regions:", error);
      throw error;
    }
  },

  async getRegionsPaginated(
    page: number = 0,
    pageSize: number = 10,
    search?: string
  ): Promise<{
    items: Region[];
    totalPages: number;
    totalElements: number;
    hasNextPage: boolean;
  }> {
    try {
      const allRegions = await this.getRegions();

      const normalizeText = (text: string) =>
        text
          .toLowerCase()
          .trim()
          .normalize("NFD")
          .replace(/\p{Diacritic}/gu, "")
          .replace(/ı/g, "i")
          .replace(/ğ/g, "g")
          .replace(/ü/g, "u")
          .replace(/ş/g, "s")
          .replace(/ö/g, "o")
          .replace(/ç/g, "c");

      let filteredRegions = allRegions;
      if (search && search.trim()) {
        const normalizedSearch = normalizeText(search);
        const exact: Region[] = [];
        const partial: Region[] = [];
        filteredRegions.forEach((r) => {
          const nameNorm = normalizeText(r.name);
          if (nameNorm === normalizedSearch) exact.push(r);
          else if (nameNorm.includes(normalizedSearch)) partial.push(r);
        });
        filteredRegions = [...exact, ...partial];
      }

      const totalElements = filteredRegions.length;
      const totalPages = Math.ceil(totalElements / pageSize);
      const start = page * pageSize;
      const end = Math.min(start + pageSize, totalElements);
      const items = filteredRegions.slice(start, end);

      return {
        items,
        totalPages,
        totalElements,
        hasNextPage: page < totalPages - 1,
      };
    } catch (error) {
      console.error("Error fetching paginated regions:", error);
      throw error;
    }
  },

  async getRegionById(id: string): Promise<Region | undefined> {
    try {
      if (!id) return undefined;

      const now = Date.now();
      if (regionsCache && now - regionsCacheTimestamp < CACHE_TTL) {
        return regionsCache.find((r) => r.id === id);
      }

      const regions = await this.getRegions();
      return regions.find((r) => r.id === id);
    } catch (error) {
      console.error(`Error fetching region with ID ${id}:`, error);
      throw error;
    }
  },
};
