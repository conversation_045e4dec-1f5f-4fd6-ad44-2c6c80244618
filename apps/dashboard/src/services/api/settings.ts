import api from "../api";
import { Api } from "../enums";

export const settingsService = {
  getNotificationSettings: async () => {
    const response = await api(
      `${Api.settings}/user/notification.preferences`,
      {
        method: "GET",
      }
    );
    return response.value;
  },
  setNotificationSettings: async (data: any) => {
    await api(`${Api.settings}/user/notification.preferences`, {
      method: "PATCH",
      body: JSON.stringify({ value: data }),
    });
    return;
  },
  getGlobalSetting: async (key: string) => {
    try {
      const response = await api(
        `${Api.settings}/global/${key}`,
        {
          method: "GET",
        }
      );
      return response.value;
    } catch (error) {
      console.error(`Error fetching global setting [${key}]:`, error);
      throw error;
    }
  },
};
