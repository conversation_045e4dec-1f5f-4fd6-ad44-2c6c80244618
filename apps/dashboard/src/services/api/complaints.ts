import api from "../api";
import type { PagedResponse, ApiQueryParams } from "../types";
import { buildApiQueryString } from "../types";

export interface ComplaintPayload {
  body: string;
  category: string;
  subcategory: string;
  subscriptionId: string;
  files: string[];
}

export async function fetchComplaints(
  params: ApiQueryParams
): Promise<PagedResponse<any>> {
  try {
    const queryString = buildApiQueryString(params);

    const response = await api(`/complaint?${queryString}`, { method: "GET" });
    if (!response) {
      throw new Error("No response from server");
    }
    return {
      ...response,
      content: await Promise.all(
        response.content.map(async (complaint: ComplaintPayload) => ({
          ...complaint,
          subscription: await api(`/subscription/${complaint.subscriptionId}`),
        }))
      ),
    };
  } catch (error) {
    console.error("Error fetching complaints:", error);
    throw error;
  }
}

export async function fetchComplaintById(id: string) {
  try {
    const response = await api(`/complaint/${id}`);
    if (!response) {
      throw new Error("No response from server");
    }
    return response;
  } catch (error) {
    console.error(`Error fetching complaint by ID (${id}):`, error);
    throw error;
  }
}

export async function createComplaint(payload: ComplaintPayload) {
  return api("/complaint", {
    method: "POST",
    body: JSON.stringify(payload),
  });
}

export async function uploadDocument(file: File) {
  const payload = {
    name: file.name,
    size: file.size,
    mimeType: file.type,
  };

  return api("/document", {
    method: "POST",
    body: JSON.stringify(payload),
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function fetchDocument(fileId: string) {
  return api(`/document/${fileId}`);
}

export async function markDocumentAsDone(fileId: string) {
  return api(`/document/${fileId}/done`, {
    method: "PATCH",
  });
}

export async function cancelComplaint(complaintId: string) {
  return api(`/complaint/${complaintId}/cancel`, {
    method: "PATCH",
  });
}
