import api from "../api";
import { Api } from "../enums";

export interface Permission {
  scope: string;
}
export interface MeResponse {
  id: string;
  permissions: Permission[];
  email: string;
  phone: string;
  tckn: string;
  firstName: string;
  lastName: string;
}

export interface LoginResponse {
  token?: string;
  user?: {
    id: string;
    email: string;
    name?: string;
  };
  session?: boolean;
  message?: string;
}

export interface RegisterResponse {
  success: boolean;
  message?: string;
  userId?: string;
}

export const authService = {
  async me(): Promise<MeResponse> {
    try {
      const result = await api(`${Api.auth}/me`, {
        method: "GET",
      });

      return result;
    } catch (error) {
      console.error("Error during me:", error);
      throw error;
    }
  },

  async login(data: {
    email: string;
    password: string;
  }): Promise<LoginResponse> {
    try {
      const result = await api(`${Api.auth}/login`, {
        method: "POST",
        body: JSON.stringify(data),
      });

      return result;
    } catch (error) {
      console.error("Error during login:", error);
      throw error;
    }
  },

  async register(data: {
    email: string;
    password: string;
    phone?: string;
    tckn?: string;
  }): Promise<RegisterResponse> {
    try {
      const result = await api(`${Api.auth}/register`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return result;
    } catch (error) {
      console.error("Error during registration:", error);
      throw error;
    }
  },

  async forgotPassword(data: {
    email: string;
  }): Promise<{ success: boolean; message?: string }> {
    try {
      const result = await api(`${Api.auth}/forgot-password`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return result;
    } catch (error) {
      console.error("Error requesting password reset:", error);
      throw error;
    }
  },

  async resetPassword(data: {
    token: string;
    password: string;
  }): Promise<{ success: boolean; message?: string }> {
    try {
      const result = await api(`${Api.auth}/reset-password`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return result;
    } catch (error) {
      console.error("Error resetting password:", error);
      throw error;
    }
  },

  async verifyOtp(data: {
    email: string;
    code: string;
  }): Promise<{ success: boolean; message?: string }> {
    try {
      const result = await api(`${Api.auth}/verify-otp`, {
        method: "POST",
        body: JSON.stringify(data),
      });
      return result;
    } catch (error) {
      console.error("Error verifying OTP:", error);
      throw error;
    }
  },

  async logout(): Promise<void> {
    try {
      await api(`${Api.auth}/logout`, {
        method: "DELETE",
      });
    } catch (error) {
      console.error("Error during logout:", error);
      throw error;
    }
  },
  
  async deleteAccount(): Promise<void> {
    try {
      await api(`${Api.auth}/account/delete`, {
        method: "DELETE",
      });
    } catch (error) {
      console.error("Error deleting account:", error);
      throw error;
    }
  },
};
