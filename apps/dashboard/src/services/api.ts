import i18next from "i18next";
import { toast } from "sonner";

const baseURL = "/api";

class RateLimitError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "RateLimitError";
  }
}

let rateLimitToastShown = false;

async function api(endpoint: string, options: RequestInit = {}) {
  const url = baseURL + endpoint;

  const defaultOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    credentials: "include",
  };

  const mergedOptions: RequestInit = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...(options.headers || {}),
    },
  };

  if (import.meta.env.DEV) {
    console.log(
      `📤 [API Request] ${(mergedOptions.method || "GET").toUpperCase()} ${url}`,
      mergedOptions
    );
  }

  // todo: this is terrible. use a proper promise from the fetch

  try {
    const response = await fetch(url, mergedOptions);

    if (import.meta.env.DEV) {
      console.log(
        `📥 [API Response] ${(mergedOptions.method || "GET").toUpperCase()} ${url}`,
        {
          status: response.status,
          statusText: response.statusText,
        }
      );
    }

    if (!response.ok) {
      if (response.status === 401 || response.status === 403) {
        window.location.href = "/login";

        return null;
      }

      if (response.status === 429) {
        const retryAfter = response.headers.get("Retry-After");
        const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : 60000;

        if (!rateLimitToastShown) {
          toast.error(
            i18next.t("common:api.errors.rate_limit_server", {
              seconds: Math.ceil(waitTime / 1000),
              defaultValue:
                "Rate limit reached. Request aborted. Try again in {{seconds}} seconds.",
            })
          );
          rateLimitToastShown = true;
          setTimeout(() => {
            rateLimitToastShown = false;
          }, waitTime);
        }

        throw new RateLimitError("SERVER_RATE_LIMIT_ABORT");
      }

      if (response.status >= 500 && response.status < 600) {
        toast.error(i18next.t("common:api.errors.default"));
      }

      const errorData = await response.json().catch(() => ({}));
      const errorMessage =
        errorData.error || `${response.status}: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      return await response.json();
    }

    return await response.text();
  } catch (error) {
    if (error instanceof RateLimitError) {
      throw error;
    }

    if (error instanceof Error) {
      if (error.name === "TypeError") {
        toast.error(i18next.t("common:api.errors.network_error"));
      }
    }
    throw error;
  }
}

export { RateLimitError };
export default api;
