/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as AuthenticatedLogoutImport } from './routes/_authenticated/logout'
import { Route as AuthenticatedComplaintsRequestsIndexImport } from './routes/_authenticated/complaints-requests/index'
import { Route as AuthenticatedSubscriptionsSubscriptionIdIndexImport } from './routes/_authenticated/subscriptions/$subscriptionId/index'
import { Route as AuthenticatedSubscriptionsSubscriptionIdOutagesImport } from './routes/_authenticated/subscriptions/$subscriptionId/outages'
import { Route as AuthenticatedSubscriptionsSubscriptionIdNotificationsImport } from './routes/_authenticated/subscriptions/$subscriptionId/notifications'
import { Route as AuthenticatedSubscriptionsSubscriptionIdDataImport } from './routes/_authenticated/subscriptions/$subscriptionId/data'

// Create Virtual Routes

const errorsErrorLazyImport = createFileRoute('/(errors)/$error')()
const authAuthLazyImport = createFileRoute('/(auth)/$auth')()
const AuthenticatedComplaintsRequestsRouteLazyImport = createFileRoute(
  '/_authenticated/complaints-requests',
)()
const AuthenticatedSubscriptionsIndexLazyImport = createFileRoute(
  '/_authenticated/subscriptions/',
)()
const AuthenticatedSettingsIndexLazyImport = createFileRoute(
  '/_authenticated/settings/',
)()
const AuthenticatedNotificationsIndexLazyImport = createFileRoute(
  '/_authenticated/notifications/',
)()
const AuthenticatedSettingsNotificationLazyImport = createFileRoute(
  '/_authenticated/settings/notification',
)()
const AuthenticatedNotificationsArchivedLazyImport = createFileRoute(
  '/_authenticated/notifications/archived',
)()
const AuthenticatedHelpCenterPageLazyImport = createFileRoute(
  '/_authenticated/help-center/$page',
)()
const AuthenticatedComplaintsRequestsComplaintIdLazyImport = createFileRoute(
  '/_authenticated/complaints-requests/$complaintId',
)()

// Create/Update Routes

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errorsErrorLazyRoute = errorsErrorLazyImport
  .update({
    id: '/(errors)/$error',
    path: '/$error',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/$error.lazy').then((d) => d.Route))

const authAuthLazyRoute = authAuthLazyImport
  .update({
    id: '/(auth)/$auth',
    path: '/$auth',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(auth)/$auth.lazy').then((d) => d.Route))

const AuthenticatedComplaintsRequestsRouteLazyRoute =
  AuthenticatedComplaintsRequestsRouteLazyImport.update({
    id: '/complaints-requests',
    path: '/complaints-requests',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/complaints-requests/route.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedLogoutRoute = AuthenticatedLogoutImport.update({
  id: '/logout',
  path: '/logout',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const AuthenticatedSubscriptionsIndexLazyRoute =
  AuthenticatedSubscriptionsIndexLazyImport.update({
    id: '/subscriptions/',
    path: '/subscriptions/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/subscriptions/index.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedSettingsIndexLazyRoute =
  AuthenticatedSettingsIndexLazyImport.update({
    id: '/settings/',
    path: '/settings/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/settings/index.lazy').then((d) => d.Route),
  )

const AuthenticatedNotificationsIndexLazyRoute =
  AuthenticatedNotificationsIndexLazyImport.update({
    id: '/notifications/',
    path: '/notifications/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/notifications/index.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedComplaintsRequestsIndexRoute =
  AuthenticatedComplaintsRequestsIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedComplaintsRequestsRouteLazyRoute,
  } as any)

const AuthenticatedSettingsNotificationLazyRoute =
  AuthenticatedSettingsNotificationLazyImport.update({
    id: '/settings/notification',
    path: '/settings/notification',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/settings/notification.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedNotificationsArchivedLazyRoute =
  AuthenticatedNotificationsArchivedLazyImport.update({
    id: '/notifications/archived',
    path: '/notifications/archived',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/notifications/archived.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedHelpCenterPageLazyRoute =
  AuthenticatedHelpCenterPageLazyImport.update({
    id: '/help-center/$page',
    path: '/help-center/$page',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/help-center/$page.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedComplaintsRequestsComplaintIdLazyRoute =
  AuthenticatedComplaintsRequestsComplaintIdLazyImport.update({
    id: '/$complaintId',
    path: '/$complaintId',
    getParentRoute: () => AuthenticatedComplaintsRequestsRouteLazyRoute,
  } as any).lazy(() =>
    import(
      './routes/_authenticated/complaints-requests/$complaintId.lazy'
    ).then((d) => d.Route),
  )

const AuthenticatedSubscriptionsSubscriptionIdIndexRoute =
  AuthenticatedSubscriptionsSubscriptionIdIndexImport.update({
    id: '/subscriptions/$subscriptionId/',
    path: '/subscriptions/$subscriptionId/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSubscriptionsSubscriptionIdOutagesRoute =
  AuthenticatedSubscriptionsSubscriptionIdOutagesImport.update({
    id: '/subscriptions/$subscriptionId/outages',
    path: '/subscriptions/$subscriptionId/outages',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSubscriptionsSubscriptionIdNotificationsRoute =
  AuthenticatedSubscriptionsSubscriptionIdNotificationsImport.update({
    id: '/subscriptions/$subscriptionId/notifications',
    path: '/subscriptions/$subscriptionId/notifications',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

const AuthenticatedSubscriptionsSubscriptionIdDataRoute =
  AuthenticatedSubscriptionsSubscriptionIdDataImport.update({
    id: '/subscriptions/$subscriptionId/data',
    path: '/subscriptions/$subscriptionId/data',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/logout': {
      id: '/_authenticated/logout'
      path: '/logout'
      fullPath: '/logout'
      preLoaderRoute: typeof AuthenticatedLogoutImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/complaints-requests': {
      id: '/_authenticated/complaints-requests'
      path: '/complaints-requests'
      fullPath: '/complaints-requests'
      preLoaderRoute: typeof AuthenticatedComplaintsRequestsRouteLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/(auth)/$auth': {
      id: '/(auth)/$auth'
      path: '/$auth'
      fullPath: '/$auth'
      preLoaderRoute: typeof authAuthLazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/$error': {
      id: '/(errors)/$error'
      path: '/$error'
      fullPath: '/$error'
      preLoaderRoute: typeof errorsErrorLazyImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/complaints-requests/$complaintId': {
      id: '/_authenticated/complaints-requests/$complaintId'
      path: '/$complaintId'
      fullPath: '/complaints-requests/$complaintId'
      preLoaderRoute: typeof AuthenticatedComplaintsRequestsComplaintIdLazyImport
      parentRoute: typeof AuthenticatedComplaintsRequestsRouteLazyImport
    }
    '/_authenticated/help-center/$page': {
      id: '/_authenticated/help-center/$page'
      path: '/help-center/$page'
      fullPath: '/help-center/$page'
      preLoaderRoute: typeof AuthenticatedHelpCenterPageLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/notifications/archived': {
      id: '/_authenticated/notifications/archived'
      path: '/notifications/archived'
      fullPath: '/notifications/archived'
      preLoaderRoute: typeof AuthenticatedNotificationsArchivedLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/notification': {
      id: '/_authenticated/settings/notification'
      path: '/settings/notification'
      fullPath: '/settings/notification'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/complaints-requests/': {
      id: '/_authenticated/complaints-requests/'
      path: '/'
      fullPath: '/complaints-requests/'
      preLoaderRoute: typeof AuthenticatedComplaintsRequestsIndexImport
      parentRoute: typeof AuthenticatedComplaintsRequestsRouteLazyImport
    }
    '/_authenticated/notifications/': {
      id: '/_authenticated/notifications/'
      path: '/notifications'
      fullPath: '/notifications'
      preLoaderRoute: typeof AuthenticatedNotificationsIndexLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsIndexLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/subscriptions/': {
      id: '/_authenticated/subscriptions/'
      path: '/subscriptions'
      fullPath: '/subscriptions'
      preLoaderRoute: typeof AuthenticatedSubscriptionsIndexLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/subscriptions/$subscriptionId/data': {
      id: '/_authenticated/subscriptions/$subscriptionId/data'
      path: '/subscriptions/$subscriptionId/data'
      fullPath: '/subscriptions/$subscriptionId/data'
      preLoaderRoute: typeof AuthenticatedSubscriptionsSubscriptionIdDataImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/subscriptions/$subscriptionId/notifications': {
      id: '/_authenticated/subscriptions/$subscriptionId/notifications'
      path: '/subscriptions/$subscriptionId/notifications'
      fullPath: '/subscriptions/$subscriptionId/notifications'
      preLoaderRoute: typeof AuthenticatedSubscriptionsSubscriptionIdNotificationsImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/subscriptions/$subscriptionId/outages': {
      id: '/_authenticated/subscriptions/$subscriptionId/outages'
      path: '/subscriptions/$subscriptionId/outages'
      fullPath: '/subscriptions/$subscriptionId/outages'
      preLoaderRoute: typeof AuthenticatedSubscriptionsSubscriptionIdOutagesImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/subscriptions/$subscriptionId/': {
      id: '/_authenticated/subscriptions/$subscriptionId/'
      path: '/subscriptions/$subscriptionId'
      fullPath: '/subscriptions/$subscriptionId'
      preLoaderRoute: typeof AuthenticatedSubscriptionsSubscriptionIdIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedComplaintsRequestsRouteLazyRouteChildren {
  AuthenticatedComplaintsRequestsComplaintIdLazyRoute: typeof AuthenticatedComplaintsRequestsComplaintIdLazyRoute
  AuthenticatedComplaintsRequestsIndexRoute: typeof AuthenticatedComplaintsRequestsIndexRoute
}

const AuthenticatedComplaintsRequestsRouteLazyRouteChildren: AuthenticatedComplaintsRequestsRouteLazyRouteChildren =
  {
    AuthenticatedComplaintsRequestsComplaintIdLazyRoute:
      AuthenticatedComplaintsRequestsComplaintIdLazyRoute,
    AuthenticatedComplaintsRequestsIndexRoute:
      AuthenticatedComplaintsRequestsIndexRoute,
  }

const AuthenticatedComplaintsRequestsRouteLazyRouteWithChildren =
  AuthenticatedComplaintsRequestsRouteLazyRoute._addFileChildren(
    AuthenticatedComplaintsRequestsRouteLazyRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedLogoutRoute: typeof AuthenticatedLogoutRoute
  AuthenticatedComplaintsRequestsRouteLazyRoute: typeof AuthenticatedComplaintsRequestsRouteLazyRouteWithChildren
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedHelpCenterPageLazyRoute: typeof AuthenticatedHelpCenterPageLazyRoute
  AuthenticatedNotificationsArchivedLazyRoute: typeof AuthenticatedNotificationsArchivedLazyRoute
  AuthenticatedSettingsNotificationLazyRoute: typeof AuthenticatedSettingsNotificationLazyRoute
  AuthenticatedNotificationsIndexLazyRoute: typeof AuthenticatedNotificationsIndexLazyRoute
  AuthenticatedSettingsIndexLazyRoute: typeof AuthenticatedSettingsIndexLazyRoute
  AuthenticatedSubscriptionsIndexLazyRoute: typeof AuthenticatedSubscriptionsIndexLazyRoute
  AuthenticatedSubscriptionsSubscriptionIdDataRoute: typeof AuthenticatedSubscriptionsSubscriptionIdDataRoute
  AuthenticatedSubscriptionsSubscriptionIdNotificationsRoute: typeof AuthenticatedSubscriptionsSubscriptionIdNotificationsRoute
  AuthenticatedSubscriptionsSubscriptionIdOutagesRoute: typeof AuthenticatedSubscriptionsSubscriptionIdOutagesRoute
  AuthenticatedSubscriptionsSubscriptionIdIndexRoute: typeof AuthenticatedSubscriptionsSubscriptionIdIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedLogoutRoute: AuthenticatedLogoutRoute,
  AuthenticatedComplaintsRequestsRouteLazyRoute:
    AuthenticatedComplaintsRequestsRouteLazyRouteWithChildren,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedHelpCenterPageLazyRoute: AuthenticatedHelpCenterPageLazyRoute,
  AuthenticatedNotificationsArchivedLazyRoute:
    AuthenticatedNotificationsArchivedLazyRoute,
  AuthenticatedSettingsNotificationLazyRoute:
    AuthenticatedSettingsNotificationLazyRoute,
  AuthenticatedNotificationsIndexLazyRoute:
    AuthenticatedNotificationsIndexLazyRoute,
  AuthenticatedSettingsIndexLazyRoute: AuthenticatedSettingsIndexLazyRoute,
  AuthenticatedSubscriptionsIndexLazyRoute:
    AuthenticatedSubscriptionsIndexLazyRoute,
  AuthenticatedSubscriptionsSubscriptionIdDataRoute:
    AuthenticatedSubscriptionsSubscriptionIdDataRoute,
  AuthenticatedSubscriptionsSubscriptionIdNotificationsRoute:
    AuthenticatedSubscriptionsSubscriptionIdNotificationsRoute,
  AuthenticatedSubscriptionsSubscriptionIdOutagesRoute:
    AuthenticatedSubscriptionsSubscriptionIdOutagesRoute,
  AuthenticatedSubscriptionsSubscriptionIdIndexRoute:
    AuthenticatedSubscriptionsSubscriptionIdIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/logout': typeof AuthenticatedLogoutRoute
  '/complaints-requests': typeof AuthenticatedComplaintsRequestsRouteLazyRouteWithChildren
  '/$auth': typeof authAuthLazyRoute
  '/$error': typeof errorsErrorLazyRoute
  '/': typeof AuthenticatedIndexRoute
  '/complaints-requests/$complaintId': typeof AuthenticatedComplaintsRequestsComplaintIdLazyRoute
  '/help-center/$page': typeof AuthenticatedHelpCenterPageLazyRoute
  '/notifications/archived': typeof AuthenticatedNotificationsArchivedLazyRoute
  '/settings/notification': typeof AuthenticatedSettingsNotificationLazyRoute
  '/complaints-requests/': typeof AuthenticatedComplaintsRequestsIndexRoute
  '/notifications': typeof AuthenticatedNotificationsIndexLazyRoute
  '/settings': typeof AuthenticatedSettingsIndexLazyRoute
  '/subscriptions': typeof AuthenticatedSubscriptionsIndexLazyRoute
  '/subscriptions/$subscriptionId/data': typeof AuthenticatedSubscriptionsSubscriptionIdDataRoute
  '/subscriptions/$subscriptionId/notifications': typeof AuthenticatedSubscriptionsSubscriptionIdNotificationsRoute
  '/subscriptions/$subscriptionId/outages': typeof AuthenticatedSubscriptionsSubscriptionIdOutagesRoute
  '/subscriptions/$subscriptionId': typeof AuthenticatedSubscriptionsSubscriptionIdIndexRoute
}

export interface FileRoutesByTo {
  '/logout': typeof AuthenticatedLogoutRoute
  '/$auth': typeof authAuthLazyRoute
  '/$error': typeof errorsErrorLazyRoute
  '/': typeof AuthenticatedIndexRoute
  '/complaints-requests/$complaintId': typeof AuthenticatedComplaintsRequestsComplaintIdLazyRoute
  '/help-center/$page': typeof AuthenticatedHelpCenterPageLazyRoute
  '/notifications/archived': typeof AuthenticatedNotificationsArchivedLazyRoute
  '/settings/notification': typeof AuthenticatedSettingsNotificationLazyRoute
  '/complaints-requests': typeof AuthenticatedComplaintsRequestsIndexRoute
  '/notifications': typeof AuthenticatedNotificationsIndexLazyRoute
  '/settings': typeof AuthenticatedSettingsIndexLazyRoute
  '/subscriptions': typeof AuthenticatedSubscriptionsIndexLazyRoute
  '/subscriptions/$subscriptionId/data': typeof AuthenticatedSubscriptionsSubscriptionIdDataRoute
  '/subscriptions/$subscriptionId/notifications': typeof AuthenticatedSubscriptionsSubscriptionIdNotificationsRoute
  '/subscriptions/$subscriptionId/outages': typeof AuthenticatedSubscriptionsSubscriptionIdOutagesRoute
  '/subscriptions/$subscriptionId': typeof AuthenticatedSubscriptionsSubscriptionIdIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/_authenticated/logout': typeof AuthenticatedLogoutRoute
  '/_authenticated/complaints-requests': typeof AuthenticatedComplaintsRequestsRouteLazyRouteWithChildren
  '/(auth)/$auth': typeof authAuthLazyRoute
  '/(errors)/$error': typeof errorsErrorLazyRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/complaints-requests/$complaintId': typeof AuthenticatedComplaintsRequestsComplaintIdLazyRoute
  '/_authenticated/help-center/$page': typeof AuthenticatedHelpCenterPageLazyRoute
  '/_authenticated/notifications/archived': typeof AuthenticatedNotificationsArchivedLazyRoute
  '/_authenticated/settings/notification': typeof AuthenticatedSettingsNotificationLazyRoute
  '/_authenticated/complaints-requests/': typeof AuthenticatedComplaintsRequestsIndexRoute
  '/_authenticated/notifications/': typeof AuthenticatedNotificationsIndexLazyRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexLazyRoute
  '/_authenticated/subscriptions/': typeof AuthenticatedSubscriptionsIndexLazyRoute
  '/_authenticated/subscriptions/$subscriptionId/data': typeof AuthenticatedSubscriptionsSubscriptionIdDataRoute
  '/_authenticated/subscriptions/$subscriptionId/notifications': typeof AuthenticatedSubscriptionsSubscriptionIdNotificationsRoute
  '/_authenticated/subscriptions/$subscriptionId/outages': typeof AuthenticatedSubscriptionsSubscriptionIdOutagesRoute
  '/_authenticated/subscriptions/$subscriptionId/': typeof AuthenticatedSubscriptionsSubscriptionIdIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/logout'
    | '/complaints-requests'
    | '/$auth'
    | '/$error'
    | '/'
    | '/complaints-requests/$complaintId'
    | '/help-center/$page'
    | '/notifications/archived'
    | '/settings/notification'
    | '/complaints-requests/'
    | '/notifications'
    | '/settings'
    | '/subscriptions'
    | '/subscriptions/$subscriptionId/data'
    | '/subscriptions/$subscriptionId/notifications'
    | '/subscriptions/$subscriptionId/outages'
    | '/subscriptions/$subscriptionId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/logout'
    | '/$auth'
    | '/$error'
    | '/'
    | '/complaints-requests/$complaintId'
    | '/help-center/$page'
    | '/notifications/archived'
    | '/settings/notification'
    | '/complaints-requests'
    | '/notifications'
    | '/settings'
    | '/subscriptions'
    | '/subscriptions/$subscriptionId/data'
    | '/subscriptions/$subscriptionId/notifications'
    | '/subscriptions/$subscriptionId/outages'
    | '/subscriptions/$subscriptionId'
  id:
    | '__root__'
    | '/_authenticated'
    | '/_authenticated/logout'
    | '/_authenticated/complaints-requests'
    | '/(auth)/$auth'
    | '/(errors)/$error'
    | '/_authenticated/'
    | '/_authenticated/complaints-requests/$complaintId'
    | '/_authenticated/help-center/$page'
    | '/_authenticated/notifications/archived'
    | '/_authenticated/settings/notification'
    | '/_authenticated/complaints-requests/'
    | '/_authenticated/notifications/'
    | '/_authenticated/settings/'
    | '/_authenticated/subscriptions/'
    | '/_authenticated/subscriptions/$subscriptionId/data'
    | '/_authenticated/subscriptions/$subscriptionId/notifications'
    | '/_authenticated/subscriptions/$subscriptionId/outages'
    | '/_authenticated/subscriptions/$subscriptionId/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  authAuthLazyRoute: typeof authAuthLazyRoute
  errorsErrorLazyRoute: typeof errorsErrorLazyRoute
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  authAuthLazyRoute: authAuthLazyRoute,
  errorsErrorLazyRoute: errorsErrorLazyRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/(auth)/$auth",
        "/(errors)/$error"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/logout",
        "/_authenticated/complaints-requests",
        "/_authenticated/",
        "/_authenticated/help-center/$page",
        "/_authenticated/notifications/archived",
        "/_authenticated/settings/notification",
        "/_authenticated/notifications/",
        "/_authenticated/settings/",
        "/_authenticated/subscriptions/",
        "/_authenticated/subscriptions/$subscriptionId/data",
        "/_authenticated/subscriptions/$subscriptionId/notifications",
        "/_authenticated/subscriptions/$subscriptionId/outages",
        "/_authenticated/subscriptions/$subscriptionId/"
      ]
    },
    "/_authenticated/logout": {
      "filePath": "_authenticated/logout.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/complaints-requests": {
      "filePath": "_authenticated/complaints-requests/route.lazy.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/complaints-requests/$complaintId",
        "/_authenticated/complaints-requests/"
      ]
    },
    "/(auth)/$auth": {
      "filePath": "(auth)/$auth.lazy.tsx"
    },
    "/(errors)/$error": {
      "filePath": "(errors)/$error.lazy.tsx"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/complaints-requests/$complaintId": {
      "filePath": "_authenticated/complaints-requests/$complaintId.lazy.tsx",
      "parent": "/_authenticated/complaints-requests"
    },
    "/_authenticated/help-center/$page": {
      "filePath": "_authenticated/help-center/$page.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/notifications/archived": {
      "filePath": "_authenticated/notifications/archived.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/notification": {
      "filePath": "_authenticated/settings/notification.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/complaints-requests/": {
      "filePath": "_authenticated/complaints-requests/index.tsx",
      "parent": "/_authenticated/complaints-requests"
    },
    "/_authenticated/notifications/": {
      "filePath": "_authenticated/notifications/index.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/": {
      "filePath": "_authenticated/settings/index.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/subscriptions/": {
      "filePath": "_authenticated/subscriptions/index.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/subscriptions/$subscriptionId/data": {
      "filePath": "_authenticated/subscriptions/$subscriptionId/data.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/subscriptions/$subscriptionId/notifications": {
      "filePath": "_authenticated/subscriptions/$subscriptionId/notifications.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/subscriptions/$subscriptionId/outages": {
      "filePath": "_authenticated/subscriptions/$subscriptionId/outages.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/subscriptions/$subscriptionId/": {
      "filePath": "_authenticated/subscriptions/$subscriptionId/index.tsx",
      "parent": "/_authenticated"
    }
  }
}
ROUTE_MANIFEST_END */
