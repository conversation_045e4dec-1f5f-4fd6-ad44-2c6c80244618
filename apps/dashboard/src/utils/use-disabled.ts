import api from "@/services/api";
import { useManyGlobalSettings } from "@mass/shared/hooks/use-global-settings";
import {
  SetStateAction,
  useState,
  Dispatch,
  useContext,
  useEffect,
  createContext,
} from "react";

export const DisabledContext = createContext({
  disabledMap: {} as Record<string, { disabled: boolean; lastFetched: Date }>,
  setDisabledMap: (() => {}) as Dispatch<
    SetStateAction<Record<string, { disabled: boolean; lastFetched: Date }>>
  >,
});

export const useDisabled = (keys: string[], to: string) => {
  const isDisabled = useIsDisabled(keys);

  if (isDisabled) {
    window.location.replace(to);
  }
};

export const useIsDisabled = (keys: string[]) => {
  const { disabledMap, setDisabledMap } = useContext(DisabledContext);
  const [isDisabled, setIsDisabled] = useState(false);

  useEffect(() => {
    const disabled = keys.map(async (key) => {
      if (
        key in disabledMap &&
        new Date().valueOf() - disabledMap[key].lastFetched.valueOf() <=
          5 * 60 * 1000
      ) {
        return disabledMap[key].disabled;
      }

      const endpoint = `/setting/global/${key}`;
      const response = await api(endpoint);
      const { value } = response;
      const disabled = value === "true";

      setDisabledMap((prev) => ({
        ...prev,
        [key]: {
          disabled,
          lastFetched: new Date(),
        },
      }));

      return disabled;
    });

    Promise.all(disabled).then((results) => {
      const isDisabled = results.some((result) => result === true);
      setIsDisabled(isDisabled);
    });
  }, []);

  return isDisabled;
};
