import { describe, it, expect } from 'vitest';
import { formatCurrency, formatDate } from './formatter';

describe('Formatter utilities', () => {
  describe('formatCurrency', () => {
    it('should format USD by default', () => {
      expect(formatCurrency(1000)).toBe('$1,000.00');
    });

    it('should format with specified currency', () => {
      expect(formatCurrency(1000, 'EUR')).toBe('€1,000.00');
    });
  });

  describe('formatDate', () => {
    it('should format date in US format', () => {
      const date = new Date('2023-01-15');
      expect(formatDate(date)).toMatch(/1\/15\/2023/);
    });
  });
});
