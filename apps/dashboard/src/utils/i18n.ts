import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import resources from 'virtual:i18next-loader'

i18n
    .use(initReactI18next)
    .init({
        resources,
        lng: localStorage.getItem('language') || 'tr',
        fallbackLng: 'en',

        interpolation: {
            escapeValue: false,
        },

        debug: import.meta.env.DEV,

        nsSeparator: ':',
        keySeparator: '.',
    })

export function changeLanguage(lng: string) {
    localStorage.setItem('language', lng)
    return i18n.changeLanguage(lng)
}

export function hasTranslation(key: string, ns?: string): boolean {
    return i18n.exists(key, { ns })
}

export default i18n
