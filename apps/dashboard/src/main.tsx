import { useAuthStore } from "@/stores/auth";
import { ModalContainer } from "@mass/shared/components/organisms/modal/index";
import { ModalProvider } from "@mass/shared/components/organisms/modal/provider";
import { toast } from "@mass/shared/hooks/index";
import { handleServerError } from "@mass/shared/utils/handle-server-error";
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { createRouter, RouterProvider } from "@tanstack/react-router";
import { AxiosError } from "axios";
import { StrictMode, useState } from "react";
import ReactDOM from "react-dom/client";
import { I18nextProvider } from "react-i18next";

import i18n from "@/utils/i18n";
import "./index.css";

import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";

import { routeTree } from "./routeTree.gen";
import { Title } from "./components/Title";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        if (import.meta.env.DEV) console.log({ failureCount, error });

        if (failureCount >= 0 && import.meta.env.DEV) return false;
        if (failureCount > 3 && import.meta.env.PROD) return false;

        return !(
          error instanceof AxiosError &&
          [401, 403].includes(error.response?.status ?? 0)
        );
      },
      refetchOnWindowFocus: import.meta.env.PROD,
      staleTime: 10 * 1000, // 10s
      gcTime: 30 * 60 * 1000,
    },
    mutations: {
      onError: (error) => {
        handleServerError(error);

        if (error instanceof AxiosError) {
          if (error.response?.status === 304) {
            toast({
              variant: "destructive",
              title: "Content not modified!",
            });
          }
        }
      },
    },
  },
  queryCache: new QueryCache({
    onError: (error) => {
      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          toast({
            variant: "destructive",
            title: "Session expired!",
          });
          useAuthStore.getState().auth.reset();
          router.navigate({
            to: "/logout",
          });
        }
        if (error.response?.status === 500) {
          toast({
            variant: "destructive",
            title: "Internal Server Error!",
          });
          router.navigate({
            to: "/$error",
            params: { error: "500" },
          });
        }
      }
    },
  }),
});

const router = createRouter({
  routeTree,
  context: { queryClient },
  defaultPreload: "intent",
  defaultPreloadStaleTime: 0,
});

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

const rootElement = document.getElementById("root")!;
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <StrictMode>
      <I18nextProvider i18n={i18n}>
        <ModalProvider>
          <QueryClientProvider client={queryClient}>
            <Title />
            <RouterProvider router={router} />
            <ModalContainer />
          </QueryClientProvider>
        </ModalProvider>
      </I18nextProvider>
    </StrictMode>
  );
}
