import React, { Suspense, lazy } from 'react'
import { createLazyFileRoute } from '@tanstack/react-router'

const pageMap: Record<
  string,
  () => Promise<{ default: React.ComponentType<any> }>
> = {
  login: () => import('@/features/auth/pages/Login'),
  '500': () => import('@/features/errors/general-error'),
}

const defaultLoader = pageMap['login']

function RouteComponent() {
  const { auth } = Route.useParams()
  const loader = pageMap[auth] || defaultLoader
  const Component = lazy(loader)

  return (
    <Suspense fallback={null}>
      <Component />
    </Suspense>
  )
}

export const Route = createLazyFileRoute('/(auth)/$auth')({
  component: RouteComponent,
})
