import { Suspense, lazy } from 'react'
import { createLazyFileRoute } from '@tanstack/react-router'

const SettingsNotificationsPage = lazy(() => import('@/features/settings/notifications'))

function NotificationRouteComponent() {
  return (
    <Suspense fallback={null}>
      <SettingsNotificationsPage />
    </Suspense>
  )
}

export const Route = createLazyFileRoute('/_authenticated/settings/notification')({
  component: NotificationRouteComponent
})