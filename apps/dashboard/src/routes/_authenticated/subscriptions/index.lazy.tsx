import React, { Suspense, lazy } from 'react'
import { createLazyFileRoute } from '@tanstack/react-router'

const SubscriptionsPage = lazy(() => import('@/features/subscriptions'))

function RouteComponent() {
  return (
    <Suspense fallback={null}>
      <SubscriptionsPage />
    </Suspense>
  )
}

export const Route = createLazyFileRoute('/_authenticated/subscriptions/')({
  component: RouteComponent,
})
