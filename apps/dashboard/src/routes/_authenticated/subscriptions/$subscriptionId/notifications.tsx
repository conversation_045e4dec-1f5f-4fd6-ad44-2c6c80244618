import { createFileRoute } from '@tanstack/react-router'
import { lazy } from 'react'

const SubscriptionDetail = lazy(() => import('@/features/subscriptions/pages/detail'))

export const Route = createFileRoute(
  '/_authenticated/subscriptions/$subscriptionId/notifications'
)({
  component: function SubscriptionsNotificationsPage() {
    const { subscriptionId } = Route.useParams()
    
    return (
        <SubscriptionDetail subscriptionId={subscriptionId} />
    )
  },
})