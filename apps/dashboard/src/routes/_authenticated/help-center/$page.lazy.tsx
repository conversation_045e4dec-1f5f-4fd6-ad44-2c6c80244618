import HelpCenterLayout from '@/features/help-center'
import { createLazyFileRoute } from '@tanstack/react-router'
import React, { Suspense, lazy } from 'react'

const pageMap: Record<string, () => Promise<{ default: React.ComponentType<any> }>> = {
  'faq': () => import('@/features/help-center/pages/faq'),
}

const defaultLoader = pageMap['faq']

function HelpCenterPage(props: { params?: { page?: string } }) {
  const pageKey = props.params?.page ?? 'faq'
  const loader = pageMap[pageKey] || defaultLoader
  const Component = lazy(loader)
  return (
    <Suspense fallback={null}>
      <HelpCenterLayout>
        <Component />
      </HelpCenterLayout>
    </Suspense>
  )
}

export const Route = createLazyFileRoute('/_authenticated/help-center/$page')({
  component: HelpCenterPage,
})