import { sidebarData } from "@/constants/sidebar-data";
import SkipToMain from "@mass/shared/components/atoms/skip-to-main";
import { AppSidebar } from "@mass/shared/components/organisms/layout/app-sidebar";
import { SidebarProvider } from "@mass/shared/components/ui/sidebar";
import { SearchProvider } from "@mass/shared/context/search-context";
import { cn } from "@mass/shared/lib/utils";
import { createFileRoute, Outlet } from "@tanstack/react-router";
import Cookies from "js-cookie";
import { useAuthStore } from "@/stores/auth";
import { useEffect, useState } from "react";
import { useMe } from "@/features/auth";
import api from "@/services/api";
import { DisabledContext } from "@/utils/use-disabled";

export const Route = createFileRoute("/_authenticated")({
  component: RouteComponent,
});

function RouteComponent() {
  const defaultOpen = Cookies.get("sidebar:state") !== "false";
  const { data: me } = useMe();
  const setUser = useAuthStore((state) => state.auth.setUser);

  useEffect(() => {
    if (me) {
      setUser({
        id: me.id ?? "",
        permissions: me.permissions ?? [],
        email: me.email ?? null,
        phone: me.phone ?? null,
        tckn: me.tckn ?? null,
        firstName: me.firstName ?? null,
        lastName: me.lastName ?? null,
      });
    }
  }, [me, setUser]);

  const [disabledMap, setDisabledMap] = useState({} as any); // todo: any

  return (
    <DisabledContext.Provider value={{ disabledMap, setDisabledMap }}>
      <SearchProvider>
        <SidebarProvider defaultOpen={defaultOpen}>
          <SkipToMain />
          <AppSidebar sidebarData={sidebarData} api={api} />
          <div
            id="content"
            className={cn(
              "ml-auto w-full max-w-full",
              "peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]",
              "peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]",
              "transition-[width] duration-200 ease-linear",
              "flex h-svh flex-col"
            )}
          >
            <Outlet />
          </div>
        </SidebarProvider>
      </SearchProvider>
    </DisabledContext.Provider>
  );
}
