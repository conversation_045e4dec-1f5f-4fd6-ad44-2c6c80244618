import React, { Suspense, lazy } from 'react';
import { createFileRoute } from '@tanstack/react-router';
import { Skeleton } from "@mass/shared/components/ui/skeleton";

const ComplaintsRequestsPage = lazy(() => import('@/features/complaints-requests'));

function RouteComponent() {
  return (
    <Suspense fallback={
      <div className="w-full h-full flex flex-col space-y-4 p-4">
        <Skeleton className="h-8 w-1/3" />
        <Skeleton className="h-4 w-1/4" />
        <Skeleton className="h-48 w-full" />
      </div>
    }>
      <ComplaintsRequestsPage />
    </Suspense>
  );
}

export const Route = createFileRoute('/_authenticated/complaints-requests/')({
  component: RouteComponent,
});
