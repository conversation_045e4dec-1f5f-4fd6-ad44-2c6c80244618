import React, { Suspense, lazy } from 'react';
import { createLazyFileRoute, useParams } from '@tanstack/react-router';
import { Skeleton } from "@mass/shared/components/ui/skeleton";

const ComplaintDetailPage = lazy(() => import('@/features/complaints-requests/pages/complaint-detail'));

function RouteComponent() {
  const params = useParams({ strict: false });
  const complaintId = params.complaintId;
  
  if (!complaintId) {
    return null;
  }
  
  return (
    <Suspense fallback={
      <div className="w-full h-full flex flex-col space-y-4 p-4">
        <Skeleton className="h-8 w-1/3" />
        <Skeleton className="h-4 w-1/4" />
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    }>
      <ComplaintDetailPage complaintId={complaintId} />
    </Suspense>
  );
}

export const Route = createLazyFileRoute('/_authenticated/complaints-requests/$complaintId')({
  component: RouteComponent,
})

