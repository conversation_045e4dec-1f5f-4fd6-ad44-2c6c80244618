import { createFileRoute, redirect } from '@tanstack/react-router'

export const Route = createFileRoute('/_authenticated/')({
    loader: () => {
        const cookies = document.cookie.split(';').map(cookie => cookie.trim());
        const hasSessionCookie = cookies.some(cookie => 
            cookie.startsWith('sess=') || cookie === 'sess');
        
        if (hasSessionCookie) {
            return redirect({
                to: '/subscriptions',
            });
        } else {
            return redirect({
                to: '/$auth',
                params: { auth: 'login' }
            });
        }
    },
})
