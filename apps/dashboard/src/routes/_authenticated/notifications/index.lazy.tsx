import React, { Suspense, lazy } from 'react'
import { createLazyFileRoute } from '@tanstack/react-router'

const NotificationsPage = lazy(() => import('@/features/notifications'))

function NotificationsRouteComponent() {
  return (
    <Suspense fallback={null}>
      <NotificationsPage />
    </Suspense>
  )
}

export const Route = createLazyFileRoute('/_authenticated/notifications/')({
  component: NotificationsRouteComponent
})
