import React, { Suspense, lazy } from 'react'
import { createLazyFileRoute } from '@tanstack/react-router'

const NotificationsPage = lazy(() => import('@/features/notifications'))

function ArchivedNotificationsRouteComponent() {
  return (
    <Suspense fallback={null}>
      <NotificationsPage isArchived />
    </Suspense>
  )
}

export const Route = createLazyFileRoute('/_authenticated/notifications/archived')({
  component: ArchivedNotificationsRouteComponent
})