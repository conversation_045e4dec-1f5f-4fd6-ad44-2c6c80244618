import { createLazyFileRoute } from '@tanstack/react-router'
import React, { Suspense, lazy } from 'react'

const errorMap: Record<string, () => Promise<{ default: React.ComponentType<any> }>> = {
  '401': () => import('@/features/errors/unauthorized-error'),
  '403': () => import('@/features/errors/forbidden'),
  '404': () => import('@/features/errors/not-found-error'),
  '500': () => import('@/features/errors/general-error'),
  '503': () => import('@/features/errors/maintenance-error'),
}

const defaultLoader = errorMap['404']

function ErrorPage(props: { params?: { error?: string } }) {
  const errorKey = props.params?.error ?? '404'
  const loader = errorMap[errorKey] || defaultLoader
  const Component = lazy(loader)
  return (
    <Suspense fallback={null}>
      <Component />
    </Suspense>
  )
}

export const Route = createLazyFileRoute('/(errors)/$error')({
  component: ErrorPage,
})
