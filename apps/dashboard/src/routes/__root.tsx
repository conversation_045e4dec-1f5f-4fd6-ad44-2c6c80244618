import type { QueryClient } from "@tanstack/react-query";
import { Toaster } from "@mass/shared/components/ui/sonner";
import GeneralError from "@/features/errors/general-error";
import NotFoundError from "@/features/errors/not-found-error";
import { createRootRouteWithContext, Outlet } from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/router-devtools";
import { MDXProvider } from "@mdx-js/react";
import { DisabledContext } from "@/utils/use-disabled";

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
}>()({
  component: () => {
    return (
      <>
        <MDXProvider>
          <Outlet />
          <Toaster />
          {import.meta.env.MODE === "development" && (
            <>
              <TanStackRouterDevtools position="bottom-right" />
            </>
          )}
        </MDXProvider>
      </>
    );
  },
  notFoundComponent: NotFoundError,
  errorComponent: GeneralError,
});
