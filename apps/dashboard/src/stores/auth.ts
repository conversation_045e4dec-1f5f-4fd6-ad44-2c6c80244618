import Cookies from 'js-cookie'
import { create } from 'zustand'

const ACCESS_TOKEN = 'sess'

export interface AuthUser {
    id: string
    permissions: { scope: string }[]
    email: string | null
    phone: string | null
    tckn: string | null
    firstName: string | null
    lastName: string | null
}

interface AuthState {
    auth: {
        user: AuthUser | null
        setUser: (user: AuthUser | null) => void
        accessToken: string
        setAccessToken: (accessToken: string) => void
        resetAccessToken: () => void
        reset: () => void
    }
}

export const useAuthStore = create<AuthState>()((set) => {
    const cookieState = Cookies.get(ACCESS_TOKEN)
    let initToken = '';
    if (cookieState) {
        try {
            initToken = JSON.parse(cookieState);
        } catch (e) {
            initToken = cookieState;
        }
    }

    return {
        auth: {
            user: null,
            setUser: user =>
                set(state => ({ ...state, auth: { ...state.auth, user } })),
            accessToken: initToken,
            setAccessToken: accessToken =>
                set((state) => {
                    Cookies.set(ACCESS_TOKEN, JSON.stringify(accessToken))
                    return { ...state, auth: { ...state.auth, accessToken } }
                }),
            resetAccessToken: () =>
                set((state) => {
                    Cookies.remove(ACCESS_TOKEN)
                    return { ...state, auth: { ...state.auth, accessToken: '' } }
                }),
            reset: () =>
                set((state) => {
                    Cookies.remove(ACCESS_TOKEN)
                    return {
                        ...state,
                        auth: { ...state.auth, user: null, accessToken: '' },
                    }
                }),
        },
    }
})

export const useAuth = () => useAuthStore(state => state.auth)
