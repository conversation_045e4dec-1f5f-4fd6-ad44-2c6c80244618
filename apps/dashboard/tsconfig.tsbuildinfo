{"root": ["./src/env.d.ts", "./src/routetree.gen.ts", "./src/constants/sidebar-data.ts", "./src/features/auth/index.ts", "./src/features/auth/hooks/use-auth.ts", "./src/features/auth/types/index.ts", "./src/features/complaints-requests/data/queries.ts", "./src/features/complaints-requests/data/schema.ts", "./src/features/notifications/components/use-notification-types.ts", "./src/features/notifications/data/queries.ts", "./src/features/settings/data/queries.ts", "./src/features/subscriptions/data/queries.ts", "./src/features/subscriptions/data/schema.ts", "./src/services/api.ts", "./src/services/enums.ts", "./src/services/types.ts", "./src/services/api/auth.ts", "./src/services/api/complaints.ts", "./src/services/api/notifications.ts", "./src/services/api/settings.ts", "./src/services/api/subscriptions.ts", "./src/stores/auth.ts", "./src/test/setup.ts", "./src/types/declarations.d.ts", "./src/types/mime-types.d.ts", "./src/types/module.d.ts", "./src/utils/formatter.test.ts", "./src/utils/formatter.ts", "./src/utils/i18n.ts", "./src/utils/use-disabled.ts", "./src/main.tsx", "./src/components/title.tsx", "./src/features/auth/components/agreement.tsx", "./src/features/auth/components/agreementwrapper.tsx", "./src/features/auth/components/authlayout.tsx", "./src/features/auth/components/loginform.tsx", "./src/features/auth/pages/login.tsx", "./src/features/complaints-requests/index.tsx", "./src/features/complaints-requests/components/columns.tsx", "./src/features/complaints-requests/components/document.tsx", "./src/features/complaints-requests/components/skeleton-loader.tsx", "./src/features/complaints-requests/components/status-badge.tsx", "./src/features/complaints-requests/components/modals/complaint-application.tsx", "./src/features/complaints-requests/components/modals/filter.tsx", "./src/features/complaints-requests/components/modals/reserve.tsx", "./src/features/complaints-requests/pages/complaint-detail.tsx", "./src/features/errors/forbidden.tsx", "./src/features/errors/general-error.tsx", "./src/features/errors/maintenance-error.tsx", "./src/features/errors/not-found-error.tsx", "./src/features/errors/unauthorized-error.tsx", "./src/features/help-center/index.tsx", "./src/features/help-center/pages/about.tsx", "./src/features/help-center/pages/faq.tsx", "./src/features/help-center/pages/use-cases.tsx", "./src/features/notifications/index.tsx", "./src/features/notifications/components/columns.tsx", "./src/features/notifications/components/sidebar-number.tsx", "./src/features/notifications/components/skeleton-loader.tsx", "./src/features/notifications/components/type-badge.tsx", "./src/features/notifications/components/modals/filter.tsx", "./src/features/notifications/components/modals/preview.tsx", "./src/features/settings/index.tsx", "./src/features/settings/notifications.tsx", "./src/features/settings/components/delete-account-modal.tsx", "./src/features/subscriptions/data.tsx", "./src/features/subscriptions/index.tsx", "./src/features/subscriptions/notification.tsx", "./src/features/subscriptions/outages.tsx", "./src/features/subscriptions/overview.tsx", "./src/features/subscriptions/components/columns.tsx", "./src/features/subscriptions/components/skeleton-loader.tsx", "./src/features/subscriptions/components/modals/delete.tsx", "./src/features/subscriptions/components/modals/edit.tsx", "./src/features/subscriptions/components/modals/export.tsx", "./src/features/subscriptions/components/modals/filter.tsx", "./src/features/subscriptions/components/modals/subscription-billing.tsx", "./src/features/subscriptions/pages/detail.tsx", "./src/routes/__root.tsx", "./src/routes/(auth)/$auth.lazy.tsx", "./src/routes/(errors)/$error.lazy.tsx", "./src/routes/_authenticated/index.tsx", "./src/routes/_authenticated/logout.tsx", "./src/routes/_authenticated/route.tsx", "./src/routes/_authenticated/complaints-requests/$complaintid.lazy.tsx", "./src/routes/_authenticated/complaints-requests/index.tsx", "./src/routes/_authenticated/complaints-requests/route.lazy.tsx", "./src/routes/_authenticated/help-center/$page.lazy.tsx", "./src/routes/_authenticated/notifications/archived.lazy.tsx", "./src/routes/_authenticated/notifications/index.lazy.tsx", "./src/routes/_authenticated/settings/index.lazy.tsx", "./src/routes/_authenticated/settings/notification.lazy.tsx", "./src/routes/_authenticated/subscriptions/index.lazy.tsx", "./src/routes/_authenticated/subscriptions/$subscriptionid/data.tsx", "./src/routes/_authenticated/subscriptions/$subscriptionid/index.tsx", "./src/routes/_authenticated/subscriptions/$subscriptionid/notifications.tsx", "./src/routes/_authenticated/subscriptions/$subscriptionid/outages.tsx", "./types/declarations.d.ts", "./types/module.d.ts", "../../shared/components/organisms/layout/document-list.tsx"], "version": "5.7.3"}