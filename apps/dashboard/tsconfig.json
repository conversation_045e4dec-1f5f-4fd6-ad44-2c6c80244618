{"references": [{"path": "../../shared/config/tsconfig.app.json"}, {"path": "../../shared/config/tsconfig.node.json"}], "files": [], "compilerOptions": {"strict": true, "strictNullChecks": true, "jsx": "react-jsx", "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "noEmit": true, "target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "downlevelIteration": true, "types": ["vite/client", "react", "react-dom", "node"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "types/**/*.d.ts", "../../shared/components/organisms/layout/document-list.tsx"]}